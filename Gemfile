source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

# Validate json columns with schemas
gem 'activerecord_json_validator'
# Assets synchronization
gem 'asset_sync'
gem 'bcrypt'
# Export to Excel files
gem 'caxlsx'
gem 'caxlsx_rails'
# Memcached client
gem 'dalli'
# Parser and evaluator for a mathematical and logical formula language
gem 'dentaku'
# Load ENV variables from config/application.yml
gem 'figaro'
# Google bucket storage (for asset_sync)
gem 'fog-core'
gem 'fog-google'
# Google bucket storage (for active_storage)
gem 'google-cloud-storage', require: false
# Use Active Storage variant
gem 'image_processing'
# Letter avatar
gem 'letter_avatar'
# Mailgun
gem 'mailgun-ruby'
# Use mysql as the database for Active Record
gem 'mysql2'
# Parse HMTL and XML
gem 'nokogiri'
# nested set models
gem 'awesome_nested_set'
# New Relic
gem 'newrelic_rpm'
# Authorization & Abilities
gem 'pundit'
# Single sign-on
gem 'omniauth-azure-activedirectory-v2'
gem 'omniauth-google-oauth2'
gem 'omniauth-rails_csrf_protection', '~> 1.0'
# Bundle edge Rails instead: gem 'rails', github: 'rails/rails', branch: 'main'
gem 'rails'
gem 'rails-i18n'
# Redis
gem 'redis'
gem 'hiredis-client'
# Apartment
gem 'ros-apartment', require: 'apartment'
gem 'ros-apartment-sidekiq', require: 'apartment-sidekiq'
# Sentry
gem 'sentry-ruby'
gem 'sentry-rails'
gem 'sentry-sidekiq'
gem 'sidekiq'
gem 'sidekiq-cron'
gem 'sidekiq-unique-jobs'
# thread safe, per-request storage
gem 'request_store'
gem 'request_store-sidekiq'
gem 'faraday-multipart'

# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'
gem 'jsonapi-serializer'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.4', require: false

gem 'rqrcode', '~> 3.1'

gem 'sprockets-rails'

gem 'mobility', '~> 1.3.2'

gem 'connection_pool', '>= 2.2.5'

gem 'poppler'

# AI
gem 'langchainrb'
# langchainrb requires tiktoken_ruby but doesn't declare it as a dependency
gem 'tiktoken_ruby'
gem 'ruby-openai'

group :development, :test do
  gem 'pry-rails'
  gem 'pry-byebug'
  # Code style
  gem 'rubocop', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
  gem 'rubocop-rspec_rails', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-factory_bot', require: false
  gem 'erb_lint', require: false
  # Code vulnerabilities
  gem 'brakeman'
  gem 'bundler-audit'
  # Query N+1's
  gem 'prosopite'
  # stub and spec network requests
  gem 'webmock'
  # Locale check
  gem 'i18n-tasks'
  gem 'faker'
  # Rspec
  gem 'factory_bot_rails'
  gem 'rspec_junit_formatter'
  gem 'rspec-rails'
  gem 'shoulda-matchers'
end

group :development do
  # Annotate models
  gem 'annotaterb'
  gem 'listen', '~> 3.9'
  # Use Puma as the app server
  gem 'puma', '~> 6.6'
  gem 'easy_translate', '~> 0.5.1'
  gem 'letter_opener'
end

group :test do
  # Coverage
  gem 'simplecov', require: false
  gem 'timecop'
  gem 'rails-controller-testing'
  gem 'mock_redis'
  gem 'roo'
end

group :production do
  gem 'passenger'
end
