/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS/SCSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

body {
  font-family: ulp-font, -apple-system, BlinkMacSystemFont, Roboto, Helvetica, sans-serif;
  font-weight: 300;
  margin: 0;
}

body * {
  box-sizing: border-box;
  font-family: inherit;
  font-weight: inherit;
}

h2 {
  font-weight: 400;
  margin-bottom: 0;
}

h4 {
  font-weight: 300;
  margin-top: 0;
}

.logo {
  width:200px;
  height:auto;
}

button, input {
  font-size: 16px;
}

input:not([disabled])[type="submit"], button {
  cursor: pointer;
}

main {
  background: white;
  border-radius: 10px;
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  box-shadow: 0 0 .3rem rgba(0,0,0,.15);

  .container {
    padding: 2em;
    width: 100%;
  }
}

main.with-side-image {
  width: calc(100% - 100px);
  height: calc(100% - 100px);
  border-radius: 20px;

  .side-image {
    width: calc(100% - 496px);
  }

  .container {
    width: 496px;
  }
}

.text-field {
  display: inline-block;
  margin-bottom: 1em;
  width: 100%;
  border-radius: 5px;
  border: 1px solid #C7CCD6;
  height: 2.5em;
  padding: 6px 20px 6px 20px;
}

.text-center {
  text-align: center;
}

.btn-primary {
  width: 100%;
  border-radius: 5px;
  background-color: rgb(3 69 90);
  border: none;
  color: white;
  height: 2.5em;
  margin-bottom: 1em;
}

.btn-primary:disabled {
  color: #fff;
  background-color: #a6adb4;
}

.divider {
  width: 100%;
  display: flex;
  text-align: center;
}

.divider-side{
  width: 46.665%;
  padding: 15px 0;
  color: #C7CCD6;
}

.divider-center{
  width: 6.67%;
  padding: 12px 0;
}

.btn-sso {
  width: 100%;
  border-radius: 5px;
  height: 2.5em;
  margin-bottom: 1em;
  border: 1px solid #C7CCD6;
  color: black;
  background-color: white;
}

.btn-sso img {
  width: 20px;
  display: inline-block;
  vertical-align: top;
  margin-right: 5px;
}

.mb-0 {
  margin-bottom: 0;
}

.action-link {
  color: black;
}

@media (max-width: 767.98px) {
  main {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  main.with-side-image {
    width: 100%;
    height: 100%;
    border-radius: 0;

    .side-image {
      display: none;
    }

    .container {
      width: 100%;
    }
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  main {
    width: 60%;
  }

  main.with-side-image {
    width: 60%;
    height: unset;
    border-radius: 10px;

    .side-image {
      display: none;
    }

    .container {
      width: 100%;
    }
  }
}
