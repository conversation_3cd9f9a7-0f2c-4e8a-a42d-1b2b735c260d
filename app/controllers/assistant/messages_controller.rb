class Assistant::MessagesController < ApplicationController
  def show
    message = Assistant::Message.find(params[:id])
    render json: Assistant::MessageSerializer.new(message, { include: [:job] })
  end

  def create
    chat_id = params.dig(:data, :relationships, :chat, :data, :id)
    chat = Assistant::Chat.find(chat_id)

    message = chat.messages.new(message_params.permit(:origin))
    message.content = permit_content(message_params)

    message.save!
    message.job = AssistantWorker.queue_with_job!(
      user: current_user,
      parameters: { message_id: message.id }
    )
    message.save

    render json: Assistant::MessageSerializer.new(message, { include: [:job] })
  end

  private

  def message_params
    params.dig(:data, :attributes)
  end

  def permit_content(message_param)
    content = message_param.fetch(:content) || []
    content.map do |part|
      if part[:type] == 'text'
        part.permit(:type, :data)
      elsif part[:type] == 'data'
        part.permit(:type, data: {})
      end
    end
  end
end
