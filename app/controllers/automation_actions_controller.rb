class AutomationActionsController < ApplicationController
  def show
    render json: AutomationActionSerializer.new(automation_action, options).serializable_hash
  end

  def create
    new_automation_action = AutomationAction::CreateService.execute(params)

    render json: AutomationActionSerializer.new(new_automation_action, options).serializable_hash, status: :created
  end

  def update
    updated_automation_action = AutomationAction::UpdateService.execute(automation_action, payload: params)

    render json: AutomationActionSerializer.new(updated_automation_action, options).serializable_hash
  end

  def destroy
    automation_action.destroy!

    head :no_content
  end

  private

  def automation_action
    includes = { automation_mail_action: [automation_mail_action_attributes: :object_type_attribute],
                 automation_ai_action: [:automation_ai_action_attributes],
                 automation_update_object_action: [:automation_update_object_action_attributes],
                 automation_webhook_action: [:automation_webhook_action_attributes] }
    @automation_action ||= AutomationAction.includes(includes).find(params[:id])
  end

  def options
    {
      include: [
        :automation_mail_action, :automation_static_data_action, :automation_update_object_action,
        :'automation_mail_action.automation_mail_action_attributes',
        :'automation_mail_action.automation_mail_action_address_attributes',
        :automation_ai_action, :'automation_ai_action.automation_ai_action_attributes',
        :'automation_update_object_action.automation_update_object_action_attributes',
        :'automation_webhook_action.automation_webhook_action_attributes',
        :'automation_mail_action.automation_mail_action_address_attributes'
      ]
    }
  end
end
