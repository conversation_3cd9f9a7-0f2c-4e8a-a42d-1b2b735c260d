class AutomationsController < ApplicationController
  def index
    automations = Automation.includes(automation_include).all

    automations = automations.where(object_type_id: object_type_filter) if object_type_filter

    render json: AutomationSerializer.new(automations, options).serializable_hash
  end

  def show
    render json: AutomationSerializer.new(automation, options).serializable_hash
  end

  def create
    new_automation = Automation::CreateService.execute(params)

    render json: AutomationSerializer.new(new_automation, options).serializable_hash, status: :created
  end

  def update
    automation_mail_action_includes = [:automation_mail_action_attributes, :selected_users, :selected_roles, :text_translations,
                                       :automation_mail_action_address_attributes]
    automation_includes = [{ frontend_events: :form_element },
                           { automation_trigger: [automation_trigger_attributes: :object_type_attribute],
                             automation_actions: [{ automation_mail_action: automation_mail_action_includes },
                                                  :automation_static_data_action,
                                                  { automation_ai_action: :automation_ai_action_attributes },
                                                  { automation_update_object_action: :automation_update_object_action_attributes },
                                                  { automation_webhook_action: :automation_webhook_action_attributes }] }]

    automation = Automation.includes(automation_includes).find(params[:id])
    updated_automation = Automation::UpdateService.execute(automation, params)

    render json: AutomationSerializer.new(updated_automation, options).serializable_hash
  end

  def automation_include
    [:frontend_events,
     { team: [:object_types, :ancestor_teams, :object_type_role_team_relationships, :interface] },
     { automation_trigger: [automation_trigger_attributes: :object_type_attribute],
       automation_actions: [{ automation_mail_action:
                           [:automation_mail_action_attributes, :selected_users, :selected_roles, :text_translations,
                            :automation_mail_action_address_attributes] },
                            :automation_static_data_action,
                            { automation_ai_action: :automation_ai_action_attributes },
                            { automation_update_object_action: :automation_update_object_action_attributes },
                            { automation_webhook_action: :automation_webhook_action_attributes }] }]
  end

  def destroy
    automation.destroy!

    head :no_content
  end

  def trigger
    return head :unprocessable_entity unless automation.active?

    automation_parameters = {}
    if params[:data].present?
      payload = params.permit(data: {}, included: [:id, :__guid__, :type, { relationships: {} }]).to_h
      object_type = Custom::ObjectType.find_by(id: payload.dig(:data, :relationships, :object_type, :data, :id))
      return head :unprocessable_entity if object_type.blank?

      field_set = FieldSet::JsonPayload.new(payload, object_type: object_type)
      automation_parameters[:field_set] = field_set.serializable_hash
    end

    if params.key?(:filter)
      permitted_rules = params.require(:filter).permit(
        :object_type_id,
        :team_id,
        rules: [
          :column_name,
          :operator,
          :rule_type,
          :sort_order,
          :value,
          :value2,
          :value3,
          :object_type_attribute_id,
          :relationship_attribute_id,
          :position
        ]
      )
      automation_parameters[:filter] = permitted_rules
    end

    automation_job = AutomationWorker.queue_with_job!(
      automation: automation,
      user: current_user,
      parameters: automation_parameters
    )
    render json: JobSerializer.new(automation_job).serializable_hash
  end

  private

  def object_type_filter
    params.dig(:filter, :object_type_id)
  end

  def automation
    @automation ||= Automation.includes(automation_include)
                              .find(params[:id])
  end

  def options
    {
      include: [
        :team,
        :frontend_events,
        :automation_trigger,
        :'automation_trigger.automation_trigger_attributes',
        :automation_actions,
        :'automation_actions.automation_mail_action',
        :'automation_actions.automation_static_data_action',
        :'automation_actions.automation_mail_action.automation_mail_action_attributes',
        :'automation_actions.automation_ai_action',
        :'automation_actions.automation_ai_action.automation_ai_action_attributes',
        :'automation_actions.automation_update_object_action',
        :'automation_actions.automation_update_object_action.automation_update_object_action_attributes',
        :'automation_actions.automation_webhook_action',
        :'automation_actions.automation_webhook_action.automation_webhook_action_attributes',
        :'automation_actions.automation_mail_action.automation_mail_action_address_attributes'
      ]
    }
  end
end
