module AuthorizationConcern
  extend ActiveSupport::Concern
  include Pundit::Authorization

  included do
    before_action :authorize_user!

    rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  end

  private

  def authorize_user!
    return if authenticated? && !@current_user&.disabled

    destroy_session! if @current_user&.disabled

    head :unauthorized
  end

  def user_not_authorized
    render_error([{ detail: I18n.t('errors.forbidden'), code: 'Forbidden' }], :forbidden)
  end
end
