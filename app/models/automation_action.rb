# == Schema Information
#
# Table name: automation_actions
#
#  id                     :bigint           not null, primary key
#  action_type            :integer          not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  automation_id          :bigint
#  object_type_variant_id :bigint
#  team_id                :bigint
#
class AutomationAction < ApplicationRecord
  include Variantable

  belongs_to :automation
  belongs_to :team, optional: true

  has_one :automation_mail_action, dependent: :destroy
  has_one :automation_static_data_action, dependent: :destroy
  has_one :automation_integration_action, dependent: :destroy
  has_one :automation_ai_action, dependent: :destroy
  has_one :automation_update_object_action, dependent: :destroy
  has_one :automation_webhook_action, dependent: :destroy
  belongs_to :object_type_variant, optional: true, class_name: 'Custom::ObjectTypeVariant'

  enum :action_type, { mail: 0, static_data: 1, integration: 2, ai: 3, update_object: 4, webhook: 5 }, scopes: false

  def mail_action?
    action_type&.to_sym == :mail && automation_mail_action.present?
  end

  def static_data_action?
    action_type&.to_sym == :static_data && automation_static_data_action.present?
  end

  def integration_action?
    action_type&.to_sym == :integration && automation_integration_action.present?
  end

  def ai_action?
    action_type&.to_sym == :ai && automation_ai_action.present?
  end

  def update_object_action?
    action_type&.to_sym == :update_object && automation_update_object_action.present?
  end

  def webhook_action?
    action_type&.to_sym == :webhook && automation_webhook_action.present?
  end
end
