# == Schema Information
#
# Table name: automation_webhook_actions
#
#  id                   :bigint           not null, primary key
#  headers              :json
#  url                  :string(255)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  automation_action_id :bigint
#
class AutomationWebhookAction < ApplicationRecord
  belongs_to :automation_action
  has_one :automation, through: :automation_action
  has_many :automation_webhook_action_attributes, dependent: :destroy

  validates :url, presence: true, format: { with: /\Ahttps:\/\/.*\z/ }
end
