# == Schema Information
#
# Table name: automation_webhook_action_attributes
#
#  id                           :bigint           not null, primary key
#  column_name                  :string(255)
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  automation_webhook_action_id :bigint
#  object_type_attribute_id     :bigint
#
class AutomationWebhookActionAttribute < ApplicationRecord
  USABLE_OBJECT_COLUMNS = %w[id created_at updated_at team.name user_id last_updated_by_id local_id].freeze

  belongs_to :automation_webhook_action
  belongs_to :object_type_attribute, class_name: 'Custom::ObjectTypeAttribute', optional: true

  validates :column_name, inclusion: { in: USABLE_OBJECT_COLUMNS }, allow_nil: true
  validate :column_xor_attribute

  private

  def column_xor_attribute
    return if column_name.present? ^ object_type_attribute_id.present?

    errors.add(:base, I18n.t(:'errors.invalid'))
  end
end
