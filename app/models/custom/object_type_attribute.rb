# == Schema Information
#
# Table name: object_type_attributes
#
#  id                              :bigint           not null, primary key
#  allow_variant_options           :boolean          default(FALSE), not null
#  allowed_values                  :json
#  archived                        :boolean          default(FALSE), not null
#  calculated                      :boolean          default(FALSE), not null
#  can_update                      :boolean          default(TRUE), not null
#  data_type                       :string(255)
#  default_value                   :json
#  field_identifier                :string(255)
#  hidden                          :json
#  inverse                         :boolean          default(FALSE), not null
#  inverse_relationship_kind       :string(255)
#  key                             :string(255)
#  locked                          :boolean          default(TRUE), not null
#  relationship_kind               :string(255)
#  searchable_for_recommendations  :boolean          default(FALSE), not null
#  show_colored_options            :boolean
#  target_class                    :string(255)
#  target_column_name              :string(255)
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  object_type_id                  :bigint
#  object_type_variant_id          :bigint
#  target_object_type_attribute_id :bigint
#  target_object_type_id           :bigint
#  target_team_id                  :bigint
#
class Custom::ObjectTypeAttribute < ApplicationRecord
  include Variantable
  extend Mobility

  ALLOWED_DATA_TYPES = %w[String Boolean Date DateTime Number HTML Relationship Attachment].freeze
  ALLOWED_RELATIONSHIP_KINDS = %w[single multiple].freeze
  JSON_HIDDEN_SCHEMA = Rails.root.join('app/models/schemas/hidden.json')

  translates :name

  belongs_to :object_type
  belongs_to :object_type_variant, optional: true
  belongs_to :target_object_type, optional: true, class_name: 'Custom::ObjectType'
  belongs_to :target_object_type_attribute, optional: true, class_name: 'Custom::ObjectTypeAttribute'
  belongs_to :target_team, optional: true, class_name: 'Team'

  has_many :object_type_roles, dependent: :nullify, inverse_of: :role_attribute, foreign_key: :role_attribute_id
  has_many :object_attributes, dependent: :destroy
  has_many :object_relationships, dependent: :destroy
  has_many :object_attachments, dependent: :destroy
  has_many :form_elements, dependent: :destroy
  has_many :object_query_rules, dependent: :destroy
  has_many :condition_rules, dependent: :destroy
  has_many :foreign_object_attribute_mappings, class_name: 'Integration::ForeignObject::AttributeMapping', dependent: :destroy
  has_many :object_type_attribute_selectable_values, dependent: :destroy
  has_many :automation_mail_action_attributes, dependent: :destroy
  has_many :automation_mail_action_address_attributes, dependent: :destroy
  has_many :automation_mail_action_relationship_address_attributes, dependent: :destroy,
                                                                    class_name: 'AutomationMailActionAddressAttribute',
                                                                    inverse_of: :relationship_object_type_attribute

  has_many :automation_static_data_actions, dependent: :nullify
  has_many :automation_trigger_attributes, dependent: :destroy
  has_many :visualisation_thumbnails, class_name: 'Thumbnail', dependent: :destroy, foreign_key: :visualisation_attribute_id,
                                      inverse_of: :visualisation_attribute
  has_many :background_thumbnails, class_name: 'Thumbnail', dependent: :nullify, foreign_key: :background_attribute_id,
                                   inverse_of: :background_attribute
  has_many :object_type_attribute_validations, dependent: :destroy
  has_many :other_object_type_attribute_validations,
           class_name: 'ObjectTypeAttributeValidation',
           foreign_key: :other_object_type_attribute_id,
           inverse_of: :other_object_type_attribute,
           dependent: :destroy

  has_many :object_type_relationships, dependent: :destroy
  has_many :inverse_object_type_attributes,
           through: :object_type_relationships
  has_many :inverse_object_type_relationships,
           class_name: 'Custom::ObjectTypeRelationship',
           dependent: :destroy,
           foreign_key: :inverse_object_type_attribute_id,
           inverse_of: :inverse_object_type_attribute
  has_many :linked_object_type_attributes,
           through: :inverse_object_type_relationships,
           source: :object_type_attribute

  has_one :object_type_owner, dependent: :nullify,
                              class_name: 'Custom::ObjectType',
                              foreign_key: :recommended_object_type_attribute_id,
                              inverse_of: :recommended_object_type_attribute

  has_one :inverse_title_object_type_attribute, class_name: 'Custom::ObjectType',
                                                dependent: :nullify,
                                                foreign_key: :title_object_type_attribute_id,
                                                inverse_of: :title_object_type_attribute

  has_one :calculation, dependent: :destroy
  has_many :data_object_type_attribute_paths, dependent: :destroy,
                                              class_name: 'ObjectTypeAttributePath',
                                              foreign_key: :data_attribute_id,
                                              inverse_of: :data_attribute
  has_many :thumbnail_attributes, dependent: :destroy
  has_many :automation_ai_action_attributes, dependent: :destroy
  has_many :automation_relationship_ai_action_attributes, dependent: :destroy,
                                                          class_name: 'AutomationAiActionAttribute',
                                                          foreign_key: :relationship_attribute_id,
                                                          inverse_of: :relationship_attribute
  has_many :automation_mail_actions, dependent: :destroy
  has_many :scheduled_automations, dependent: :nullify,
                                   class_name: 'Automation',
                                   foreign_key: :schedule_attribute_id,
                                   inverse_of: :schedule_attribute
  has_many :interface_element_record_pickers, dependent: :nullify, class_name: 'Interface::Element::RecordPicker'
  has_many :interface_element_fields, dependent: :nullify, class_name: 'Interface::Element::Field'
  has_many :filter_attribute_views, dependent: :nullify,
                                    class_name: 'View',
                                    foreign_key: :filter_attribute_id,
                                    inverse_of: :filter_attribute
  has_many :object_query_rules_depth1, dependent: :destroy, class_name: 'ObjectQueryRule', foreign_key: :relationship_attribute_id,
                                       inverse_of: :relationship_attribute
  has_many :object_query_rules_depth2, dependent: :destroy, class_name: 'ObjectQueryRule', foreign_key: :relationship_attribute2_id,
                                       inverse_of: :relationship_attribute2

  attribute :hidden, :json

  validates :name, length: { maximum: 255 }
  validates :key, presence: true, length: { maximum: 255 }, uniqueness: { scope: :object_type_id, case_sensitive: false }
  validates :field_identifier, presence: true, length: { maximum: 255 }, uniqueness: { scope: :object_type_id, conditions: lambda {
    where(archived: false)
  }, case_sensitive: false }, unless: :archived
  validates :data_type, presence: true, inclusion: { in: ALLOWED_DATA_TYPES }
  validates :relationship_kind, absence: true, if: -> { !relationship? }
  validates :relationship_kind, presence: true, inclusion: { in: ALLOWED_RELATIONSHIP_KINDS }, if: -> { relationship? }
  validates :inverse_relationship_kind, absence: true, if: -> { !relationship? }
  validates :inverse_relationship_kind, presence: true, inclusion: { in: ALLOWED_RELATIONSHIP_KINDS }, if: -> { relationship? }
  validate :target_column_name_xor_target_object_type_attribute, if: -> { relationship? }
  validates :target_object_type_attribute, absence: true, if: -> { target_class != 'Object' }
  validates :hidden, json: { schema: JSON_HIDDEN_SCHEMA }, unless: -> { hidden.nil? }

  after_update :update_filter_rules_for_relationship_kind_change, if: -> { saved_change_to_relationship_kind? && relationship? }
  after_destroy :destroy_dependent_object_type_attribute_paths
  after_save :recalculate, if: -> { calculated }

  def relationship?
    data_type == 'Relationship'
  end

  def user_relationship?
    relationship? && target_class == 'User'
  end

  def object_relationship?
    relationship? && target_class == 'Object'
  end

  def attachment?
    data_type == 'Attachment'
  end

  def data_attribute?
    %w[String Boolean Date DateTime Number HTML].include?(data_type)
  end

  def html?
    data_type == 'HTML'
  end

  def datetime?
    data_type == 'DateTime'
  end

  def boolean?
    data_type == 'Boolean'
  end

  def selectable_values?
    allowed_values&.dig('type') == 'Select'
  end

  def active_calculation?
    calculated && !archived?
  end

  def relationship_objects_for(object)
    if inverse?
      linked_attribute_ids = linked_object_type_attributes.pluck(:id)
      return object.model_object_relationships.filter_map do |obj_rel|
        obj_rel.object if linked_attribute_ids.include?(obj_rel.object_type_attribute_id)
      end.uniq
    end

    object.object_relationships.filter_map { |obj_rel| obj_rel.model if obj_rel.object_type_attribute_id == id }
  end

  def map_relationship_objects(objects)
    objects.map do |obj|
      {
        object_type_id: obj.try(:object_type_id),
        object_id: obj.id,
        value: if target_column_name
                 # map related models to target column
                 obj.try(target_column_name)
               else
                 # map related Custom::Objects to the target_object_type_attribute
                 ObjectAttributeValueFormatter.formatted_value_for(
                   obj.value_for(target_object_type_attribute.id),
                   target_object_type_attribute
                 )
               end
      }
    end
  end

  def validate_value_type!(value)
    return if value.nil?

    ObjectAttributeTypeValidator.new(self).validate(value)
  end

  def hidden_for?(variant)
    return false if locked
    return false unless variant

    hidden&.dig(variant.id.to_s).present?
  end

  def with_validations_for?(variant)
    object_type_attribute_validations.present? && part_of_variant?(variant) && !hidden_for?(variant)
  end

  def default_value_for_variant(variant = nil)
    default_value_for_variant = nil
    default_value&.find do |value|
      if value['object_type_variant_id'] == variant&.id
        default_value_for_variant = value
        true
      elsif value['object_type_variant_id'].nil?
        default_value_for_variant = value
        false
      end
    end
    default_value_for_variant
  end

  def recalculate
    object_type.objects.find_each do |object|
      Calculation::FormulaEvaluateService.execute(calculation, object, dry_run: false)
    end
  end

  private

  def target_column_name_xor_target_object_type_attribute
    return unless relationship?
    return if target_column_name.present? ^ target_object_type_attribute_id.present?

    errors.add(:base, I18n.t(:'errors.invalid'))
  end

  def destroy_dependent_object_type_attribute_paths
    ObjectTypeAttributePath.with_attribute_reference(self).destroy_all
  end

  def update_filter_rules_for_relationship_kind_change
    return unless relationship_kind == 'multiple'

    object_query_rules.where(rule_type: :filter, operator: 'equals').update_all(operator: 'contains')
    object_query_rules.where(rule_type: :filter, operator: 'not_equals').update_all(operator: 'not_contains')
  end
end
