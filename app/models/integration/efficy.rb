# == Schema Information
#
# Table name: integrations
#
#  id         :bigint           not null, primary key
#  options    :json
#  type       :string(255)      not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  webhook_id :string(255)
#
class Integration::Efficy < Integration
  TABLE_KEYS = {
    22000 => :opportunity,
    30022 => :product
  }.freeze

  attribute :options, SettingsType.new(as: Settings::Integration::Efficy), default: -> { Settings::Integration::Efficy.new }

  # Efficy webhook example:
  # {
  #   "database": "kopf",
  #   "version": "12.0.104",
  #   "records": [
  #     {
  #       "table": 22000,
  #       "key": "10414",
  #       "operation": "Update",
  #       "user": "HOUSTTEST",
  #       "subject": " AMAZON / 64 /Tero H17/ 05.07.23",
  #       "fields": {
  #         "NAME": " AMAZON / 64 /Tero H17/ 05.07.23"
  #       },
  #       "date": "2024-08-26T09:13:07.065Z"
  #     }
  #   ]
  # }
  def handle_external_event(events)
    events[:records].each do |event|
      efficy_type = TABLE_KEYS[event[:table]]
      next if efficy_type.blank?

      event_handler = efficy_object_handler(efficy_type)
      event_handler.handle_external_event(event)
    end
  end

  def efficy_client
    ::Efficy::Client.new(options.url, options.token, options.session)
  end

  def mappings_for_efficy_type(efficy_type)
    foreign_object_mappings.where(foreign_object_type: efficy_type)
  end

  private

  def efficy_object_handler(efficy_type)
    "Integration::Efficy::#{efficy_type.capitalize}::Handler".constantize.new(self)
  end
end
