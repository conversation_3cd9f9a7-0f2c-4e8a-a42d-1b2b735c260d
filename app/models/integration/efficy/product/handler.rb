class Integration::Efficy::Product::Handler
  SUPPORTED_EVENTS = { incoming: %w[create update delete], outgoing: %w[] }.freeze

  def self.supports_event?(event, type = :incoming)
    SUPPORTED_EVENTS[type]&.include?(event)
  end

  def initialize(integration)
    @integration = integration
  end

  attr_reader :integration

  def handle_external_event(event)
    action = event[:operation].downcase
    return unless self.class.supports_event?(action)

    mapping = integration.mappings_for_efficy_type(:opportunity).first
    composite_key = event[:key].split(',')
    return unless composite_key.size == 3

    opportunity_id = Integer(composite_key[1], exception: false)
    return if opportunity_id.blank?

    opportunity_event = {
      'table' => Integration::Efficy::TABLE_KEYS.key(:opportunity),
      'key' => opportunity_id.to_s,
      'operation' => 'Update',
      'user' => event[:user],
      'subject' => '',
      'fields' => {},
      'date' => event[:date]
    }

    Integration::Efficy::OpportunitySyncWorker.perform_async(
      {
        'opportunity_id' => opportunity_id.to_s,
        'event' => opportunity_event.to_json,
        'mapping_id' => mapping.id
      }
    )
  end
end
