class CurrentInstanceSerializer < ApplicationSerializer
  set_type :current_instance

  attribute :company_name do |object|
    object.settings.company_name || object.subdomain
  end

  attribute :hide_recent_objects_section do |object|
    return true if object.settings.hide_recent_objects_section.nil?

    object.settings.hide_recent_objects_section
  end

  attribute :hide_team_field_on_forms do |object|
    object.settings.hide_team_field_on_forms
  end

  attribute :display_avatar_with_company_logo do |object|
    object.settings.display_avatar_with_company_logo
  end

  attribute :logo, if: proc { |object|
    object.logo.attached?
  } do |object|
    url_helpers = Rails.application.routes.url_helpers

    {
      default: url_helpers.rails_representation_path(object.logo.variant(resize_to_limit: [200, 40]), only_path: true)
    }
  end
end
