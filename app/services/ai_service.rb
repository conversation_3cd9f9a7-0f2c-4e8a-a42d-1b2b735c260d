require 'langchain'

class AiService < ApplicationService
  SYSTEM_MESSAGE_DEFAULT = 'You are an assistant who helps users fill in a form. Use a professional tone in your answers.'.freeze

  def initialize(automation_ai_action, field_set, job_id)
    @automation_ai_action = automation_ai_action
    @field_set = field_set
    @job_id = job_id
    @llm = initialize_llm
    @token_usage = initialize_token_usage
  end

  def execute
    suggest_fields
  end

  private

  attr_reader :automation_ai_action, :field_set, :job_id, :llm, :token_usage

  def suggest_fields
    tools = build_tools
    messages = build_system_message

    messages = load_record_into_chat(messages, tools)
    fields = suggest_fields_and_extract_from_chat(messages, tools)

    FieldSet::KeyHash.new(
      { attributes: fields },
      object_type: object_type
    )
  ensure
    token_usage.save!
  end

  def build_tools
    output_params = build_output_params
    [
      {
        type: 'function',
        function: {
          name: 'get_record',
          description: 'Get the current record'
        }
      },
      {
        type: 'function',
        function: {
          name: 'save_result',
          description: 'Save the predicted fields of the record',
          parameters: {
            type: :object,
            properties: output_params,
            required: output_params.keys
          }
        }
      }
    ]
  end

  def build_output_params
    output_params = {}
    automation_ai_action.automation_ai_action_attributes.each do |attribute|
      next unless attribute.param_type == 'output'

      param_description = attribute.description || attribute.object_type_attribute.name
      if attribute.object_type_attribute.selectable_values?
        options = attribute.object_type_attribute
                           .object_type_attribute_selectable_values
                           .where(archived: false)
                           .part_of_variant(object_type_variant)
                           .includes(:string_translations)
                           .filter { |value| !value.hidden_for?(object_type_variant) }

        param_options = options.to_h do |option|
          [
            option.id.to_s,
            {
              description: option.text,
              type: :boolean
            }
          ]
        end

        param = {
          description: param_description,
          properties: param_options,
          required: param_options.keys,
          type: :object
        }
      else
        param = {
          description: add_language_to_string(param_description),
          type: :string
        }
      end

      output_params[attribute.object_type_attribute.key] = param
    end

    output_params
  end

  def add_language_to_string(string = nil)
    language_mapping = {
      en: 'English',
      fr: 'French',
      nl: 'Dutch',
      es: 'Spanish'
    }
    lang_string = "Only answer in #{language_mapping[I18n.locale]}."
    return lang_string if string.nil?

    if string.rstrip.end_with?('.', '!', '?')
      "#{string} #{lang_string}"
    else
      "#{string}. #{lang_string}"
    end
  end

  def build_system_message
    messages = []
    messages << { role: 'system', content: automation_ai_action.system_message.presence || SYSTEM_MESSAGE_DEFAULT }
    messages.push({ role: 'system', content: add_language_to_string })
  end

  def load_record_into_chat(messages, tools)
    messages << { role: 'user', content: 'Get the record' }
    chat = llm.chat(messages: messages, tools: tools, tool_choice: { type: 'function', function: { name: 'get_record' } })
    update_token_usage(chat)
    update_messages_with_tool_calls(messages, chat)
  end

  def suggest_fields_and_extract_from_chat(messages, tools)
    messages << { role: 'user', content: 'Save the result' }
    chat = llm.chat(messages: messages, tools: tools, tool_choice: { type: 'function', function: { name: 'save_result' } })
    update_token_usage(chat)
    result = execute_tool_call(chat.tool_calls.first, json_schema: tools[1][:function][:parameters])
    map_or_reject_selectable_values(result)
  end

  def map_or_reject_selectable_values(result)
    automation_ai_action.automation_ai_action_attributes.each do |attribute|
      next unless attribute.param_type == 'output'
      next unless attribute.object_type_attribute.selectable_values?

      answer_object = result[attribute.object_type_attribute.key]
      answer_id = Integer(answer_object&.key(true), exception: false)
      answer = answer_id && attribute.object_type_attribute.object_type_attribute_selectable_values.find do |value|
        value.id == answer_id
      end

      if answer.present?
        result[attribute.object_type_attribute.key] = answer.value
      else
        result.delete(attribute.object_type_attribute.key)
      end
    end
    result
  end

  def execute_tool_call(tool_call, json_schema = nil)
    case tool_call['function']['name']
    when 'get_record'
      return load_data
    when 'save_result'
      return save_result(json_schema, tool_call['function']['arguments'])
    end
    nil
  end

  def load_data
    attributes = automation_ai_action.automation_ai_action_attributes
                                     .filter { |attr| attr.param_type == 'input' }
                                     .group_by(&:relationship_attribute_id)

    relationship_attributes = Custom::ObjectTypeAttribute.where(id: attributes.keys)
    data = attributes.each_with_object({}) do |relational_attributes, hash|
      if relational_attributes.first.nil?
        mapped_attribute_values(relational_attributes.second, hash)
      else
        mapped_relational_attribute_values(
          relationship_attributes.find { |attr| attr.id = relational_attributes.first },
          relational_attributes.second,
          hash
        )
      end
    end

    data.empty? ? nil : data
  end

  def mapped_attribute_values(ai_attributes, hash)
    ai_attributes.each do |ai_attribute|
      object_value = field_set.attribute(ai_attribute.object_type_attribute_id)
      hash[ai_attribute.object_type_attribute.key] = mapped_attribute_value(ai_attribute, object_value) if object_value
    end
  end

  def mapped_relational_attribute_values(relation_attribute, ai_attributes, hash)
    indexed_ai_attributes = ai_attributes.index_by(&:object_type_attribute_id)
    object_ids = NPlusOne.ignore { field_set.relationship(relation_attribute.id) }
    relationship_object_attributes = Custom::ObjectAttribute.where(
      object_id: object_ids,
      object_type_attribute: ai_attributes.map(&:object_type_attribute_id)
    ).group_by(&:object_id)

    mapped_objects = {}
    relationship_object_attributes.each do |object_id, attributes|
      mapped_object_attributes = {}
      attributes.each do |object_attribute|
        next unless object_attribute.value

        ai_attribute = indexed_ai_attributes[object_attribute.object_type_attribute_id]
        mapped_object_attributes[ai_attribute.object_type_attribute.key] = mapped_attribute_value(ai_attribute, object_attribute.value)
      end
      mapped_objects[object_id] = mapped_object_attributes if mapped_object_attributes.present?
    end
    hash[relation_attribute.key] = mapped_objects
  end

  def mapped_attribute_value(attribute, object_value)
    ota = attribute.object_type_attribute

    if ota.selectable_values?
      selectable_value = ota.object_type_attribute_selectable_values.find { |sv| sv.value == object_value }
      object_value = selectable_value.text if selectable_value
    end

    { value: object_value, description: attribute.description || ota.name }
  end

  def save_result(json_schema, result)
    parser = Langchain::OutputParsers::StructuredOutputParser.from_json_schema(json_schema)
    parser.parse(result)
  end

  def update_messages_with_tool_calls(messages, chat)
    messages << { role: chat.role, tool_calls: chat.tool_calls }
    messages << { role: 'tool', tool_call_id: chat.tool_calls.first['id'], content: execute_tool_call(chat.tool_calls.first).to_json }
    messages
  end

  def initialize_llm
    Langchain::LLM::OpenAI.new(
      api_key: Instance.current.settings.openai_key,
      default_options: {
        chat_model: 'gpt-4o-mini',
        temperature: 0.0
      }
    )
  end

  def initialize_token_usage
    obfuscated_api_key = "#{Instance.current.settings.openai_key[0..2]}****#{Instance.current.settings.openai_key[-4..]}"
    Analytics::AiTokenUsage.new(
      api_key: obfuscated_api_key,
      input_tokens: 0,
      output_tokens: 0,
      automation_ai_action_id: automation_ai_action.id,
      ai_model: 'gpt-4o-mini',
      job_id: job_id
    )
  end

  def update_token_usage(chat)
    token_usage.input_tokens += chat.prompt_tokens
    token_usage.output_tokens += chat.completion_tokens
    token_usage.ai_model = chat.model
  end

  def object_type
    @object_type ||= Custom::ObjectType.find(field_set.object_type_id)
  end

  def object_type_variant
    return @object_type_variant if defined?(@object_type_variant)

    team = Team.find_by(id: field_set.team_id)
    @object_type_variant = nil
    @object_type_variant = Custom::ObjectTypeVariant.for_hierarchical_team(team, object_type) if team
  end
end
