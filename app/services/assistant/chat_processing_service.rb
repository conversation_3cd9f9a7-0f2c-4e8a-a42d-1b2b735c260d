module Assistant
  class ChatProcessingService < ApplicationService
    def initialize(chat)
      @chat = chat
    end

    attr_reader :chat

    def execute
      assistant = build_assistant
      add_messages(assistant)
      get_response(assistant)
    end

    def build_assistant
      prompt = Assistant::Prompts.build_assistant_prompt(
        instance_context: Instance.current.settings.custom_assistant_system_message,
        chat_context: Assistant::Prompts.build_chat_context(chat),
        locale: I18n.locale
      )
      tools = Assistant::Tools.build_tools_for_chat(chat)
      Assistant::Util.build_assistant(
        api_key: Instance.current.settings.openai_key,
        prompt: prompt,
        tools: tools
      )
    end

    def add_messages(assistant)
      # TODO: better message ordering
      chat.messages.each do |message|
        add_message(assistant, message)
      end
    end

    def add_message(assistant, message)
      message.content.each do |part|
        part = part.with_indifferent_access
        data = part[:data]
        case part[:type]
        when 'text'
          assistant.add_message(
            role: message.origin,
            content: data
          )
        when 'tool'
          assistant.add_message(
            role: 'assistant',
            tool_calls: [
              {
                id: data[:tool_call_id],
                type: 'function',
                function: {
                  name: data[:tool_name],
                  arguments: data[:arguments].is_a?(String) ? data[:arguments] : data[:arguments].to_json
                }
              }
            ]
          )
          assistant.submit_tool_output(
            output: data.dig(:output, :content),
            tool_call_id: data[:tool_call_id]
          )
        end
      end
    end

    def get_response(assistant)
      run_finished = false
      response_message = chat.messages.new(origin: 'assistant')
      until run_finished
        Assistant::Util.verify_token_count!(assistant)
        assistant.run(auto_tool_execution: false)
        run_finished = process_response(assistant, response_message)
      end
      response_message.save!
      response_message
    end

    def process_response(assistant, response_message)
      response = assistant.messages.last
      Rails.logger.debug(response.content.presence || response.tool_calls)

      if response.tool_calls.any?
        run_tools(assistant, response.tool_calls, response_message)
        false
      else
        response_message.add_content(
          { type: 'text', data: response.content }
        )
        true
      end
    end

    def run_tools(assistant, tool_calls, response_message)
      tool_calls.each do |tool_call|
        tool_call = Assistant::Tools.run_tool(assistant, tool_call)

        assistant.submit_tool_output(
          output: tool_call[:output].content,
          tool_call_id: tool_call[:tool_call_id]
        )

        response_message.add_content(
          {
            type: 'tool',
            data: {
              tool_name: tool_call[:tool_name],
              tool_call_id: tool_call[:tool_call_id],
              arguments: tool_call[:arguments],
              output: {
                content: tool_call[:output].content,
                data: tool_call[:output].data? ? tool_call[:output].data : nil
              }
            }
          }
        )
        next unless tool_call[:output].action_suggestion?

        response_message.add_content(
          {
            type: 'action_suggestion',
            data: tool_call[:output].data
          }
        )
      end
    end
  end
end
