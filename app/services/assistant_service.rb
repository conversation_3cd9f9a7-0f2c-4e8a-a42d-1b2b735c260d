require 'langchain'

class AssistantService < ApplicationService
  def initialize(chat_prompt, job_id = nil)
    @chat_prompt = chat_prompt
    @job_id = job_id
    @llm = initialize_llm
    @token_usage = initialize_token_usage
  end

  SYSTEM_MESSAGE_DEFAULT = <<~PROMPT.freeze
    You are <PERSON>, the intelligent and supportive AI assistant for our B2B SaaS no-code builder platform.
    Your primary role is to analyze data and provide clear, concise, and actionable insights based on that analysis.
    You are exclusively focused on data analysis and are not to perform any calculations or numerical computations.

    Guidelines:

    - Data Analysis Focus: You are used solely to analyze data. Provide interpretations, insights, and recommendations based on the data presented.
    - No Calculations: Do not provide answers that involve any form of calculations or numerical computations. Emphasize conceptual understanding and data analysis.
    - Clarity & Simplicity: Use plain language and step-by-step guidance where necessary. Avoid jargon unless it's clearly explained.
    - Conciseness: Keep responses focused and to the point, ensuring users can quickly grasp the insights provided.
    - Professional Tone: Maintain a friendly yet professional demeanor that reflects our commitment to excellence in B2B SaaS solutions.

    Remember, you are <PERSON>, and your mission is to help users succeed with our no-code builder by offering expert data analysis and intuitive support—without resorting to calculations in your responses.
  PROMPT

  def execute
    suggest_fields
  end

  private

  attr_reader :chat_prompt, :llm, :token_usage, :job_id

  def suggest_fields
    tools = build_tools
    messages = build_system_message

    messages = load_record_into_chat(messages, tools)
    ask_question_and_get_response(messages)
  ensure
    token_usage.save!
  end

  def build_tools
    [
      {
        type: 'function',
        function: {
          name: 'get_record',
          description: 'Get the current record'
        }
      }
    ]
  end

  def add_language_to_string
    language_mapping = {
      en: 'English',
      fr: 'French',
      nl: 'Dutch',
      es: 'Spanish'
    }
    "Only answer in #{language_mapping[I18n.locale]}."
  end

  def build_system_message
    messages = []
    messages << { role: 'system', content: SYSTEM_MESSAGE_DEFAULT }
    if Instance.current.settings.custom_assistant_system_message.present?
      messages << { role: 'system', content: Instance.current.settings.custom_assistant_system_message }
    end
    messages << { role: 'system', content: add_language_to_string }
  end

  def load_record_into_chat(messages, tools)
    messages << { role: 'user', content: 'Get the record' }
    chat = llm.chat(messages: messages, tools: tools, tool_choice: { type: 'function', function: { name: 'get_record' } })
    update_token_usage(chat)
    update_messages_with_tool_calls(messages, chat)
  end

  def ask_question_and_get_response(messages)
    messages << { role: 'user', content: chat_prompt.question }
    chat = llm.chat(messages: messages)
    update_token_usage(chat)
    chat.completion
  end

  def execute_tool_call(tool_call)
    case tool_call['function']['name']
    when 'get_record'
      return load_data
    end
    nil
  end

  def load_data
    chat_prompt.data
  end

  def update_messages_with_tool_calls(messages, chat)
    messages << { role: chat.role, tool_calls: chat.tool_calls }
    messages << { role: 'tool', tool_call_id: chat.tool_calls.first['id'], content: execute_tool_call(chat.tool_calls.first).to_json }
    messages
  end

  def initialize_llm
    Langchain::LLM::OpenAI.new(
      api_key: Instance.current.settings.openai_key,
      default_options: {
        chat_model: 'gpt-4o-mini',
        temperature: 0.0
      }
    )
  end

  def initialize_token_usage
    obfuscated_api_key = "#{Instance.current.settings.openai_key[0..2]}****#{Instance.current.settings.openai_key[-4..]}"
    Analytics::AiTokenUsage.new(
      api_key: obfuscated_api_key,
      input_tokens: 0,
      output_tokens: 0,
      automation_ai_action_id: nil,
      ai_model: 'gpt-4o-mini',
      job_id: job_id
    )
  end

  def update_token_usage(chat)
    token_usage.input_tokens += chat.prompt_tokens
    token_usage.output_tokens += chat.completion_tokens
    token_usage.ai_model = chat.model
  end
end
