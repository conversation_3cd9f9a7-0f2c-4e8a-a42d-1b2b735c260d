class Automation::HandlerService < ApplicationService
  def initialize(automation, field_set: nil, change_set: nil, filter: nil, user: nil, job_id: nil)
    @automation = automation
    @field_set = field_set
    @change_set = change_set
    @filter = filter
    @user = user
    @job_id = job_id
  end

  # rubocop:disable Metrics/PerceivedComplexity
  def execute
    automation_actions = automation.automation_actions
                                   .where(team_id: [nil, object_team_id]).part_of_variant(object_type_variant)
                                   .order(created_at: :asc)
                                   .includes(:automation_mail_action, :automation_static_data_action, :automation_ai_action)

    automation_result = AutomationResult.new
    automation_actions.each do |action|
      result = if action.mail_action?
                 AutomationMailActionWorker.schedule_for(
                   action.automation_mail_action,
                   object,
                   user,
                   change_set
                 )
               elsif action.static_data_action?
                 AutomationStaticDataAction::HandlerService.execute(action.automation_static_data_action, field_set: field_set)
               elsif action.integration_action?
                 execute_integration_action(action)
               elsif action.ai_action?
                 AutomationAiAction::HandlerService.execute(action.automation_ai_action, field_set, user, job_id)
               elsif action.update_object_action?
                 AutomationUpdateObjectAction::HandlerService.execute(action.automation_update_object_action,
                                                                      object: object,
                                                                      user: user)
               elsif action.webhook_action?
                 schedule_webhook_action(action)
               end

      automation_result.add_action_result(result) if result.instance_of?(AutomationActionResult)
    end

    if automation_result.object_did_change?
      Custom::Object::EventHandlerService.execute(
        object,
        'update',
        user: user,
        change_set: automation_result.change_set,
        handler_options: {
          automations: false
        }
      )
    end

    automation_result.field_set
  end
  # rubocop:enable Metrics/PerceivedComplexity

  private

  attr_reader :automation, :field_set, :change_set, :filter, :user, :job_id

  def object
    @object ||= field_set.try(:object) || Custom::Object.find(field_set.property(:id)) if field_set.present?
  end

  def object_team_id
    field_set&.team_id
  end

  def object_type_id
    field_set&.object_type_id
  end

  def object_type_variant
    return @object_type_variant if defined?(@object_type_variant)

    @object_type_variant = Custom::ObjectTypeVariant.for_hierarchical_team(Team.find(object_team_id), object_type_id) if object_team_id
  end

  def execute_integration_action(action)
    AutomationIntegrationAction::HandlerService.execute(
      action.automation_integration_action,
      field_set: field_set,
      change_set: change_set,
      filter: filter,
      job_id: job_id,
      user: user
    )
  end

  def schedule_webhook_action(action)
    AutomationWebhookActionWorker.schedule_for(
      action.automation_webhook_action,
      object,
      automation.automation_trigger.trigger_type,
      user,
      object.updated_at
    )
  end
end
