class AutomationAction::CreateService < ApplicationService
  include FindRelationshipsInIncluded

  SUBTYPE_MODEL_NAMES = {
    mail: :automation_mail_action,
    static_data: :automation_static_data_action,
    ai: :automation_ai_action,
    update_object: :automation_update_object_action,
    webhook: :automation_webhook_action
  }.freeze

  def initialize(payload)
    @payload = payload
  end

  def execute
    automation = Automation.find(payload[:data][:relationships][:automation][:data][:id])
    automation_action = automation.automation_actions.build(automation_action_params)

    ActiveRecord::Base.transaction do
      automation_action.save!

      assign_action(automation_action)
    end

    automation_action
  end

  private

  def automation_action_params
    payload.require(:data)
           .fetch(:attributes, {})
           .permit(:action_type, :object_type_variant_id)
  end

  attr_reader :payload

  def assign_action(automation_action)
    relationship_param = relationship_param(automation_action)
    return if relationship_param.nil?

    case relationship_param[:type]
    when 'automation_mail_action'
      AutomationMailAction::UpdateService.execute(AutomationMailAction.new(automation_action: automation_action),
                                                  payload: relationship_param.merge(included: payload[:included]))
    when 'automation_static_data_action'
      automation_static_data_action = AutomationStaticDataAction.new(relationship_param.fetch(:attributes, {}).permit(data: {}))
      automation_static_data_action.automation_action = automation_action
      automation_static_data_action.object_type_attribute = Custom::ObjectTypeAttribute.find(
        relationship_param.dig(:relationships, :object_type_attribute, :data, :id)
      )
      automation_static_data_action.save!
    when 'automation_ai_action'
      AutomationAiAction::UpdateService.execute(AutomationAiAction.new(automation_action: automation_action),
                                                payload: relationship_param.merge(included: payload[:included]))
    when 'automation_update_object_action'
      AutomationUpdateObjectAction::UpdateService.execute(AutomationUpdateObjectAction.new(automation_action: automation_action),
                                                          payload: relationship_param.merge(included: payload[:included]))
    when 'automation_webhook_action'
      AutomationWebhookAction::UpdateService.execute(AutomationWebhookAction.new(automation_action: automation_action),
                                                     payload: relationship_param.merge(included: payload[:included]))
    end
  end

  def action_type(automation_action)
    SUBTYPE_MODEL_NAMES[automation_action.action_type.to_sym]
  end

  def relationship_param(automation_action)
    data = payload.dig(:data, :relationships, action_type(automation_action), :data)
    find_relationships_in_included(data, payload[:included]) unless data.nil?
  end
end
