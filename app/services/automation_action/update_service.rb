class AutomationAction::UpdateService < ApplicationService
  include FindRelationshipsInIncluded

  def initialize(automation_action, payload:)
    @automation_action = automation_action
    @payload = payload
  end

  def execute
    ActiveRecord::Base.transaction do
      update_action
    end

    automation_action
  end

  private

  attr_reader :automation_action, :payload

  def update_action
    return if relationship_param.nil?

    case relationship_param[:type]
    when 'automation_mail_action'
      automation_action.automation_mail_action = AutomationMailAction::UpdateService.execute(
        automation_action.automation_mail_action,
        payload: relationship_param.merge(included: payload[:included])
      )
    when 'automation_static_data_action'
      automation_action.automation_static_data_action.update!(relationship_param.fetch(:attributes, {}).permit(data: {}))
    when 'automation_ai_action'
      automation_action.automation_ai_action = AutomationAiAction::UpdateService.execute(
        automation_action.automation_ai_action,
        payload: relationship_param.merge(included: payload[:included])
      )
    when 'automation_update_object_action'
      automation_action.automation_update_object_action = AutomationUpdateObjectAction::UpdateService.execute(
        automation_action.automation_update_object_action,
        payload: relationship_param.merge(included: payload[:included])
      )
    when 'automation_webhook_action'
      automation_action.automation_webhook_action = AutomationWebhookAction::UpdateService.execute(
        automation_action.automation_webhook_action,
        payload: relationship_param.merge(included: payload[:included])
      )
    end
  end

  def action_type
    AutomationAction::CreateService::SUBTYPE_MODEL_NAMES[automation_action.action_type.to_sym]
  end

  def relationship_param
    @relationship_param ||= begin
      data = payload.dig(:data, :relationships, action_type, :data)
      find_relationships_in_included(data, payload[:included]) unless data.nil?
    end
  end
end
