class AutomationWebhookAction::HandlerService < ApplicationService
  TIMEOUT = 5

  PROPERTY_KEY_RENAMES = {
    'team.name': :team,
    last_updated_by_id: :last_updated_by,
    user_id: :created_by
  }.freeze

  def initialize(action, object:, event:, timestamp:, user: nil)
    @action = action
    @object = object
    @user = user
    @event = event
    @timestamp = timestamp
  end

  def execute
    return if object.nil?

    connection = Faraday.new do |con|
      con.options.timeout = TIMEOUT
      con.adapter Faraday.default_adapter
    end

    connection.post action.url, webhook_data.to_json do |req|
      req.headers['Content-Type'] = 'application/json'
    end
  end

  private

  attr_reader :action, :object, :user, :event, :timestamp

  def webhook_data
    {
      type: webhook_event,
      timestamp: timestamp,
      data: {
        **object_properties,
        values: object_data,
        user: user ? format_user(user) : nil
      }
    }
  end

  def webhook_event
    {
      create: :'record.create',
      update: :'record.update',
      destroy: :'record.destroy'
    }[event]
  end

  def object_data
    attributes = webhook_attributes.filter_map(&:object_type_attribute)
    object_attributes = object.object_attributes.where(object_type_attribute: attributes).index_by(&:object_type_attribute_id)

    attributes.each_with_object({}) do |attribute, hash|
      if attribute.data_attribute?
        hash[attribute.field_identifier] = object_attributes[attribute.id]&.value
      elsif attribute.user_relationship?
        # listed_users always queries, alternatives could be implemented
        NPlusOne.ignore do
          users = object.listed_users(attribute.id).map(&method(:format_user))

          hash[attribute.field_identifier] = attribute.relationship_kind == 'single' ? users.first : users
        end
      end

      hash
    end
  end

  def object_properties
    properties = [:id, :object_type_id].concat(webhook_attributes.filter_map(&:column_name))
    properties.each_with_object({}) do |property, hash|
      hash[PROPERTY_KEY_RENAMES[property.to_sym] || property] = property_value(property)
      hash
    end
  end

  def property_value(name)
    case name.to_sym
    when :id then object.id.to_s
    when :object_type_id then object.object_type_id.to_s
    when :created_at then object.created_at.iso8601
    when :updated_at then object.updated_at.iso8601
    when :'team.name' then object.team&.name
    when :user_id then format_user(object.user)
    when :last_updated_by_id then format_user(object.last_updated_by)
    when :local_id then object.local_id.to_s
    end
  end

  def format_user(user)
    { id: user.id, name: user.name }
  end

  def webhook_attributes
    @webhook_attributes ||= action.automation_webhook_action_attributes.includes(:object_type_attribute)
  end
end
