class AutomationWebhookAction::UpdateService < ApplicationService
  include FindRelationshipsInIncluded

  def initialize(automation_webhook_action, payload:)
    @automation_webhook_action = automation_webhook_action
    @payload = payload
  end

  def execute
    ActiveRecord::Base.transaction do
      automation_webhook_action.assign_attributes(automation_webhook_action_params)
      update_action_attributes

      automation_webhook_action.save!
    end

    automation_webhook_action
  end

  private

  attr_reader :automation_webhook_action, :payload

  def update_action_attributes
    return if payload.dig(:relationships, :automation_webhook_action_attributes).nil?

    attributes_params = find_relationships_in_included(
      payload.dig(:relationships, :automation_webhook_action_attributes, :data), payload[:included]
    )

    object_type_attribute_ids = attributes_params.filter_map do |param|
      param.dig('relationships', 'object_type_attribute', 'data', 'id')
    end
    object_type_attributes = Custom::ObjectTypeAttribute.where(id: object_type_attribute_ids).index_by { |attr| attr.id.to_s }

    current_action_attributes = automation_webhook_action.automation_webhook_action_attributes
    automation_webhook_action.automation_webhook_action_attributes = attributes_params.map do |param|
      relationship = current_action_attributes.find { |r| r.id.to_s == param[:id] } if param.key?(:id)
      relationship ||= automation_webhook_action.automation_webhook_action_attributes.new(__guid__: param[:__guid__])
      relationship.assign_attributes(param[:attributes].permit(automation_webhook_action_attribute_params)) if param[:attributes]

      relationship.object_type_attribute = object_type_attributes[param.dig(:relationships, :object_type_attribute, :data, :id)]

      relationship.save!

      relationship
    end
  end

  def automation_webhook_action_params
    payload.fetch(:attributes, {}).permit(:url)
  end

  def automation_webhook_action_attribute_params
    [:column_name]
  end
end
