class Calculation::RecalculateService < ApplicationService
  attr_reader :calculation

  def initialize(calculation)
    @calculation = calculation
  end

  def execute
    return if calculation&.type_lookup?

    ota = calculation.object_type_attribute

    ota.object_type.objects.find_each do |object|
      begin
        Calculation::FormulaEvaluateService.execute(calculation, object)
      rescue FormulaError, Calculation::RuntimeError
        object.object_attributes.detect { |oa| oa.object_type_attribute_id == ota.id }&.update!(value: nil)
      end

      Custom::Object::AutoCalculationService.execute(object, 'update', changed_attribute_ids: [ota.id])
    rescue Calculation::RuntimeError, FormulaError
      # Just continue processing other objects if one fails with an expected error
    end
  end
end
