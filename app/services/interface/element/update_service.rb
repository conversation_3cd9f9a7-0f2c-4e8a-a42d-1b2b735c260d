class Interface::Element::UpdateService < ApplicationService
  include FindRelationshipsInIncluded

  def initialize(interface_element, params:)
    @interface_element = interface_element
    @params = params
  end

  def execute
    ActiveRecord::Base.transaction do
      interface_element.assign_attributes(interface_element_params)
      if params.dig(:data, :relationships, :section).present?
        interface_element.section = Section.find(params.dig(:data, :relationships, :section, :data, :id))
      end
      assign_parent
      update_linked_filter
      interface_element.save!

      update_widget
      update_interface_element_attachment
      update_interface_element_button
      update_view
      update_record_picker
      update_interface_element_field
      update_interface_element_filter
      update_interface_element_link
    end

    interface_element
  end

  private

  attr_reader :interface_element, :params

  def interface_element_params
    params.require(:data)
          .fetch(:attributes, {})
          .permit(:name, :subtitle, :background_color, grid_position: [:x, :y, :h, :w])
  end

  def assign_parent
    return unless params.dig(:data, :relationships, :parent)&.key?(:data)

    parent_id = params.dig(:data, :relationships, :parent, :data, :id)
    return if interface_element.parent_id&.to_s == parent_id

    interface_element.parent = parent_id && interface_element.section.interface_elements
                                                             .where(element_type: :nested_grid)
                                                             .find(parent_id)
  end

  def update_widget
    return if widget_params.blank?

    widget = interface_element.widget
    widget.update!(widget_params)
  end

  def update_view
    return unless interface_element.view && view_params.present?

    View::UpdateService.execute(interface_element.view, params: view_params)
  end

  def interface_element_button_params
    data = params.dig(:data, :relationships, :interface_element_button, :data)

    find_relationships_in_included(data, params[:included]) unless data.nil?
  end

  def widget_params
    data = params.dig(:data, :relationships, :widget, :data)
    find_relationships_in_included(data, params[:included]).fetch(:attributes, {}).permit(:html) unless data.nil?
  end

  def update_interface_element_attachment
    return if interface_element_attachment_params.nil?

    interface_element.interface_element_attachment.update!(interface_element_attachment_params
    .fetch(:attributes)
    .permit(:display_type))
    update_attachments
  end

  def update_interface_element_button
    return if interface_element_button_params.blank?
    return if interface_element.interface_element_button.blank?

    Interface::Element::Button::UpdateService.execute(interface_element.interface_element_button,
                                                      params: interface_element_button_params)
  end

  def update_interface_element_link
    return if interface_element.interface_element_link.blank?

    Interface::Element::Link::UpdateService.execute(interface_element.interface_element_link,
                                                    params: params)
  end

  def update_attachments
    return if attachment_params.nil?

    interface_element.interface_element_attachment&.attachments = attachment_params.map do |attachment_param|
      interface_element.interface_element_attachment.create_or_update_attachment!(attachment_param)
    end
  end

  def update_interface_element_field
    return if interface_element.interface_element_field.nil?
    return if interface_element_field_params.blank?

    Interface::Element::Field::UpdateService.execute(interface_element.interface_element_field, params: interface_element_field_params)
  end

  def interface_element_field_params
    data = params.dig(:data, :relationships, :interface_element_field, :data)
    find_relationships_in_included(data, params[:included]) unless data.nil?
  end

  def interface_element_attachment_params
    data = params.dig(:data, :relationships, :interface_element_attachment, :data)
    find_relationships_in_included(data, params[:included]) unless data.nil?
  end

  def attachment_params
    @attachment_params ||= begin
      data = interface_element_attachment_params.dig(:relationships, :attachments, :data)
      find_relationships_in_included(data, params[:included]) unless data.nil?
    end
  end

  def view_params
    data = params.dig(:data, :relationships, :view, :data)
    view = find_relationships_in_included(data, params[:included]) unless data.nil?
    return if view.nil?

    # Add data to make it look like JSON:API
    ActionController::Parameters.new(data: view, included: params[:included])
  end

  def update_record_picker
    return if record_picker_params.blank?

    permitted_params = record_picker_params
                       .fetch(:attributes, {})
                       .permit(:placeholder, :column_name)

    interface_element.interface_element_record_picker.assign_attributes(permitted_params)
    ota_id = record_picker_params.dig(:relationships, :object_type_attribute, :data, :id)
    interface_element.interface_element_record_picker.object_type_attribute = if ota_id.nil?
                                                                                nil
                                                                              else
                                                                                Custom::ObjectTypeAttribute.find(ota_id)
                                                                              end

    update_object_query_view(interface_element.interface_element_record_picker.object_query_view, record_picker_params)
    interface_element.interface_element_record_picker.save!
  end

  def update_object_query_view(object_query_view, element_params)
    return unless object_query_view
    return unless element_params.dig(:relationships, :object_query_view, :data)

    object_query_view_data = find_relationships_in_included(element_params.dig(:relationships, :object_query_view, :data),
                                                            params[:included])
    object_query_view_params = ActionController::Parameters.new(
      data: object_query_view_data,
      included: params[:included]
    )
    ObjectQueryView::UpdateService.execute(object_query_view, params: object_query_view_params)
  end

  def record_picker_params
    @record_picker_params ||= begin
      data = params.dig(:data, :relationships, :interface_element_record_picker, :data)
      find_relationships_in_included(data, params[:included]) unless data.nil?
    end
  end

  def update_interface_element_filter
    return if filter_params.blank?

    update_object_query_view(interface_element.interface_element_filter&.object_query_view, filter_params)
  end

  def filter_params
    @filter_params ||= begin
      data = params.dig(:data, :relationships, :interface_element_filter, :data)
      find_relationships_in_included(data, params[:included]) unless data.nil?
    end
  end

  def update_linked_filter
    interface_element.linked_filter = linked_filter_param.present? ? Interface::Element::Filter.find(linked_filter_param[:id]) : nil
  end

  def linked_filter_param
    params.dig(:data, :relationships, :linked_filter, :data)
  end
end
