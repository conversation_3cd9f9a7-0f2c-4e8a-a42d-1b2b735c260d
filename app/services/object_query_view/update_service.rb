class ObjectQueryView::UpdateService < ApplicationService
  include FindRelationshipsInIncluded

  PERMITTABLE_RULE_ATTRIBUTES = [
    :column_name, :operator, :rule_type, :sort_order, :value, :value2, :value3, :position,
    :object_type_attribute_id, :relationship_attribute_id, :relationship_attribute2_id
  ].freeze

  def initialize(object_query_view, params:)
    @object_query_view = object_query_view
    @params = params
  end

  def execute
    ActiveRecord::Base.transaction do
      update_object_query_rules

      object_query_view.save!
    end

    object_query_view
  end

  private

  attr_reader :object_query_view, :params

  def update_object_query_rules
    return unless params.dig(:data, :relationships)&.key?(:object_query_rules)

    object_query_rules_params = find_relationships_in_included(params.dig(:data, :relationships, :object_query_rules, :data),
                                                               params[:included])

    current_rules = object_query_view.object_query_rules

    object_query_view.object_query_rules = object_query_rules_params.map do |param|
      rule = current_rules.find { |r| r.id.to_s == param[:id] } if param.key?(:id)
      rule ||= object_query_view.object_query_rules.new(__guid__: param[:__guid__])
      rule.assign_attributes(param[:attributes].permit(PERMITTABLE_RULE_ATTRIBUTES))
      rule.save!
      rule
    end
  end
end
