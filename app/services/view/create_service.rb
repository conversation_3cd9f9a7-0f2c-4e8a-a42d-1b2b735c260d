class View::CreateService < ApplicationService
  include FindRelationshipsInIncluded

  def initialize(params:)
    @params = params
    @view = View.new
  end

  def execute
    ActiveRecord::Base.transaction do
      initialize_view
      filter_ota = view.object_type.object_type_attributes.find(filter_attribute_id) if filter_attribute_id
      view.filter_attribute = filter_ota
      view.save!

      create_visualisation
      create_object_query_view
    end

    view
  end

  private

  attr_reader :params, :view

  def initialize_view
    view.object_type = object_type
    view.user = user
    view.assign_attributes(view_params)
    view.interface_element = interface_element if interface_element
    view.__guid__ = params.dig(:data, :__guid__)
  end

  def interface_element
    interface_element_id = params.dig(:data, :relationships, :interface_element, :data, :id)
    @interface_element ||= Interface::Element.find(interface_element_id) if interface_element_id
  end

  def create_visualisation
    Visualisation.new(view: view, view_type: 'objectTable')
    if visualisation_params.present?
      view.visualisation.__guid__ = visualisation_params.dig(:data, :__guid__)
      Visualisation::UpdateService.execute(view.visualisation, params: visualisation_params)
    else
      view.visualisation.save!
    end
  end

  def create_object_query_view
    view.object_query_view = ObjectQueryView.new

    if object_query_view_params.present?
      view.object_query_view.__guid__ = object_query_view_params[:__guid__]

      view.object_query_view.object_query_rules = object_query_rules_params.map do |param|
        rule = view.object_query_view.object_query_rules.new(__guid__: param[:__guid__])
        rule.assign_attributes(param[:attributes].permit(object_query_rules_params_permit))
        rule.save!
        rule
      end
    end
    view.object_query_view.save!
  end

  def object_type
    return if params.dig(:data, :relationships, :object_type, :data).blank?

    @object_type ||= Custom::ObjectType.find(params.dig(:data, :relationships, :object_type, :data, :id))
  end

  def object_query_view_params
    find_relationships_in_included(params.dig(:data, :relationships, :object_query_view, :data), params[:included])
  end

  def object_query_rules_params
    find_relationships_in_included(object_query_view_params.dig(:relationships, :object_query_rules, :data), params[:included])
  end

  def visualisation_params
    data = params.dig(:data, :relationships, :visualisation, :data)
    ActionController::Parameters.new(data: find_relationships_in_included(data, params[:included])) unless data.nil?
  end

  def view_params
    params.require(:data)
          .fetch(:attributes, {})
          .permit(:name, :default, :object_type_variant_id, :include_records_from_subteams, :include_shared_records,
                  :allow_create_new_object, { filter_properties: filter_properties_params }, template: {})
  end

  def object_query_rules_params_permit
    ObjectQueryView::UpdateService::PERMITTABLE_RULE_ATTRIBUTES
  end

  def filter_properties_params
    [:begin_date, :end_date, :date_period, :date_offset]
  end

  def filter_attribute_id
    params.dig(:data, :relationships, :filter_attribute, :data, :id)
  end

  def user
    id = params.dig(:data, :relationships, :user, :data, :id)

    @user ||= id.blank? ? nil : User.find(id)
  end
end
