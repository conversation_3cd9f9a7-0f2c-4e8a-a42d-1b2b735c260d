class View::DataService < ApplicationService
  def initialize(view, data_point: nil, user: nil, custom_filter_rules: nil)
    @view = view
    @data_point = data_point
    @user = user
    @custom_filter_rules = custom_filter_rules
  end

  attr_reader :view, :data_point, :user, :custom_filter_rules

  def execute
    template = view.template
    template = apply_interface_scope_teams(template) if view.interface_element
    template = apply_interface_filters(template) if view.interface_element
    template = apply_data_point_filter(template) if data_point.present?
    DataSourceQuery::Base.execute_template(template)
  end

  private

  def apply_interface_scope_teams(template)
    data_scope_teams = view.interface_element.section.interface.interface_team_data_scopes.select(:team_id)

    data_scope_teams = Team.self_and_descendants(data_scope_teams)
    return template if data_scope_teams.blank?

    modified_template = prepared_template(template)
    modified_template.restrict_to_teams!(data_scope_teams.pluck(:id))

    modified_template
  end

  def apply_interface_filters(template)
    return template unless view.interface_element.linked_filter

    modified_template = prepared_template(template)

    objects = Custom::Object.where(object_type_id: view.object_type_id)
    query = if custom_filter_rules.nil?
              view.interface_element.linked_filter.object_query_view.query(user)
            else
              rules = custom_filter_rules.map do |rule|
                ObjectQueryRule.new(rule.dig(:data, :attributes).permit(object_query_rules_params_permit))
              end

              ObjectQueryBuilder::Builder.new(base: objects, rules: rules, user: user).query
            end

    modified_template.add_filter!('and', { operator: 'in', value: query.select(:id), sources: [{ type: 'property', source: 'id' }] })

    modified_template
  end

  def apply_data_point_filter(template)
    modified_template = prepared_template(template).expand_time_range_clause
    raise View::TemplateDataPointError unless modified_template.group_replaceable_by_data?(data_point)

    modified_template.replace_group_clause_by_selects(data_point)
  end

  def prepared_template(template)
    return template if template.is_a?(DataSourceQuery::Template)

    DataSourceQuery::Template.new(template.deep_dup)
  end

  def object_query_rules_params_permit
    ObjectQueryView::UpdateService::PERMITTABLE_RULE_ATTRIBUTES
  end
end

class View::TemplateDataPointError < StandardError
end
