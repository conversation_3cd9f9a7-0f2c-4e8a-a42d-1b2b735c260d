class View::UpdateService < ApplicationService
  include FindRelationshipsInIncluded

  def initialize(view, params:)
    @view = view
    @params = params
  end

  def execute
    ActiveRecord::Base.transaction do
      view.assign_attributes(view_params)
      update_object_query_view
      update_visualisation
      update_user
      filter_ota = view.object_type.object_type_attributes.find(filter_attribute_id) if filter_attribute_id
      view.filter_attribute = filter_ota
      view.save!

      update_object_types

      view
    end
  end

  private

  attr_reader :view, :params

  def view_params
    template_params = [template: {}] if view.configurable_template

    params.require(:data)
          .fetch(:attributes, {})
          .permit(
            :allow_create_new_object,
            :include_records_from_subteams,
            :include_shared_records,
            :name,
            { filter_properties: filter_properties_params },
            *template_params
          )
  end

  def update_object_query_view
    return unless view.object_query_view
    return unless params.dig(:data, :relationships, :object_query_view, :data)

    object_query_view_data = find_relationships_in_included(params.dig(:data, :relationships, :object_query_view, :data),
                                                            params[:included])
    object_query_view_params = ActionController::Parameters.new(
      data: object_query_view_data,
      included: params[:included]
    )
    ObjectQueryView::UpdateService.execute(view.object_query_view, params: object_query_view_params)
  end

  def update_object_types
    param = params.dig(:data, :relationships, :object_types)
    return if param.blank?

    object_types = Custom::ObjectType.where(id: param[:data].pluck(:id)) if param[:data].present?
    view.object_types = object_types.to_a
  end

  def update_visualisation
    return unless (visualisation = view.visualisation).present? && visualisation_params.present?

    Visualisation::UpdateService.execute(visualisation, params: visualisation_params)
  end

  def update_user
    return if params.dig(:data, :relationships, :user).blank?

    view.user = user_params.nil? ? nil : User.find(user_params[:id])
  end

  def visualisation_params
    data = params.dig(:data, :relationships, :visualisation, :data)
    visualisation = find_relationships_in_included(data, params[:included]) unless data.nil?
    return if visualisation.nil?

    ActionController::Parameters.new(data: visualisation, included: params[:included])
  end

  def user_params
    params.dig(:data, :relationships, :user, :data)
  end

  def filter_properties_params
    [:begin_date, :end_date, :date_period, :date_offset]
  end

  def filter_attribute_id
    params.dig(:data, :relationships, :filter_attribute, :data, :id)
  end
end
