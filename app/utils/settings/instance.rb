class Settings::Instance < Settings::Settings
  setting :allow_local_account_creation, default: false
  setting :allow_local_authentication, default: false
  setting :allow_sso_azure, default: false
  setting :allow_sso_google, default: false
  setting :allowed_domains, default: []
  setting :enabled_announcements, default: []
  setting :restrict_domains, default: false
  setting :url, default: ''
  setting :company_name, default: nil
  setting :openai_key, default: nil
  setting :hide_recent_objects_section, default: false
  setting :hide_team_field_on_forms, default: false
  setting :custom_assistant_system_message, default: ''
  setting :display_avatar_with_company_logo, default: false
end
