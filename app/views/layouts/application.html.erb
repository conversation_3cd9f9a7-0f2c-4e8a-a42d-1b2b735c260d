<!DOCTYPE html>
<html>
  <head>
    <title>Houston</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex, nofollow">
    <%= csrf_meta_tags %>
    <%= stylesheet_link_tag 'application', media: 'all' %>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;500&display=swap" rel="stylesheet">
    <% if Rails.env.production? && File.exist?(Rails.root.join('public/index.html')) %>
      <% File.open(Rails.root.join('public/index.html')).each do |line| %>
        <% if ['name="theme-color"', 'rel="icon"', 'rel="manifest"', 'name="msapplication-config"', 'rel="apple-touch-', 'name="apple-mobile-', 'src="/sw-registration'].any? { |word| line.include?(word) } %>
          <%= raw line %>
        <% end %>
      <% end %>
    <% end %>
  </head>

  <body>
    <%= yield %>
  </body>
</html>
