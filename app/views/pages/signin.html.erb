<div style="height: 100vh; display: flex; justify-content: center; align-items: center; background-color: rgb(3 69 90);">
  <% if Instance.current.background.attached? %>
    <main class="with-side-image">
      <div class="side-image" style="height: 100%; background: url(<%= url_for(Instance.current.background) %>) center top no-repeat; background-size: cover;">
      </div>
  <% else %>
    <main>
  <% end %>
    <div class="container">
      <div class="text-center">
      <%= image_tag("logos/houston_dark.png", alt: "Houston logo", class: "logo") %>
        <h2><%= t('welcome') %></h2>
        <h4><%= t('login_to_continue') %></h4>
      </div>

      <% if Instance.current.settings.allow_local_authentication %>
        <div>
          <%= form_for(:session, url: sessions_path, id: 'form-signin') do |f| %>
            <%= f.text_field :email, placeholder: t('email_address'), autocomplete: 'email', class: 'text-field' %>

            <%= f.password_field :password, placeholder: t('password'), autocomplete: 'current-password', class: 'text-field' %>

            <%= f.submit t('login'), type: 'submit', disabled: true, class: 'btn-primary mb-0' %>
          <% end %>
        </div>

        <%if Instance.current.settings.allow_local_account_creation %>
          <div>
            <p class="mb-0"> <%= t('no_account_prompt') %> <%= link_to t('sign_up'), signup_page_path, class: 'action-link' %></p>
          </div>
          <div>
            <p class="mb-0"> <%= link_to t('forgot_password_prompt'), reset_password_path, class: 'action-link' %></p>
          </div>
        <% end %>
      <% end %>

      <% if (!Rails.env.production? || Instance.current.settings.allow_sso_google || Instance.current.settings.allow_sso_azure) && Instance.current.settings.allow_local_authentication %>
        <div class="divider">
          <div class="divider-side"><hr></div>
          <div class="divider-center"><span><%= t('or') %></span></div>
          <div class="divider-side"><hr></div>
        </div>
      <% end %>

      <div class="sso">
        <% unless Rails.env.production? %>
          <%= form_tag('/auth/developer', method: 'post', class: 'omniauth-link') do %>
            <button class="btn-primary" type='submit'><%= t('developer') %></button>
          <% end %>
        <% end %>
        <% if Instance.current.settings.allow_sso_google %>
          <%= form_tag('/auth/google_oauth2', method: 'post', class: 'omniauth-link') do %>
            <button class="btn-sso" type='submit'>
              <img src="<%= image_path('sso/google-icon.svg') %>"> <%= t('login_with_google') %>
            </button>
          <% end %>
        <% end %>
        <% if Instance.current.settings.allow_sso_azure %>
          <%= form_tag('/auth/azure_activedirectory_v2', method: 'post', class: 'omniauth-link') do %>
            <button class="btn-sso" type='submit'>
              <img src="<%= image_path('sso/ms-icon.svg') %>"> <%= t('login_with_microsoft') %>
            </button>
          <% end %>
        <% end %>
      </div>
    </div>
  </main>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      var form = document.getElementById("form-signin");
      var timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (form) {
        function onFormInputChange() {
          var formData = new FormData(form)
          var formEntries = Object.fromEntries(formData);
          var valid = formEntries["session[email]"] && formEntries["session[password]"];

          form.querySelector("input[type='submit']").disabled = !valid;
        }

        form.querySelectorAll("input[name^='session[']").forEach(function(element) {
          element.addEventListener("input", onFormInputChange);
        });
      }

      // Append time_zone to the omniauth links
      document.querySelectorAll(".omniauth-link").forEach(function(element) {
        let url = new URL(element.action);
        url.searchParams.append('time_zone', timeZone);
        element.action = url.href;
      });
    });
  </script>
</div>
