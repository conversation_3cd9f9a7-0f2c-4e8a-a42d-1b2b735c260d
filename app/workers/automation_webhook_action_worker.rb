class AutomationWebhookActionWorker
  include Sidekiq::Worker

  sidekiq_options retry: 5

  def self.schedule_for(automation_webhook_action, object, event, user, timestamp = nil)
    options = {
      'automation_webhook_action_id' => automation_webhook_action.id,
      'object_id' => object.id,
      'user_id' => user&.id,
      'event' => event,
      'timestamp' => (timestamp || object.updated_at).iso8601
    }

    perform_async(options)
  end

  def perform(options)
    automation_webhook_action = AutomationWebhookAction.find_by(id: options['automation_webhook_action_id'])
    return unless automation_webhook_action

    object = Custom::Object.find_by(id: options['object_id'])
    return unless object

    user = User.find_by(id: options['user_id']) if options['user_id']

    AutomationWebhookAction::HandlerService.execute(
      automation_webhook_action,
      object: object,
      event: options['event']&.to_sym,
      timestamp: options['timestamp'],
      user: user
    )
  end
end
