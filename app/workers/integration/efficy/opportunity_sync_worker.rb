class Integration::Efficy::OpportunitySyncWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'internal'
  sidekiq_options retry: 1
  sidekiq_options lock: :until_and_while_executing,
                  lock_ttl: 10.minutes.to_i,
                  on_conflict: { client: :replace, server: :reschedule },
                  lock_args_method: :lock_args

  def self.lock_args(args)
    options = args.first
    [options['opportunity_id']]
  end

  def perform(options)
    mapping = Integration::ForeignObject::Mapping.find_by(id: options['mapping_id'])
    return if mapping.blank?

    event = JSON.parse(options['event']).with_indifferent_access
    handler = Integration::Efficy::Opportunity::Handler.new(mapping.integration)
    handler.handle_opportunity_event(event, mapping)
  end
end
