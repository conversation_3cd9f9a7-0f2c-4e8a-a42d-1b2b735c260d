# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_06_10_090435) do
  create_table "access_tokens", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "token"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind"
    t.datetime "expires_at"
    t.datetime "last_used_at"
    t.index ["kind", "user_id"], name: "index_access_tokens_on_kind_and_user_id"
    t.index ["user_id"], name: "index_access_tokens_on_user_id"
  end

  create_table "active_storage_attachments", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", precision: nil, null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "ai_token_usages", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "api_key"
    t.integer "input_tokens"
    t.integer "output_tokens"
    t.bigint "automation_ai_action_id"
    t.bigint "job_id"
    t.string "ai_model"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_ai_action_id"], name: "index_ai_token_usages_on_automation_ai_action_id"
    t.index ["job_id"], name: "index_ai_token_usages_on_job_id"
  end

  create_table "announcements", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.text "body", null: false
    t.string "category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "assistant_chats", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.json "context"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_assistant_chats_on_user_id"
  end

  create_table "assistant_messages", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "chat_id"
    t.bigint "job_id"
    t.json "content"
    t.integer "origin"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chat_id"], name: "index_assistant_messages_on_chat_id"
    t.index ["job_id"], name: "index_assistant_messages_on_job_id"
  end

  create_table "attachments", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "model_type"
    t.bigint "model_id"
    t.index ["model_type", "model_id"], name: "index_attachments_on_model"
  end

  create_table "automation_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_id"
    t.integer "action_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "team_id"
    t.bigint "object_type_variant_id"
    t.index ["automation_id"], name: "index_automation_actions_on_automation_id"
    t.index ["object_type_variant_id"], name: "index_automation_actions_on_object_type_variant_id"
    t.index ["team_id"], name: "index_automation_actions_on_team_id"
  end

  create_table "automation_ai_action_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_ai_action_id"
    t.bigint "object_type_attribute_id"
    t.integer "param_type"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "relationship_attribute_id"
    t.index ["automation_ai_action_id"], name: "idx_on_automation_ai_action_id_e6e5062771"
    t.index ["object_type_attribute_id"], name: "idx_on_object_type_attribute_id_2bb623861f"
    t.index ["relationship_attribute_id"], name: "idx_on_relationship_attribute_id_0cf8980367"
  end

  create_table "automation_ai_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_action_id"
    t.text "system_message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_action_id"], name: "index_automation_ai_actions_on_automation_action_id"
  end

  create_table "automation_integration_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_action_id"
    t.bigint "integration_action_id"
    t.bigint "foreign_object_mapping_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_action_id"], name: "index_automation_integration_actions_on_automation_action"
    t.index ["foreign_object_mapping_id"], name: "index_automation_integration_actions_on_foreign_object_mapping"
    t.index ["integration_action_id"], name: "index_automation_integration_actions_on_integration_action"
  end

  create_table "automation_mail_action_address_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_mail_action_id"
    t.bigint "object_type_attribute_id"
    t.bigint "relationship_object_type_attribute_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "column_name"
    t.index ["automation_mail_action_id"], name: "idx_on_automation_mail_action_id_2e23282915"
    t.index ["object_type_attribute_id"], name: "idx_on_object_type_attribute_id_acaadae80a"
    t.index ["relationship_object_type_attribute_id"], name: "idx_on_relationship_object_type_attribute_id_15e92b964a"
  end

  create_table "automation_mail_action_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_mail_action_id"
    t.bigint "object_type_attribute_id"
    t.string "column_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.index ["automation_mail_action_id"], name: "index_automation_mail_action_attr_on_automation_mail_action_id"
    t.index ["object_type_attribute_id"], name: "index_automation_mail_action_attr_on_obj_type_attr_id"
  end

  create_table "automation_mail_action_role_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_mail_action_id"
    t.bigint "object_type_role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_mail_action_id"], name: "index_mail_action_object_role_rel_on_mail_action_id"
    t.index ["object_type_role_id"], name: "index_mail_action_object_role_rel_on_role_id"
  end

  create_table "automation_mail_action_user_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "automation_mail_action_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_mail_action_id"], name: "index_autom_mail_action_user_rel_on_autom_mail_action_id"
    t.index ["user_id"], name: "index_automation_mail_action_user_relationships_on_user_id"
  end

  create_table "automation_mail_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_action_id"
    t.integer "mail_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "to_listed_users", default: false
    t.boolean "to_team", default: false
    t.json "settings"
    t.boolean "exclude_service_invoker", default: false, null: false
    t.json "static_mail_address"
    t.bigint "object_type_attribute_id"
    t.index ["automation_action_id"], name: "index_automation_mail_actions_on_automation_action_id"
    t.index ["object_type_attribute_id"], name: "index_automation_mail_actions_on_object_type_attribute_id"
  end

  create_table "automation_static_data_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_action_id"
    t.bigint "object_type_attribute_id"
    t.json "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_action_id"], name: "index_automation_static_data_actions_on_automation_action_id"
    t.index ["object_type_attribute_id"], name: "index_automation_static_data_actions_on_object_type_attr_id"
  end

  create_table "automation_trigger_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_trigger_id", null: false
    t.bigint "object_type_attribute_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "value"
    t.integer "condition_type", default: 0, null: false
    t.string "column_name"
    t.index ["automation_trigger_id"], name: "index_aut_trig_object_type_attr_rel_on_aut_trig_id"
    t.index ["object_type_attribute_id"], name: "index_aut_trig_object_type_attr_rel_on_object_type_attr_id"
  end

  create_table "automation_triggers", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_id"
    t.bigint "object_type_id"
    t.integer "trigger_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "condition_operator", default: "or", null: false
    t.index ["automation_id"], name: "index_automation_triggers_on_automation_id"
    t.index ["object_type_id"], name: "index_automation_triggers_on_object_type_id"
  end

  create_table "automation_update_object_action_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_update_object_action_id"
    t.bigint "object_type_attribute_id"
    t.json "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_update_object_action_id"], name: "idx_on_automation_update_object_action_id_92a14eb815"
    t.index ["object_type_attribute_id"], name: "idx_on_object_type_attribute_id_77788ddc9d"
  end

  create_table "automation_update_object_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_action_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_action_id"], name: "index_automation_update_object_actions_on_automation_action_id"
  end

  create_table "automation_webhook_action_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_webhook_action_id"
    t.bigint "object_type_attribute_id"
    t.string "column_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_webhook_action_id"], name: "idx_on_automation_webhook_action_id_eaa145e54b"
    t.index ["object_type_attribute_id"], name: "idx_on_object_type_attribute_id_603c2c7bbe"
  end

  create_table "automation_webhook_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "automation_action_id"
    t.string "url"
    t.json "headers"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_action_id"], name: "index_automation_webhook_actions_on_automation_action_id"
  end

  create_table "automations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "active", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "team_id"
    t.bigint "integration_id"
    t.bigint "object_type_id"
    t.bigint "object_type_variant_id"
    t.integer "schedule_type", default: 0, null: false
    t.string "schedule_value"
    t.bigint "schedule_attribute_id"
    t.index ["integration_id"], name: "index_automations_on_integration_id"
    t.index ["object_type_id"], name: "index_automations_on_object_type_id"
    t.index ["object_type_variant_id"], name: "index_automations_on_object_type_variant_id"
    t.index ["schedule_attribute_id"], name: "index_automations_on_schedule_attribute_id"
    t.index ["team_id"], name: "index_automations_on_team_id"
  end

  create_table "backups", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "calculations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_attribute_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "formula", limit: 4096
    t.integer "calculation_type", default: 0, null: false
    t.index ["object_type_attribute_id"], name: "index_calculations_on_object_type_attribute_id", unique: true
  end

  create_table "comments", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.text "text"
    t.string "commentable_type"
    t.bigint "commentable_id"
    t.bigint "user_id"
    t.datetime "edited_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "condition_rules", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "conditional_display_id"
    t.string "operator"
    t.json "value"
    t.bigint "object_type_attribute_id"
    t.string "column_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "rule_type"
    t.index ["conditional_display_id"], name: "index_condition_rules_on_conditional_display_id"
    t.index ["object_type_attribute_id"], name: "index_condition_rules_on_object_type_attribute_id"
  end

  create_table "conditional_display_applicabilities", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "applicable_type"
    t.bigint "applicable_id"
    t.bigint "conditional_display_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["applicable_type", "applicable_id", "conditional_display_id"], name: "index_conditional_display_applicabilities_uniqueness", unique: true
    t.index ["conditional_display_id"], name: "index_conditional_display_applicabilities_on_cd_id"
  end

  create_table "conditional_displays", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "subject_type"
    t.bigint "subject_id"
    t.string "display_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "object_type_variant_id"
    t.index ["object_type_variant_id"], name: "index_conditional_displays_on_object_type_variant_id"
    t.index ["subject_type", "subject_id"], name: "index_conditional_displays_on_subject"
  end

  create_table "connections", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.string "resource"
    t.integer "resource_id"
    t.string "resource_action"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "access_token_id"
    t.index ["access_token_id"], name: "index_connections_on_access_token_id"
    t.index ["user_id"], name: "index_connections_on_user_id"
  end

  create_table "data_sources", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name"
    t.string "uuid", null: false
    t.bigint "user_id"
    t.bigint "view_id"
    t.bigint "team_id"
    t.bigint "access_token_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["access_token_id"], name: "index_data_sources_on_access_token_id"
    t.index ["team_id"], name: "index_data_sources_on_team_id"
    t.index ["user_id"], name: "index_data_sources_on_user_id"
    t.index ["uuid"], name: "index_data_sources_on_uuid", unique: true
    t.index ["view_id"], name: "index_data_sources_on_view_id"
  end

  create_table "foreign_object_attribute_mappings", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "foreign_object_mapping_id"
    t.bigint "object_type_attribute_id"
    t.string "foreign_object_type_attribute"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "property"
    t.json "options"
    t.bigint "integration_action_param_id"
    t.boolean "incoming", default: true, null: false
    t.boolean "outgoing", default: true, null: false
    t.json "value"
    t.index ["foreign_object_mapping_id", "foreign_object_type_attribute"], name: "index_foreign_object_attribute_mappings_mapping_f_attribute"
    t.index ["foreign_object_mapping_id", "property"], name: "index_foreign_object_attribute_mappings_mapping_property"
    t.index ["integration_action_param_id"], name: "index_foreign_object_attribute_mappings_on_action_param"
    t.index ["object_type_attribute_id"], name: "index_foreign_object_attribute_mappings_attribute"
  end

  create_table "foreign_object_mappings", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "integration_id"
    t.bigint "object_type_id"
    t.string "foreign_object_type"
    t.json "enabled_incoming_events"
    t.json "enabled_outgoing_events"
    t.json "options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "model_type"
    t.bigint "foreign_object_attribute_mapping_id"
    t.bigint "integration_action_id"
    t.index ["foreign_object_attribute_mapping_id"], name: "index_foreign_object_mappings_on_attribute_mapping", unique: true
    t.index ["integration_action_id"], name: "index_foreign_object_mappings_on_integration_action"
    t.index ["integration_id", "foreign_object_type"], name: "index_foreign_object_mappings_integration_f_type"
    t.index ["object_type_id"], name: "index_foreign_object_mappings_type"
  end

  create_table "foreign_object_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "foreign_object_mapping_id"
    t.bigint "model_id"
    t.string "foreign_object_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "model_type"
    t.index ["foreign_object_mapping_id", "foreign_object_id"], name: "index_foreign_object_relationships_mapping_f_object"
    t.index ["model_type", "model_id"], name: "index_foreign_object_relationships_model"
  end

  create_table "form_elements", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "form_tab_id"
    t.bigint "object_type_attribute_id"
    t.string "input_type", null: false
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "custom_display"
    t.boolean "team_filterable", default: false, null: false
    t.json "options"
    t.bigint "object_type_variant_id"
    t.boolean "locked", default: true, null: false
    t.json "hidden"
    t.index ["form_tab_id"], name: "index_form_elements_on_form_tab_id"
    t.index ["object_type_attribute_id"], name: "index_form_elements_on_object_type_attribute_id"
    t.index ["object_type_variant_id"], name: "index_form_elements_on_object_type_variant_id"
  end

  create_table "form_tabs", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "parent_id"
    t.bigint "object_type_variant_id"
    t.integer "lft"
    t.integer "rgt"
    t.index ["lft"], name: "index_form_tabs_on_lft"
    t.index ["object_type_id"], name: "index_form_tabs_on_object_type_id"
    t.index ["object_type_variant_id"], name: "index_form_tabs_on_object_type_variant_id"
    t.index ["parent_id"], name: "index_form_tabs_on_parent_id"
    t.index ["rgt"], name: "index_form_tabs_on_rgt"
  end

  create_table "frontend_events", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "form_element_id"
    t.bigint "automation_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "save_before_execute", default: true, null: false
    t.bigint "interface_element_button_id"
    t.index ["automation_id"], name: "index_frontend_events_on_automation_id"
    t.index ["form_element_id"], name: "index_frontend_events_on_form_element_id", unique: true
    t.index ["interface_element_button_id"], name: "index_frontend_events_on_interface_element_button_id"
  end

  create_table "instances", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "subdomain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "settings", null: false
    t.index ["subdomain"], name: "index_instances_on_subdomain", unique: true
  end

  create_table "integration_action_params", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "param_type"
    t.json "options"
    t.bigint "integration_action_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_action_id"], name: "index_integration_action_params_on_integration_action"
  end

  create_table "integration_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "type"
    t.string "name"
    t.json "options"
    t.bigint "integration_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_integration_actions_on_integration_id"
  end

  create_table "integrations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "type", null: false
    t.string "webhook_id"
    t.json "options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["type"], name: "index_integrations_on_type"
    t.index ["webhook_id"], name: "index_integrations_on_webhook_id", unique: true
  end

  create_table "interface_element_attachments", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_element_id"
    t.integer "display_type", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_element_id"], name: "index_interface_element_attachments_on_interface_element_id"
  end

  create_table "interface_element_buttons", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "color"
    t.string "background_color"
    t.bigint "interface_element_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "action_type"
    t.bigint "interface_element_record_picker_id"
    t.index ["interface_element_id"], name: "index_interface_element_buttons_on_interface_element_id"
    t.index ["interface_element_record_picker_id"], name: "idx_int_elm_btn_on_record_picker"
  end

  create_table "interface_element_fields", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_element_id"
    t.bigint "object_type_attribute_id"
    t.bigint "interface_element_record_picker_id"
    t.string "column_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "hide_label", default: true, null: false
    t.boolean "read_only", default: true, null: false
    t.index ["interface_element_id"], name: "index_interface_element_fields_on_interface_element_id"
    t.index ["interface_element_record_picker_id"], name: "idx_on_interface_element_record_picker_id_9f5f01630b"
    t.index ["object_type_attribute_id"], name: "index_interface_element_fields_on_object_type_attribute_id"
  end

  create_table "interface_element_filters", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_element_id"
    t.bigint "object_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_element_id"], name: "index_interface_element_filters_on_interface_element_id"
    t.index ["object_type_id"], name: "index_interface_element_filters_on_object_type_id"
  end

  create_table "interface_element_link_tabs", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "position"
    t.bigint "interface_element_link_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_element_link_id"], name: "index_interface_element_link_tabs_on_interface_element_link_id"
  end

  create_table "interface_element_links", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_element_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_element_id"], name: "index_interface_element_links_on_interface_element_id"
  end

  create_table "interface_elements", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "section_id"
    t.integer "element_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "grid_position"
    t.string "background_color"
    t.bigint "linked_filter_id"
    t.bigint "parent_id"
    t.index ["linked_filter_id"], name: "index_interface_elements_on_linked_filter_id"
    t.index ["parent_id"], name: "index_interface_elements_on_parent_id"
    t.index ["section_id"], name: "index_interface_elements_on_section_id"
  end

  create_table "interface_external_url_navigation_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "url", null: false
    t.boolean "open_in_new_tab", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "interface_element_button_id"
    t.index ["interface_element_button_id"], name: "idx_interface_elem_btn_ext_url_nav_act"
  end

  create_table "interface_interface_section_navigation_actions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_element_button_id"
    t.bigint "section_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_element_button_id"], name: "idx_interface_elem_btn_interface_sec_nav_act"
    t.index ["section_id"], name: "idx_section_id_on_interface_sec_nav_act"
  end

  create_table "interface_team_data_scopes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_id"
    t.bigint "team_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_id", "team_id"], name: "index_interface_team_data_scopes_on_interface_id_and_team_id", unique: true
    t.index ["interface_id"], name: "index_interface_team_data_scopes_on_interface_id"
    t.index ["team_id"], name: "index_interface_team_data_scopes_on_team_id"
  end

  create_table "interface_team_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_id"
    t.bigint "team_id"
    t.boolean "published", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "publish_to_descendants", default: false, null: false
    t.index ["interface_id", "team_id"], name: "index_interface_team_relationships_on_interface_id_and_team_id", unique: true
    t.index ["interface_id"], name: "index_interface_team_relationships_on_interface_id"
    t.index ["team_id"], name: "index_interface_team_relationships_on_team_id"
  end

  create_table "interfaces", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "color"
    t.string "icon"
    t.bigint "team_id"
    t.index ["team_id"], name: "index_interfaces_on_team_id"
    t.index ["user_id"], name: "index_interfaces_on_user_id"
  end

  create_table "invitations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "team_id"
    t.string "invited_email", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["invited_email", "team_id"], name: "index_invitations_on_invited_email_and_team_id", unique: true
    t.index ["team_id"], name: "index_invitations_on_team_id"
    t.index ["user_id"], name: "index_invitations_on_user_id"
  end

  create_table "jobs", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "job_id"
    t.bigint "user_id"
    t.string "job_type"
    t.json "parameters"
    t.integer "status", default: 0, null: false
    t.json "result"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "scheduled_execution_time"
    t.bigint "automation_id"
    t.bigint "object_id"
    t.index ["automation_id"], name: "index_jobs_on_automation_id"
    t.index ["object_id"], name: "index_jobs_on_object_id"
    t.index ["scheduled_execution_time"], name: "index_jobs_on_scheduled_execution_time"
    t.index ["user_id"], name: "index_jobs_on_user_id"
  end

  create_table "mobility_string_translations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "locale", limit: 15, null: false
    t.string "key", limit: 243, null: false
    t.string "value"
    t.string "translatable_type"
    t.bigint "translatable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["translatable_id", "translatable_type", "key"], name: "index_mobility_string_translations_on_translatable_attribute"
    t.index ["translatable_id", "translatable_type", "locale", "key"], name: "index_mobility_string_translations_on_keys", unique: true
    t.index ["translatable_type", "key", "value", "locale"], name: "index_mobility_string_translations_on_query_keys"
  end

  create_table "mobility_text_translations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "locale", null: false
    t.string "key", null: false
    t.text "value", size: :medium
    t.string "translatable_type"
    t.bigint "translatable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["translatable_id", "translatable_type", "key"], name: "index_mobility_text_translations_on_translatable_attribute"
    t.index ["translatable_id", "translatable_type", "locale", "key"], name: "index_mobility_text_translations_on_keys", unique: true
  end

  create_table "object_attachments", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_id"
    t.bigint "object_type_attribute_id"
    t.string "name"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_id"], name: "index_object_attachments_on_object_id"
    t.index ["object_type_attribute_id"], name: "index_object_attachments_on_object_type_attribute_id"
  end

  create_table "object_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_id"
    t.bigint "object_type_attribute_id"
    t.json "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_id", "object_type_attribute_id"], name: "index_object_attributes_on_object_id_and_type_attribute_id", unique: true
    t.index ["object_type_attribute_id"], name: "index_object_attributes_on_object_type_attribute_id"
  end

  create_table "object_query_rules", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "rule_type"
    t.bigint "object_query_view_id"
    t.bigint "object_type_attribute_id"
    t.string "operator"
    t.string "value"
    t.string "sort_order"
    t.string "column_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "relationship_attribute_id"
    t.string "value2"
    t.integer "position"
    t.string "value3"
    t.bigint "relationship_attribute2_id"
    t.index ["object_query_view_id"], name: "index_object_query_rules_on_object_query_view_id"
    t.index ["object_type_attribute_id"], name: "index_object_query_rules_on_object_type_attribute_id"
    t.index ["relationship_attribute2_id"], name: "index_object_query_rules_on_relationship_attribute2_id"
    t.index ["relationship_attribute_id"], name: "index_object_query_rules_on_relationship_attribute_id"
  end

  create_table "object_query_views", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "dependency_type"
    t.integer "dependency_id"
    t.index ["dependency_type", "dependency_id"], name: "index_object_query_views_on_dependency_type_and_dependency_id"
  end

  create_table "object_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_id"
    t.bigint "object_type_attribute_id"
    t.string "model_type"
    t.bigint "model_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["model_type", "model_id"], name: "index_object_relationships_on_model"
    t.index ["object_id"], name: "index_object_relationships_on_object_id"
    t.index ["object_type_attribute_id"], name: "index_object_relationships_on_object_type_attribute_id"
  end

  create_table "object_type_attribute_paths", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "data_attribute_id"
    t.bigint "calculation_id"
    t.json "relationship_references"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "key"
    t.json "mapping"
    t.string "column_name"
    t.string "aggregation"
    t.index ["calculation_id"], name: "index_object_type_attribute_paths_on_calculation_id"
    t.index ["data_attribute_id"], name: "index_object_type_attribute_paths_on_data_attribute_id"
  end

  create_table "object_type_attribute_selectable_values", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.json "value"
    t.string "color"
    t.bigint "object_type_attribute_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "archived", default: false
    t.integer "position"
    t.bigint "object_type_variant_id"
    t.boolean "locked", default: true, null: false
    t.json "hidden"
    t.index ["object_type_attribute_id"], name: "index_object_type_attr_selectable_values_on_object_type_attr_id"
    t.index ["object_type_variant_id"], name: "object_type_attribute_selectable_values_on_variant_id"
  end

  create_table "object_type_attribute_validations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_attribute_id"
    t.string "validation_type"
    t.json "value"
    t.bigint "other_object_type_attribute_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_type_attribute_id"], name: "index_object_type_attribute_validations_on_ota_id"
    t.index ["other_object_type_attribute_id"], name: "index_object_type_attribute_validations_on_other_ota_id"
  end

  create_table "object_type_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.string "key"
    t.string "data_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "searchable_for_recommendations", default: false, null: false
    t.json "allowed_values"
    t.string "relationship_kind"
    t.string "target_class"
    t.bigint "target_object_type_id"
    t.boolean "can_update", default: true, null: false
    t.json "default_value"
    t.string "target_column_name"
    t.bigint "target_object_type_attribute_id"
    t.boolean "show_colored_options"
    t.bigint "target_team_id"
    t.bigint "object_type_variant_id"
    t.boolean "allow_variant_options", default: false, null: false
    t.boolean "archived", default: false, null: false
    t.boolean "inverse", default: false, null: false
    t.boolean "locked", default: true, null: false
    t.json "hidden"
    t.string "field_identifier"
    t.boolean "calculated", default: false, null: false
    t.string "inverse_relationship_kind"
    t.index "`object_type_id`, (case when (`archived` = 1) then NULL else `field_identifier` end)", name: "index_otas_on_field_identifier", unique: true
    t.index ["object_type_id", "key"], name: "index_object_type_attributes_on_object_type_id_and_key", unique: true
    t.index ["object_type_variant_id"], name: "index_object_type_attributes_on_object_type_variant_id"
    t.index ["target_object_type_attribute_id"], name: "idx_object_type_attributes_on_target_object_type_attribute_id"
    t.index ["target_object_type_id"], name: "index_object_type_attributes_on_target_object_type_id"
    t.index ["target_team_id"], name: "index_object_type_attributes_on_target_team_id"
  end

  create_table "object_type_descriptions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "section"
    t.bigint "object_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_type_id", "section"], name: "index_object_type_descriptions_on_object_type_id_and_section", unique: true
    t.index ["object_type_id"], name: "index_object_type_descriptions_on_object_type_id"
  end

  create_table "object_type_groups", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
  end

  create_table "object_type_object_type_group_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.bigint "object_type_group_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_type_group_id"], name: "index_object_type_object_type_group_rel_on_object_type_group_id"
    t.index ["object_type_id", "object_type_group_id"], name: "index_object_type_object_type_group_rel", unique: true
    t.index ["object_type_id"], name: "index_object_type_object_type_group_rel_on_object_type_id"
  end

  create_table "object_type_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_attribute_id", null: false
    t.bigint "inverse_object_type_attribute_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["inverse_object_type_attribute_id"], name: "index_object_type_relationships_on_inverse_attribute"
    t.index ["object_type_attribute_id"], name: "index_object_type_relationships_on_object_type_attribute_id"
  end

  create_table "object_type_role_team_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_role_id"
    t.bigint "team_id"
    t.bigint "object_scope_team_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "object_type_variant_id"
    t.index ["object_scope_team_id"], name: "index_object_type_role_team_rel_on_object_scope_team"
    t.index ["object_type_role_id"], name: "index_object_type_role_team_relationships_on_object_type_role_id"
    t.index ["object_type_variant_id"], name: "index_object_type_role_team_relationships_on_variant_id"
    t.index ["team_id"], name: "index_object_type_role_team_relationships_on_team_id"
  end

  create_table "object_type_roles", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.bigint "role_attribute_id"
    t.boolean "through_attribute", default: false, null: false
    t.boolean "through_team", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "role_type"
    t.boolean "can_create", default: false, null: false
    t.boolean "can_edit", default: false, null: false
    t.boolean "can_delete", default: false, null: false
    t.bigint "object_type_variant_id"
    t.index ["object_type_id"], name: "index_object_type_roles_on_object_type_id"
    t.index ["object_type_variant_id"], name: "index_object_type_roles_on_object_type_variant_id"
    t.index ["role_attribute_id"], name: "index_object_type_roles_on_role_attribute_id"
  end

  create_table "object_type_team_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "team_id"
    t.bigint "object_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "published", default: false, null: false
    t.boolean "publish_to_descendants", default: false, null: false
    t.index ["object_type_id"], name: "index_object_type_team_relationships_on_object_type_id"
    t.index ["team_id", "object_type_id"], name: "index_object_type_team_rel_on_team_id_and_object_type_id", unique: true
    t.index ["team_id"], name: "index_object_type_team_relationships_on_team_id"
  end

  create_table "object_type_user_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.bigint "user_id"
    t.datetime "last_visited_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "owner", default: false, null: false
    t.bigint "view_id"
    t.index ["object_type_id"], name: "index_object_type_user_relationships_on_object_type_id"
    t.index ["user_id", "object_type_id"], name: "index_user_object_type", unique: true
    t.index ["user_id"], name: "index_object_type_user_relationships_on_user_id"
    t.index ["view_id"], name: "index_object_type_user_relationships_on_view_id"
  end

  create_table "object_type_variants", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id", null: false
    t.bigint "team_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_type_id", "team_id"], name: "index_object_type_variants_on_object_type_id_and_team_id", unique: true
    t.index ["object_type_id"], name: "index_object_type_variants_on_object_type_id"
    t.index ["team_id"], name: "index_object_type_variants_on_team_id"
  end

  create_table "object_types", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "color"
    t.string "icon"
    t.bigint "recommended_object_type_attribute_id"
    t.bigint "user_id"
    t.bigint "max_local_id", default: 0, null: false
    t.bigint "title_object_type_attribute_id"
    t.index ["recommended_object_type_attribute_id"], name: "index_object_types_on_recommended_object_type_attribute_id"
    t.index ["title_object_type_attribute_id"], name: "title_object_type_attribute_id"
    t.index ["user_id"], name: "index_object_types_on_user_id"
  end

  create_table "objects", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.bigint "team_id"
    t.integer "comments_count", default: 0, null: false
    t.bigint "last_updated_by_id"
    t.bigint "local_id", null: false
    t.index ["last_updated_by_id"], name: "index_objects_on_last_updated_by_id"
    t.index ["object_type_id", "local_id"], name: "index_local_id_on_objects", unique: true
    t.index ["object_type_id"], name: "index_objects_on_object_type_id"
    t.index ["team_id"], name: "index_objects_on_team_id"
    t.index ["user_id"], name: "index_objects_on_user_id"
  end

  create_table "qr_codes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "code", null: false
    t.bigint "object_type_id"
    t.json "object_attributes"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "object_id"
    t.integer "qr_type"
    t.index ["code"], name: "index_qr_codes_on_code", unique: true
    t.index ["object_id"], name: "index_qr_codes_on_object_id"
    t.index ["object_type_id"], name: "index_qr_codes_on_object_type_id"
  end

  create_table "record_pickers", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "interface_element_id", null: false
    t.bigint "object_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "object_type_attribute_id"
    t.string "column_name"
    t.index ["interface_element_id"], name: "index_record_pickers_on_interface_element_id"
    t.index ["object_type_attribute_id"], name: "index_record_pickers_on_object_type_attribute_id"
    t.index ["object_type_id"], name: "index_record_pickers_on_object_type_id"
  end

  create_table "sections", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "position"
    t.bigint "interface_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_id"], name: "index_sections_on_interface_id"
  end

  create_table "seen_announcements", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "announcement_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["announcement_id"], name: "index_seen_announcements_on_announcement_id"
    t.index ["user_id", "announcement_id"], name: "index_seen_announcements_on_user_id_and_announcement_id", unique: true
  end

  create_table "shortcuts", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.json "target_info"
    t.string "dependency_type", null: false
    t.bigint "dependency_id", null: false
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.bigint "interface_element_link_tab_id"
    t.index ["dependency_type", "dependency_id"], name: "index_shortcuts_on_dependency"
    t.index ["interface_element_link_tab_id"], name: "index_shortcuts_on_interface_element_link_tab_id"
    t.index ["user_id"], name: "index_shortcuts_on_user_id"
  end

  create_table "snapshots", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.bigint "user_id"
    t.json "object"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.index ["item_type", "item_id"], name: "index_snapshots_on_item"
    t.index ["user_id"], name: "index_snapshots_on_user_id"
  end

  create_table "teams", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name"
    t.bigint "parent_id"
    t.integer "lft"
    t.integer "rgt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "team_type", default: 0, null: false
    t.integer "users_count", default: 0
    t.index ["lft"], name: "index_teams_on_lft"
    t.index ["parent_id"], name: "index_teams_on_parent_id"
    t.index ["rgt"], name: "index_teams_on_rgt"
  end

  create_table "thumbnail_attributes", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "thumbnail_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "row"
    t.integer "column"
    t.bigint "object_type_attribute_id"
    t.string "column_name"
    t.string "date_time_display_mode"
    t.boolean "collapsed", default: false, null: false
    t.integer "width"
    t.json "options"
    t.index ["object_type_attribute_id"], name: "index_thumbnail_attributes_on_object_type_attribute_id"
    t.index ["thumbnail_id"], name: "index_thumbnail_attributes_on_thumbnail_id"
  end

  create_table "thumbnails", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "object_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "thumbnail_type", default: 0, null: false
    t.boolean "show_icon", default: true, null: false
    t.boolean "show_attribute_names", default: true, null: false
    t.integer "min_width"
    t.boolean "default", default: false, null: false
    t.boolean "has_header", default: false, null: false
    t.bigint "visualisation_id"
    t.bigint "visualisation_attribute_id"
    t.bigint "form_element_id"
    t.bigint "background_attribute_id"
    t.boolean "collapsible", default: false, null: false
    t.boolean "show_background_image", default: true, null: false
    t.index "`object_type_id`, `thumbnail_type`, (case when (`default` = true) then true else NULL end)", name: "unique_index_on_thumbnail_ot_and_type_and_default", unique: true
    t.index "`visualisation_id`, (coalesce(`visualisation_attribute_id`,0))", name: "index_thumbnails_on_visualisation_id", unique: true
    t.index ["background_attribute_id"], name: "index_thumbnails_on_background_attribute_id"
    t.index ["form_element_id"], name: "index_thumbnails_on_form_element_id"
    t.index ["object_type_id"], name: "index_thumbnails_on_object_type_id"
    t.index ["visualisation_attribute_id"], name: "index_thumbnails_on_visualisation_attribute_id"
  end

  create_table "tokens", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "kind"
    t.string "key"
    t.datetime "expires_at"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_tokens_on_key", unique: true
    t.index ["kind", "user_id"], name: "index_tokens_on_kind_and_user_id", unique: true
    t.index ["user_id"], name: "index_tokens_on_user_id"
  end

  create_table "user_interface_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "interface_id"
    t.boolean "owner", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["interface_id", "user_id"], name: "index_user_interface_relationships_on_interface_id_and_user_id", unique: true
    t.index ["interface_id"], name: "index_user_interface_relationships_on_interface_id"
    t.index ["user_id"], name: "index_user_interface_relationships_on_user_id"
  end

  create_table "user_team_join_requests", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "team_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["team_id"], name: "index_user_team_join_requests_on_team_id"
    t.index ["user_id", "team_id"], name: "index_user_team_join_requests_on_user_id_and_team_id", unique: true
    t.index ["user_id"], name: "index_user_team_join_requests_on_user_id"
  end

  create_table "user_team_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "team_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin", default: false, null: false
    t.index ["team_id", "user_id"], name: "index_user_team_relationships_on_team_id_and_user_id", unique: true
    t.index ["team_id"], name: "index_user_team_relationships_on_team_id"
    t.index ["user_id"], name: "index_user_team_relationships_on_user_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "email", null: false
    t.string "avatar"
    t.string "sso_provider"
    t.string "sso_uid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "invited", default: false
    t.string "language"
    t.boolean "admin", default: false, null: false
    t.string "password_digest"
    t.json "settings"
    t.boolean "email_verified", default: false, null: false
    t.boolean "no_session", default: false, null: false
    t.boolean "disabled", default: false, null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["sso_provider", "sso_uid"], name: "index_users_on_sso_provider_and_sso_uid", unique: true
  end

  create_table "versions", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "item_type", limit: 191, null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object", size: :long
    t.datetime "created_at", precision: nil
    t.text "object_changes", size: :long
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "view_object_type_relationships", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.bigint "view_id"
    t.bigint "object_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_type_id"], name: "index_view_object_type_relationships_on_object_type_id"
    t.index ["view_id"], name: "index_view_object_type_relationships_on_view_id"
  end

  create_table "views", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "name"
    t.boolean "default", default: false
    t.bigint "object_type_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "template"
    t.bigint "object_type_variant_id"
    t.boolean "include_records_from_subteams", default: false, null: false
    t.boolean "include_shared_records", default: false, null: false
    t.boolean "all_view", default: false, null: false
    t.bigint "interface_element_id"
    t.boolean "configurable_template", default: true, null: false
    t.bigint "filter_attribute_id"
    t.json "filter_properties"
    t.boolean "allow_create_new_object", default: false, null: false
    t.index ["filter_attribute_id"], name: "index_views_on_filter_attribute_id"
    t.index ["interface_element_id"], name: "index_views_on_interface_element_id"
    t.index ["object_type_id"], name: "index_views_on_object_type_id"
    t.index ["object_type_variant_id"], name: "index_views_on_object_type_variant_id"
    t.index ["user_id"], name: "index_views_on_user_id"
  end

  create_table "visualisations", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "view_type"
    t.json "layout"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "view_id"
    t.index ["view_id"], name: "index_visualisations_on_view_id"
  end

  create_table "widgets", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.text "html"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "interface_element_id"
    t.integer "widget_type", default: 0, null: false
    t.index ["interface_element_id"], name: "index_widgets_on_interface_element_id"
  end

  add_foreign_key "access_tokens", "users"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "connections", "users"
  add_foreign_key "interfaces", "users"
  add_foreign_key "object_type_variants", "object_types"
  add_foreign_key "object_type_variants", "teams"
  add_foreign_key "sections", "interfaces"
end
