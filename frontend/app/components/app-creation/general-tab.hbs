{{#if this.data}}
  {{! Short Description Section }}
  <div class="mb-4">
    <h4 class="fw-bold mb-3">{{t "app_creation.short_description"}}</h4>
    <p>{{this.shortProcessDescription}}</p>
  </div>

  {{! Summary Section }}
  <div class="mb-4">
    <h4 class="fw-bold mb-3">{{t "app_creation.summary"}}</h4>
    <div class="assistant-response form-control-plaintext">
      <Markdown @markdown={{this.processSummary}} />
    </div>
  </div>

  {{! Key Roles Section }}
  <div class="mb-4">
    <h4 class="fw-bold mb-3">{{t "app_creation.key_roles"}}</h4>
    <ul>
      {{#each this.keyRoles as |role|}}
        <li>
          {{role}}
        </li>
      {{/each}}
    </ul>
  </div>

  {{! Step-by-step Process Description Section }}
  <div class="mb-4">
    <h4 class="fw-bold mb-3">{{t "app_creation.step_by_step_process_description"}}</h4>
    <div class="mt-4">
      <div class="row fw-bold border-bottom border-dark pb-2 mb-2">
        <div class="col-2">{{t "app_creation.step"}}</div>
        <div class="col-6">{{t "app_creation.action"}}</div>
        <div class="col-4">{{t "app_creation.actor"}}</div>
      </div>
      {{#each this.processSteps as |step|}}
        <div class="row mb-2">
          <div class="col-2">{{step.step}}</div>
          <div class="col-6">{{step.description}}</div>
          <div class="col-4">{{step.actor}}</div>
        </div>
      {{/each}}
    </div>
  </div>
{{/if}}
