import Component from '@glimmer/component';
import { AssistantChatSession, ENTRY_TYPE, ToolContent } from 'frontend/utils/assistant-chat-session';
import { cached } from '@glimmer/tracking';

type SummaryToolOutput = {
  short_process_description: string;
  process_summary: string;
  key_roles: Array<string>;
  process_steps: Array<{ step: string, description: string, actor: string }>;
};

interface GeneralTabSignature {
  Args: {
    assistantChatSession: AssistantChatSession;
  };
}

const SUMMARY_TOOL_NAME = 'assistant_tools_describe_app_tool__execute';

export default class GeneralTabComponent extends Component<GeneralTabSignature> {
  @cached
  get data() {
    const chatEntries = [...this.args.assistantChatSession.entries];
    const summaryToolEntry = chatEntries.reverse().find((entry) => {
      return entry.type == ENTRY_TYPE.TOOL &&
        (entry.content as ToolContent).tool_name == SUMMARY_TOOL_NAME;
    });
    if (!summaryToolEntry) return undefined;

    return (summaryToolEntry.content as ToolContent).output.data as SummaryToolOutput;
  }

  get shortProcessDescription() {
    return this.data?.short_process_description;
  }

  get processSummary() {
    return this.data?.process_summary;
  }

  get keyRoles() {
    return this.data?.key_roles;
  }

  get processSteps() {
    return this.data?.process_steps;
  }
}
