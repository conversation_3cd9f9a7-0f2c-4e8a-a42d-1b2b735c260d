{{#each this.objectTypes as |objectType|}}
  <div class="mt-4 p-4 rounded border border-2">
    <h5 class="fw-bold">{{t "object_type"}}: {{objectType.name}}</h5>

    <div
      class="d-grid gap-2 mt-4"
      style={{style "grid-template-columns" "repeat(3, auto)"}}
    >
      <div class="fw-bold">{{t "app_creation.field_or_relationship"}}</div>
      <div class="fw-bold">{{t "app_creation.description"}}</div>
      <div class="fw-bold">{{t "type"}}</div>

      <div class="border-bottom" style={{style "grid-column" "1 / 4"}}></div>
      {{#each objectType.attributes as |field|}}
        <div>{{field.name}}</div>
        <div>{{field.description}}</div>
        <div>
          {{#if field.relationshipTarget}}
            <span class="fw-bold">{{t "app_creation.relationship_to"}} {{field.relationshipTarget.name}}</span>
          {{else}}
            {{field.dataType}}
          {{/if}}
        </div>
      {{/each}}
    </div>
  </div>
{{/each}}
