import { IntlService } from 'ember-intl';
import { service } from '@ember/service';
import Component from "@glimmer/component";
import { cached } from '@glimmer/tracking';
import { AssistantChatSession, ENTRY_TYPE, ToolContent } from 'frontend/utils/assistant-chat-session';
import { AppCreation } from 'frontend/utils/assistant/app-creation';

interface ObjectTypesDataSignature {
  Args: {
    assistantChatSession: AssistantChatSession;
  };
}

const SUMMARY_TOOL_NAME = 'assistant_tools_suggest_data_model_tool__execute';

export default class ObjectTypesDataComponent extends Component<ObjectTypesDataSignature> {
  @service
  declare intl: IntlService;

  get objectTypes() {
    if (!this.data) return;

    const objectsData = (this.data as any).objects;
    if (!objectsData) return undefined;

    const locale = this.intl.primaryLocale ?? 'en';
    return new AppCreation(locale, objectsData).objectTypes;
  }

  @cached
  get data() {
    const chatEntries = [...this.args.assistantChatSession.entries];
    const summaryToolEntry = chatEntries.reverse().find((entry) => {
      return entry.type == ENTRY_TYPE.TOOL &&
        (entry.content as ToolContent).tool_name == SUMMARY_TOOL_NAME;
    });
    if (!summaryToolEntry) return undefined;

    return (summaryToolEntry.content as ToolContent).output.data;
  }
}
