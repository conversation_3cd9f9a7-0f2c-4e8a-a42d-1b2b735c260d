<div class="bg-body-tertiary h-100 mini-scrollbar overflow-auto d-flex" {{did-insert this.setupScrollContainer}} {{did-update this.scrollToBottom @assistantChatSession.entries}}>
  <div class="d-flex flex-column justify-content-end flex-grow-1 mt-auto">
    <div class="px-2">
      {{#each @assistantChatSession.entries as |entry|}}
        <AssistantChatEntry @entry={{entry}} />
      {{/each}}
      {{#if this.isProcessingPrompt}}
        <div class="d-flex">
          <div class="py-3">
            <div class="d-flex align-items-center">
              <i class="fa-regular fa-rocket-launch fa-beat-fade me-2"></i>
              <span>{{t "assistant.chat_analysis_in_progress"}}</span>
            </div>
          </div>
        </div>
      {{/if}}
    </div>

    <div class="position-sticky bottom-0 border-top bg-body-tertiary py-2">
      <div class="px-2">
        <div class="d-flex align-items-center mb-3">
          <i class="fal fa-sparkles me-2"></i>
          <h6 class="mb-0">{{t "assistant.chat_houston_ai"}}</h6>
        </div>
        <div class="mb-3">
          <div class="text-muted small-font">{{t "assistant.chat_adjustments_question"}}</div>
        </div>
        <div class="form-border my-2">
          <textarea
            class="form-control rounded-3"
            placeholder="{{t "assistant.chat_prompt_placeholder"}}"
            rows="3"
            value={{this.currentPrompt}}
            {{autosize}}
            {{on "input" this.setCurrentPrompt}}
            {{on "keydown" this.handleKeyDown}}
            disabled={{this.isProcessingPrompt}}
          ></textarea>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <div class="text-muted small-font">{{t "assistant.chat_prompt_may_update_tabs"}}</div>
          <button
            type="button"
            class="btn btn-primary btn-sm"
            {{on "click" this.submitPrompt}}
            disabled={{this.isSubmitDisabled}}
          >
            <i class="fal fa-sparkles"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
