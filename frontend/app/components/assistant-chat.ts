import { action } from '@ember/object';
import { tracked } from '@glimmer/tracking';
import Component from '@glimmer/component';
import { schedule } from '@ember/runloop';
import { AssistantChatSession } from 'frontend/utils/assistant-chat-session';
import { handleError } from 'frontend/utils/handle-error';

interface AssistantChatSignature {
  Args: {
    assistantChatSession: AssistantChatSession;
  };
}

export default class AssistantChatComponent extends Component<AssistantChatSignature> {
  @tracked
  currentPrompt = '';

  private scrollContainer: HTMLElement | null = null;

  get isProcessingPrompt() {
    return this.args.assistantChatSession.isProcessing;
  }

  get isSubmitDisabled() {
    return this.isProcessingPrompt || this.currentPrompt.trim().length === 0;
  }

  @action
  setCurrentPrompt(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    this.currentPrompt = target.value;
  }

  @action
  handleKeyDown(event: KeyboardEvent) {
    // Submit on Enter, but allow Shift+Enter for line breaks
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (!this.isSubmitDisabled) {
        this.submitPrompt();
      }
    }
  }

  @action
  setupScrollContainer(element: HTMLElement) {
    this.scrollContainer = element;
  }

  @action
  scrollToBottom() {
    if (this.scrollContainer) {
      schedule('afterRender', () => {
        if (this.scrollContainer) {
          this.scrollContainer.scrollTop = this.scrollContainer.scrollHeight;
        }
      });
    }
  }

  @action
  async submitPrompt() {
    if (this.isSubmitDisabled) return;

    const userMessage = this.currentPrompt.trim();
    if (!userMessage) return;

    this.currentPrompt = '';
    try {
      await this.args.assistantChatSession.addUserMessage(userMessage);
    } catch (error) {
      this.currentPrompt = userMessage;
      handleError(error);
    }
  }
}
