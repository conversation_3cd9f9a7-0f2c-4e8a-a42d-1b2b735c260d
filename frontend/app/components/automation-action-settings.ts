import { service } from '@ember/service';
import Component from '@glimmer/component';
import IntlService from 'ember-intl/services/intl';
import AutomationActionModel, { ACTION_TYPE } from 'frontend/models/automation-action';

interface AutomationActionSettingsSignature {
  Args: {
    action: AutomationActionModel;
  };
}

export default class AutomationActionSettingsComponent extends Component<AutomationActionSettingsSignature> {
  @service
  declare intl: IntlService;

  get selectedActionLabel() {
    const actionType = this.args.action.actionType;

    switch (actionType) {
    case ACTION_TYPE.MAIL: return this.intl.t("automation.send_email");
    case ACTION_TYPE.AI: return this.intl.t("automation.fill_fields_ai");
    case ACTION_TYPE.UPDATE_OBJECT: return this.intl.t("automation.update_record");
    case ACTION_TYPE.WEBHOOK: return this.intl.t("automation.send_webhook");
    }
    return undefined;
  }

  get isMailAction() {
    return this.args.action.actionType == ACTION_TYPE.MAIL && this.args.action.automationMailAction;
  }

  get isAiAction() {
    return this.args.action.actionType == ACTION_TYPE.AI && this.args.action.automationAiAction;
  }

  get isUpdateObjectAction() {
    return this.args.action.actionType == ACTION_TYPE.UPDATE_OBJECT && this.args.action.automationUpdateObjectAction;
  }

  get isWebhookAction() {
    return this.args.action.actionType == ACTION_TYPE.WEBHOOK && this.args.action.automationWebhookAction;
  }
}
