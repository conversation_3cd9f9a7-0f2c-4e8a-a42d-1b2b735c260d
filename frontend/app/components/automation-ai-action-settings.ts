import Component from '@glimmer/component';
import { action } from '@ember/object';
import { service } from "@ember/service";
import Viewpoint from "frontend/services/viewpoint";
import { VariantedObjectTypeAttribute, findObjectTypeVariantFor, variantFor } from "frontend/utils/variants";
import AutomationAiActionAttributeModel, { AI_ATTRIBUTE_PARAM_TYPE } from "frontend/models/automation-ai-action-attribute";
import Store from "@ember-data/store";
import ObjectTypeField from "frontend/types/ObjectTypeField";
import ObjectTypeProperty from "frontend/utils/object-type-property";
import ObjectTypeAttributeModel, { DATA_TYPE } from "frontend/models/object-type-attribute";
import AutomationAiActionModel from "frontend/models/automation-ai-action";
import { tracked } from "@glimmer/tracking";
import Owner from '@ember/owner';

interface AutomationAiActionSettingsSignature {
  Args: {
    automationAiAction: AutomationAiActionModel;
  };
}

export default class AutomationAiActionSettings extends Component<AutomationAiActionSettingsSignature> {
  @service
  declare viewpoint: Viewpoint;

  @service
  declare store: Store;

  @tracked showAdvancedSettings = false;

  @tracked
  selectedRelationshipAttributes: Array<ObjectTypeAttributeModel>;

  constructor(owner: Owner, args: AutomationAiActionSettingsSignature['Args']) {
    super(owner, args);

    this.selectedRelationshipAttributes = this.args.automationAiAction.automationAiActionAttributes.reduce((attributes: Array<ObjectTypeAttributeModel>, attr) => {
      if (attr.relationshipAttribute && !attributes.includes(attr.relationshipAttribute)) {
        attributes.push(attr.relationshipAttribute);
      }
      return attributes;
    }, []);
  }

  get selectedInputFields() {
    let selectedAttributeIds = this.args.automationAiAction.automationAiActionAttributes.filter((attr) => attr.paramType === AI_ATTRIBUTE_PARAM_TYPE.INPUT).map((attr) => {
      return attr.relationshipAttribute?.id || attr.objectTypeAttribute.id;
    });

    selectedAttributeIds = selectedAttributeIds.concat(this.selectedRelationshipAttributes.map((attr) => attr.id));
    return this.variantedObjectType.fields.filter((field) => selectedAttributeIds.includes(field.id));
  }

  get selectedOutputFields() {
    const selectedAttributeIds = this.args.automationAiAction.automationAiActionAttributes.filter((attr) => attr.paramType === AI_ATTRIBUTE_PARAM_TYPE.OUTPUT).map((attr) => {
      return attr.objectTypeAttribute.id;
    });
    return this.variantedObjectType.fields.filter((field) => selectedAttributeIds.includes(field.id));
  }

  get selectedInputAiAttributes() {
    return this.args.automationAiAction.automationAiActionAttributes.filter((attr) => {
      return attr.paramType == AI_ATTRIBUTE_PARAM_TYPE.INPUT && !attr.relationshipAttribute;
    });
  }

  get selectedOutputAiAttributes() {
    return this.args.automationAiAction.automationAiActionAttributes.filter((attr) => attr.paramType == AI_ATTRIBUTE_PARAM_TYPE.OUTPUT);
  }

  get variantedObjectType() {
    const team = this.viewpoint.selectedTeam;
    const objectTypeVariant = findObjectTypeVariantFor(team, this.args.automationAiAction.automationAction.automation.objectType);
    return variantFor(this.args.automationAiAction.automationAction.automation.objectType, objectTypeVariant);
  }

  @action
  setSystemMessage(event: GenericEvent<HTMLInputElement>) {
    this.args.automationAiAction.systemMessage = event.target.value;
    this.args.automationAiAction.automationAction.isDirty = true;
  }

  @action
  removeAiActionAttribute(attribute: AutomationAiActionAttributeModel) {
    this.args.automationAiAction.automationAiActionAttributes.removeObject(attribute);
    attribute.deleteRecord();
    this.args.automationAiAction.automationAction.isDirty = true;
  }

  @action
  removeSelectedRelationshipAttribute(field: ObjectTypeAttributeModel) {
    this.selectedRelationshipAttributes.removeObject(field);
    this.args.automationAiAction.automationAiActionAttributes.slice().forEach((attribute) => {
      if (attribute.relationshipAttribute == field) {
        this.removeAiActionAttribute(attribute);
      }
    });
  }

  @action
  setAiAttributeDescription(attribute: AutomationAiActionAttributeModel, event: GenericEvent<HTMLInputElement>) {
    attribute.description = event.target.value;
    this.args.automationAiAction.automationAction.isDirty = true;
  }

  @action
  addInputField(field: Array<VariantedObjectTypeAttribute>) {
    field.forEach((field) => {
      if (this.selectedInputFields.includes(field)) return;

      if (field.dataType == DATA_TYPE.RELATIONSHIP && field.targetClass == "Object") {
        this.selectedRelationshipAttributes.pushObject(field.original);
        return;
      }

      const attribute = this.store.createRecord('automation-ai-action-attribute', {
        automationAiAction: this.args.automationAiAction,
        paramType: AI_ATTRIBUTE_PARAM_TYPE.INPUT,
        objectTypeAttribute: field.original
      });

      this.args.automationAiAction.automationAiActionAttributes.push(attribute);
      this.args.automationAiAction.automationAction.isDirty = true;
    });
  }

  @action
  addOutputField(field: Array<VariantedObjectTypeAttribute>) {
    field.forEach((field: VariantedObjectTypeAttribute) => {
      if (this.selectedOutputFields.includes(field)) return;
      this.args.automationAiAction.automationAiActionAttributes.push(this.store.createRecord('automation-ai-action-attribute', {
        automationAiAction: this.args.automationAiAction,
        objectTypeAttribute: field.original,
        paramType: AI_ATTRIBUTE_PARAM_TYPE.OUTPUT
      }));
      this.args.automationAiAction.automationAction.isDirty = true;
    });
  }

  @action
  attributeInputFilter(field: ObjectTypeField) {
    if (field instanceof ObjectTypeProperty) {
      return false;
    }
    if ([DATA_TYPE.ATTACHMENT].includes(field.dataType)) return false;

    return true;
  }

  @action
  attributeOutputFilter(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    if (field instanceof ObjectTypeProperty || field.dataType !== DATA_TYPE.STRING || field.calculated) {
      return false;
    }

    return true;
  }

  @action
  toggleAdvancedSettings() {
    this.showAdvancedSettings = !this.showAdvancedSettings;
  }
}
