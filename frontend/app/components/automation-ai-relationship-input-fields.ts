import { action } from '@ember/object';
import { service } from '@ember/service';
import Component from '@glimmer/component';
import AutomationAiActionAttributeModel, { AI_ATTRIBUTE_PARAM_TYPE } from 'frontend/models/automation-ai-action-attribute';
import ObjectTypeAttributeModel, { DATA_TYPE } from 'frontend/models/object-type-attribute';
import Viewpoint from 'frontend/services/viewpoint';
import ObjectTypeField from 'frontend/types/ObjectTypeField';
import ObjectTypeProperty from 'frontend/utils/object-type-property';
import { findObjectTypeVariantFor, VariantedObjectTypeAttribute, variantFor } from 'frontend/utils/variants';
import Store from "@ember-data/store";
import AutomationAiActionModel from 'frontend/models/automation-ai-action';
import Owner from '@ember/owner';
import { loadFormTabs } from 'frontend/utils/form-tab-loader';

interface AutomationAiRelationshipInputFieldsSignature {
  Args: {
    relationshipAttribute: ObjectTypeAttributeModel;
    removeAiActionAttribute: (attr: AutomationAiActionAttributeModel) => void;
    setAiAttributeDescription: (attr: AutomationAiActionAttributeModel) => void;
    removeSelectedRelationshipAttribute: (attribute: ObjectTypeAttributeModel) => void;
    automationAiAction: AutomationAiActionModel;
  };
}

export default class AutomationAiRelationshipInputFieldsComponent extends Component<AutomationAiRelationshipInputFieldsSignature> {
  @service
  declare viewpoint: Viewpoint;

  @service
  declare store: Store;

  get inputAttributes() {
    return this.args.automationAiAction.automationAiActionAttributes.filter((attr) => {
      return attr.relationshipAttribute?.id == this.args.relationshipAttribute.id;
    });
  }

  get selectedInputFields() {
    const otaIds = this.inputAttributes.map((attr) => attr.objectTypeAttribute.id);

    return this.variantedObjectType?.fields.filter((field) => otaIds.includes(field?.id));
  }

  get variantedObjectType() {
    if (!this.args.relationshipAttribute.targetObjectType) return;

    const team = this.viewpoint.selectedTeam;
    const objectTypeVariant = findObjectTypeVariantFor(team, this.args.relationshipAttribute.targetObjectType);
    return variantFor(this.args.relationshipAttribute.targetObjectType, objectTypeVariant);
  }

  constructor(owner: Owner, args: AutomationAiRelationshipInputFieldsSignature['Args']) {
    super(owner, args);

    if (this.variantedObjectType) loadFormTabs(this.store, this.variantedObjectType.id);
  }

  @action
  attributeInputFilter(field: ObjectTypeField) {
    if (field instanceof ObjectTypeProperty) {
      return false;
    }
    if ([DATA_TYPE.ATTACHMENT, DATA_TYPE.RELATIONSHIP].includes(field.dataType)) return false;

    return true;
  }

  @action
  addInputField(fields: Array<VariantedObjectTypeAttribute>) {
    fields.forEach((field) => {
      if (this.selectedInputFields?.includes(field)) return;

      const attribute = this.store.createRecord('automation-ai-action-attribute', {
        automationAiAction: this.args.automationAiAction,
        paramType: AI_ATTRIBUTE_PARAM_TYPE.INPUT,
        objectTypeAttribute: field.original,
        relationshipAttribute: this.args.relationshipAttribute
      });

      this.args.automationAiAction?.automationAiActionAttributes.push(attribute);
      this.args.automationAiAction.automationAction.isDirty = true;
    });
  }
}
