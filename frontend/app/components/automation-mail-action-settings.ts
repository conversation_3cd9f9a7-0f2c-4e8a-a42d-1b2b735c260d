import Store from '@ember-data/store';
import { action } from '@ember/object';
import { service } from '@ember/service';
import Component from '@glimmer/component';
import AutomationMailActionAttributeModel from 'frontend/models/automation-mail-action-attribute';
import ObjectTypeRoleModel from 'frontend/models/object-type-role';
import User from 'frontend/models/user';
import ModalService from 'frontend/services/modal';
import ObjectTypeField from 'frontend/types/ObjectTypeField';
import { trackedFunction } from 'reactiveweb/function';
import AddRecipientModal from 'frontend/utils/modals/add-recipient-modal';
import ObjectTypeProperty, { PROPERTIES } from 'frontend/utils/object-type-property';
import { VariantedFormElement, VariantedFormTab, VariantedObjectTypeAttribute, findObjectTypeVariantFor, variantFor } from 'frontend/utils/variants';
import Viewpoint from 'frontend/services/viewpoint';
import { tracked } from '@glimmer/tracking';
import AutomationMailActionModel from 'frontend/models/automation-mail-action';
import AutomationMailActionMailAddressAttributeModel from 'frontend/models/automation-mail-action-address-attribute';
import ObjectTypeAttributeModel, { DATA_TYPE } from 'frontend/models/object-type-attribute';
import { IntlService } from 'ember-intl';

interface AutomationMailActionSettingsSignature {
  Args: {
    automationMailAction: AutomationMailActionModel;
  };
}

export default class AutomationMailActionSettingsComponent extends Component<AutomationMailActionSettingsSignature> {
  @service
  declare intl: IntlService;

  @service
  declare store: Store;

  @service
  declare viewpoint: Viewpoint;

  @service('modal')
  declare modalService: ModalService;

  @tracked
  declare rerenderBodyEdit: boolean;

  @tracked
  declare rerenderSubjectEdit: boolean;

  loadUsersResource = trackedFunction(this, async() => {
    if (!this.args.automationMailAction?.hasMany('selectedUsers').value()) {
      const userIds = this.args.automationMailAction?.hasMany('selectedUsers').ids();
      if (userIds?.length) {
        return await this.store.query('user', { filter: { ids: userIds.join(',') } });
      }
    }
    return this.args.automationMailAction?.selectedUsers;
  });

  get loadedUsers() {
    return this.loadUsersResource.value ?? [];
  }

  get hasUserRecipients() {
    return this.args.automationMailAction?.hasMany('selectedUsers').ids().length;
  }

  get orderedBodyAttributes() {
    return this.args.automationMailAction?.automationMailActionAttributes.filter((attribute) => !attribute.isDeleted)
      .sort((a, b) => a.position > b.position ? 1 : -1);
  }

  get selectedObjectTypeAttributesForBody() {
    const selectedAttributeKeys = this.args.automationMailAction?.automationMailActionAttributes.map((attr) => {
      if (attr.columnName == "team") {
        return "team.name";
      }
      return attr.objectTypeAttribute ? attr.objectTypeAttribute.id : attr.columnName;
    });
    return this.variantedObjectType.fields.filter((field) => selectedAttributeKeys?.includes(field.id));
  }

  get selectedDeliveryOption() {
    if (this.args.automationMailAction.settings?.type == 'attribute') return 'attribute';
    if (this.args.automationMailAction?.settings?.value === 0) {
      return 'immediately';
    }
    return 'delay';
  }

  get variantedObjectType() {
    const team = this.viewpoint.selectedTeam;
    const objectTypeVariant = findObjectTypeVariantFor(team, this.args.automationMailAction.automationAction.automation.objectType);
    return variantFor(this.args.automationMailAction.automationAction.automation.objectType, objectTypeVariant);
  }

  get groupedDynamicAddressAttribute() {
    const isUserRelationShip = (attr: AutomationMailActionMailAddressAttributeModel) => {
      if (attr.columnName) return true;

      return attr.objectTypeAttribute?.targetClass === 'User';
    };
    const isNotUserRelationShip = (attr: AutomationMailActionMailAddressAttributeModel) => !isUserRelationShip(attr);

    const attributes = this.args.automationMailAction.automationMailActionAddressAttributes;
    const addressAttributesDerivedFromField = attributes.filter(isNotUserRelationShip);
    const addressAttributesThroughRelation = attributes.filter(isUserRelationShip);

    const groups = [];

    if (addressAttributesDerivedFromField.length) {
      groups.push({ addressAttributes: addressAttributesDerivedFromField, label: this.intl.t('automation.email_address_from_field') });
    }

    if (addressAttributesThroughRelation.length) {
      groups.push({ addressAttributes: addressAttributesThroughRelation, label: this.intl.t('automation.user_selected_in_a_field') });
    }

    return groups;
  }

  get automationMailActionObjectType() {
    return this.args.automationMailAction.automationAction.automation.objectType;
  }

  @action
  removeUserRecipient(user: User) {
    this.args.automationMailAction?.selectedUsers.removeObject(user);
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  removeRoleRecipient(role: ObjectTypeRoleModel) {
    this.args.automationMailAction?.selectedRoles.removeObject(role);
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  attributeFilter(field: ObjectTypeField) {
    if (field instanceof ObjectTypeProperty && field.key == 'team_id') {
      return false;
    }

    return true;
  }

  @action
  moveAttribute(startAttribute: AutomationMailActionAttributeModel, endattribute: AutomationMailActionAttributeModel) {
    if (!this.orderedBodyAttributes) return;

    const attributes = [...this.orderedBodyAttributes];
    const index = attributes.indexOf(endattribute);
    attributes.removeObject(startAttribute);
    attributes.insertAt(index, startAttribute);
    attributes.forEach((attr, index) => {
      attr.position = index;
    });
    this.args.automationMailAction?.set("automationMailActionAttributes", attributes);
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  selectBodyAttribute(selectedFields: Array<ObjectTypeProperty | VariantedObjectTypeAttribute>) {
    if (!selectedFields.lastObject) return;
    this.addBodyAttribute(selectedFields.lastObject);
  }

  @action
  addBodyAttribute(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    const properties: Dict = {
      automationMailAction: this.args.automationMailAction,
      position: this.orderedBodyAttributes?.length
    };

    if (field instanceof VariantedObjectTypeAttribute) {
      properties.objectTypeAttribute = field.original;
    } else if (field?.key == "team.name") {
      properties.columnName = "team";
    } else {
      properties.columnName = field?.key;
    }
    this.args.automationMailAction?.automationMailActionAttributes.push(this.store.createRecord('automation-mail-action-attribute', properties));
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  removeBodyAttribute(attribute: AutomationMailActionAttributeModel) {
    attribute.unloadRecord();
    this.args.automationMailAction?.automationMailActionAttributes.filter((attr) => !attr.isDeleted)
      .forEach((attr, index) => {
        attr.position = index;
      });
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  setDeliveryTimingType(timing: 'immediately' | 'delay' | 'attribute') {
    if (!this.args.automationMailAction) return;

    if (timing == 'immediately') {
      this.args.automationMailAction.settings.value = 0;
    } else if (timing == 'delay') {
      this.args.automationMailAction.settings.value = 15;
    } else {
      this.args.automationMailAction.settings.value = undefined;
    }

    if (timing == 'attribute') {
      this.args.automationMailAction.settings.type = timing;
    } else {
      this.args.automationMailAction.settings.type = "delay";
    }

    this.args.automationMailAction.objectTypeAttribute = undefined;
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  setDeliveryTiming(number: number | null, event: GenericEvent<HTMLInputElement>) {
    if (!this.args.automationMailAction) return;

    if (number && number >= 0) {
      this.args.automationMailAction.settings.value = Math.min(1440, number);
      this.args.automationMailAction.automationAction.isDirty = true;
    } else {
      event.preventDefault();
    }
  }

  @action
  openAddRecipientModal() {
    if (!this.args.automationMailAction) return;

    const modal = new AddRecipientModal({
      context: {
        mailAction: this.args.automationMailAction,
        variantedObjectType: this.variantedObjectType
      }
    });

    this.modalService.open(modal);
  }

  @action
  addAllAttributeToBody() {
    const sortedFields: Array<VariantedObjectTypeAttribute | ObjectTypeProperty> = [];
    this.variantedObjectType.rootFormTabs.forEach((tab) => {
      this.addTabItemsToFields(tab, sortedFields);
    });

    const fields = sortedFields.concat(PROPERTIES).filter((field) => {
      return this.attributeFilter(field) && !this.selectedObjectTypeAttributesForBody.includes(field);
    });
    fields.forEach((attr) => {
      this.addBodyAttribute(attr);
    });
  }

  addTabItemsToFields(tab: VariantedFormTab, fields: Array<ObjectTypeField>) {
    tab.sortedFormItems.forEach((item) => {
      if (item instanceof VariantedFormElement) {
        if (item.objectTypeAttribute) fields.push(item.objectTypeAttribute);
      } else {
        this.addTabItemsToFields(item, fields);
      }
    });
  }

  @action
  removeAllBodyAttributes() {
    this.args.automationMailAction?.automationMailActionAttributes.slice().forEach((attr) => {
      this.removeBodyAttribute(attr);
    });
  }

  @action
  setBody(body: string) {
    if (this.args.automationMailAction) {
      this.args.automationMailAction.body = body;
      this.args.automationMailAction.automationAction.isDirty = true;
    }
  }

  @action
  setSubject(subject: string) {
    if (this.args.automationMailAction) {
      this.args.automationMailAction.subject = subject;
      this.args.automationMailAction.automationAction.isDirty = true;
    }
  }

  @action
  toggleRerenderBodyEdit() {
    this.rerenderBodyEdit = !this.rerenderBodyEdit;
  }

  @action
  toggleRerenderSubjectEdit() {
    this.rerenderSubjectEdit = !this.rerenderSubjectEdit;
  }

  @action
  removeMailAddressAttribute(attribute: AutomationMailActionMailAddressAttributeModel) {
    this.args.automationMailAction.automationMailActionAddressAttributes.removeObject(attribute);
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  removeStaticMailAddress(mailAddress: string) {
    this.args.automationMailAction.staticMailAddress?.removeObject(mailAddress);
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  deliverAttributeFilter(field: ObjectTypeField) {
    if (field instanceof ObjectTypeAttributeModel) {
      return [DATA_TYPE.DATE, DATA_TYPE.DATETIME].includes(field.dataType);
    }

    return false;
  }

  @action
  selectField(attribute: ObjectTypeAttributeModel) {
    this.args.automationMailAction.objectTypeAttribute = attribute;
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  toggleShowChanges() {
    this.args.automationMailAction.settings.showChanges = !this.args.automationMailAction.settings.showChanges;
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  toggleOnlyPresentFields() {
    this.args.automationMailAction.settings.onlyPresentFields = !this.args.automationMailAction.settings.onlyPresentFields;
    this.args.automationMailAction.automationAction.isDirty = true;
  }

  @action
  toggleOnlyUpdatedFields() {
    this.args.automationMailAction.settings.onlyUpdatedFields = !this.args.automationMailAction.settings.onlyUpdatedFields;
    this.args.automationMailAction.automationAction.isDirty = true;
  }
}
