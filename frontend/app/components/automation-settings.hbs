<div class="automation-settings h-100">
  <div class="mini-scrollbar overflow-auto {{if this.hasSelectedSetting "d-none d-lg-block"}}">
    <div class="bg-body d-flex align-items-center">
      <button type="button" {{on "click" this.unselectAutomation}} class="btn"><i class="fa fa-arrow-left"></i></button>
      <h5 class="mb-0 standard-fontsize fw-bold">{{@automation.name}}</h5>
      <div class="ms-auto user-select-none d-flex align-items-center">
        {{t (if @automation.active "active" "not_active")}}
        {{#if this.ownedByVariant}}
          <ToggleBox class="fa-lg" @checked={{@automation.active}} @toggle={{this.toggleActive}}/>
          {{#if (and this.isDeleteableAutomation @deleteAutomation)}}
            <div class="dropdown px-1">
              <a type="button" data-bs-toggle="dropdown">
                <i class="fa fa-ellipsis text-secondary"/>
              </a>
              <div class="dropdown-menu">
                <button class="dropdown-item" {{on "click" @deleteAutomation}} type="button">
                  <i class="fa fa-trash fa-fw text-danger"/>
                  {{t "delete"}}
                </button>
              </div>
            </div>
          {{/if}}
        {{/if}}
      </div>
    </div>
    <div class="row justify-content-center vertical-line"><div class="py-3 px-0"></div></div>
    <div class="row justify-content-center">
      <span class="col-2 badge rounded-pill text-dark bg-secondary p-2">
        {{t "condition.when"}}...
      </span>
    </div>
    <div class="row justify-content-center vertical-line"><div class="py-3 px-0"></div></div>
    <div {{!-- template-lint-disable no-invalid-interactive --}}
      role="{{if this.ownedByVariant "button"}}"
      class="automation-group offset-2 col-8 rounded bg-body p-2 {{if this.showEventSettings "active" }} {{unless this.ownedByVariant "text-secondary"}}"
      {{on "click" (if this.ownedByVariant (fn this.selectSettings "when") (noop))}}
    >
      <div class="text-primary">
        <i class="fa fa-circle-exclamation"/>
        {{t "automation.event"}}
      </div>
      <div>
        {{#if @automation.hasAutomationTrigger}}
          {{capitalize (t (concat "automation." @automation.automationTrigger.triggerType))}}
        {{else}}
          {{capitalize (t "automation.frontend")}}
        {{/if}}
      </div>
    </div>

    <div class="row justify-content-center vertical-line"><div class="py-3 px-0"></div></div>
    <div class="row justify-content-center">
      <span class="col-2 badge rounded-pill text-dark bg-secondary p-2">
        {{capitalize (t "condition.then")}}...
      </span>
    </div>
    <div class="row justify-content-center vertical-line"><div class="py-3 px-0"></div></div>
    <div class="automation-group offset-2 col-8 rounded bg-body p-2 {{if (or this.selectedCondition this.selectedAction) "active" }}">
      <div class="text-primary">
        <i class="fa fa-code-branch"/>
        {{t "condition.if"}}...
      </div>
      <div class="ms-1 list-group">
        {{#if this.hasTeamCondition}}
          <div {{!-- template-lint-disable no-invalid-interactive --}}
            role="{{if this.ownedByVariant "button"}}"
            class="list-group-item border-0 rounded d-flex align-items-center ps-1 {{if (eq this.selectedCondition "teamCondition") "active"}} {{unless this.ownedByVariant "text-secondary"}}"
            {{on "click" (if this.ownedByVariant (fn this.selectSettings "teamCondition") (noop))}}
          >
            {{#if this.ownedByVariant}}
              <button type="button" class="btn badge text-dark py-2" {{on "click" (stop-propagation (fn this.deleteCondition "teamCondition"))}}>
                <i class="fa fa-xmark opacity-50"/>
              </button>
            {{/if}}
            {{t "team"}} {{t "condition.operators.equals"}} {{@automation.team.name}}
          </div>
          {{#if this.automationTriggerAttributes.length}}
            <div class="ms-1 p-2 text-uppercase">
              {{t "and"}}
            </div>
          {{/if}}
        {{/if}}
        {{#each this.automationTriggerAttributes as |triggerAttribute index|}}
          <div {{!-- template-lint-disable no-invalid-interactive --}}
            role="{{if this.ownedByVariant "button"}}"
            class="list-group-item border-0 rounded d-flex align-items-center ps-1 {{if (eq this.selectedCondition triggerAttribute) "active"}} {{unless this.ownedByVariant "text-secondary"}}"
            {{on "click" (if this.ownedByVariant (fn this.selectSettings triggerAttribute) (noop))}}
          >
            {{#if this.ownedByVariant}}
              <button type="button" class="btn badge text-dark py-2" {{on "click" (stop-propagation (fn this.deleteCondition triggerAttribute))}}>
                <i class="fa fa-xmark opacity-50"/>
              </button>
            {{/if}}
            {{#if (gt index 0)}}
              <span class="col-4 col-lg-2 me-2">
                <TomSelect
                  @valueField="value"
                  @labelField="label"
                  @selected={{this.selectedConditionOperator}}
                  @data={{this.conditionOperators}}
                  @disableClear={{true}}
                  @onChange={{this.setConditionOperator}}
                  @returnOnlyValue={{true}}
                  @disabled={{gt index 1}}
                />
              </span>
            {{/if}}
            {{triggerAttribute.text}}
          </div>
        {{/each}}
      </div>
      {{#if this.showNewConditionButton}}
        <button type="button" class="btn btn-outline-primary btn-sm py-0 m-2" {{on "click" this.createNewCondition}}>
          <i class="fa fa-plus"></i> {{t "condition.condition"}}
        </button>
      {{/if}}
      {{#if this.isDevMode}}
        <div class="text-primary mt-4">
          <i class="fa fa-clock"/>
          {{t "on"}}...
        </div>
        <button type="button" class="btn btn-outline-primary btn-sm py-0 m-2" {{on "click" (fn this.selectSettings "schedule")}} disabled={{not this.ownedByVariant}}>
          {{this.schedulePrompt}}
        </button>
      {{/if}}
      <div class="text-primary mt-4">
        <i class="fa fa-bolt"/>
        {{t "do"}}...
      </div>
      <div class="ms-1 list-group">
        {{#each this.visibleAutomationActions as |action|}}
          <div {{!-- template-lint-disable no-invalid-interactive --}}
            class="list-group-item border-0 rounded d-flex align-items-center ps-1 {{unless (owned-by-variant action @automation.objectType.objectTypeVariant) "text-secondary"}} {{if (eq action this.selectedAction) "active"}}"
            role="{{if this.ownedByVariant "button"}}" {{on "click" (if (owned-by-variant action @automation.objectType.objectTypeVariant) (fn this.selectSettings action) (noop))}}
          >
            {{#if (owned-by-variant action @automation.objectType.objectTypeVariant)}}
              <button type="button" class="btn badge text-dark py-2" {{on "click" (fn this.removeAction action)}}>
                <i class="fa fa-xmark opacity-50"/>
              </button>
            {{/if}}
            {{#if action.automationMailAction}}
              {{action.automationMailAction.recipientsText}}
            {{else if action.automationAiAction}}
              {{t "automation.fill_fields_ai"}}
            {{else if action.automationUpdateObjectAction}}
              {{t "automation.update_record"}}
            {{else if action.automationWebhookAction}}
              {{t "automation.send_webhook"}}
            {{/if}}
          </div>
        {{/each}}
      </div>
      <button type="button" class="btn btn-outline-primary btn-sm py-0 m-2" data-bs-toggle="dropdown" {{on "click" (stop-propagation)}}>
        <i class="fa fa-plus"></i> {{t "automation.action"}}
      </button>
      <AutomationActionMenu @createNewAction={{this.createNewAction}}/>
    </div>
  </div>
  <div class="bg-body h-100 p-2 overflow-auto mini-scrollbar {{unless this.hasSelectedSetting "d-none d-lg-block"}}">
    <div class="d-flex flex-column h-100">
      {{#if this.hasSelectedSetting}}
        <div class="d-flex align-items-baseline">
          <button class="btn d-lg-none" type="button" {{on "click" this.resetSelectedSettings}}>
            <i class="fa fa-arrow-left"/>
          </button>
          {{#if this.showEventSettings}}
            <AutomationTriggerSettings @automation={{@automation}} />
          {{else if this.showScheduleSettings}}
            <AutomationScheduleSettings @automation={{@automation}} />
          {{else if this.selectedCondition}}
            <AutomationConditionSettings
              @condition={{this.selectedCondition}}
              @automation={{@automation}}
              @setSelectedCondition={{this.setSelectedCondition}}
              @deleteCondition={{this.deleteCondition}}
            />
          {{else if this.selectedAction}}
            {{#if this.rerenderActionSettings}}
              <AutomationActionSettings @action={{this.selectedAction}} />
            {{else}}
              <AutomationActionSettings @action={{this.selectedAction}} />
            {{/if}}
          {{/if}}
        </div>
      {{else}}
        {{t "select_placeholder"}}
      {{/if}}
      <div class="d-flex mt-auto">
        <SaveButton class="ms-auto btn-primary" @callback={{this.saveSelectedAutomation}}/>
      </div>
    </div>
  </div>
</div>
