import { action } from '@ember/object';
import { service } from '@ember/service';
import Component from '@glimmer/component';
import { tracked } from '@glimmer/tracking';
import Store from '@ember-data/store';
import { handleError } from 'frontend/utils/handle-error';
import AutomationTriggerModel, { CONDITION_OPERATOR, TRIGGER_TYPE } from 'frontend/models/automation-trigger';
import FrontendEventModel from 'frontend/models/frontend-event';
import IntlService from 'ember-intl/services/intl';
import DialogService from 'frontend/services/dialog';
import RouterService from '@ember/routing/router-service';
import Owner from '@ember/owner';
import AutomationActionModel, { ACTION_TYPE } from 'frontend/models/automation-action';
import { MAIL_TYPE } from 'frontend/models/automation-mail-action';
import AutomationTriggerAttributeModel, { CONDITION_TYPE } from 'frontend/models/automation-trigger-attribute';
import { EXCLUDED_CONDITION_DATA_TYPES } from './automation-condition-settings';
import AutomationMailActionSettings from 'frontend/utils/automation-mail-action-settings';
import FormElementModel from 'frontend/models/form-element';
import { abortTransition, hasDirtyChecksEnabled, ignoreDirtyChecks } from 'frontend/utils/transition';
import Transition from '@ember/routing/transition';
import Viewpoint, { VIEWPOINT_CALLBACK } from 'frontend/services/viewpoint';
import { VariantedAutomation } from 'frontend/utils/variants/varianted-automation';
import { destroyRecord } from 'frontend/utils/destroy-record';
import ENV from 'frontend/config/environment';
import { SUPPORTED_ACTIONS } from './automation-thumbnail';

interface AutomationSettingsSignature {
  Args: {
    automation: VariantedAutomation;
    unselectAutomation: () => void;
    toggleActive: () => void;
    deleteAutomation: () => void;
  };
}

export default class AutomationSettingsComponent extends Component<AutomationSettingsSignature> {
  @tracked
  showEventSettings = false;

  @tracked
  showScheduleSettings = false;

  @tracked
  declare selectedCondition?: 'teamCondition' | AutomationTriggerAttributeModel;

  @tracked
  declare selectedAction?: AutomationActionModel;

  @service
  declare store: Store;

  @service
  declare intl: IntlService;

  @service
  declare dialog: DialogService;

  @service
  declare router: RouterService;

  @service
  declare viewpoint: Viewpoint;

  @tracked
  declare rerenderActionSettings: boolean;

  declare _onViewpointChange: () => void;

  declare _beforeViewpointChange: () => Promise<boolean>;

  declare _warnBeforeUnload: () => void;

  declare _onrouteChange: (transition: Transition) => void;

  constructor(owner: Owner, args: AutomationSettingsSignature['Args']) {
    super(owner, args);

    this._warnBeforeUnload = this.warnBeforeUnload.bind(this);
    window.addEventListener('beforeunload', this._warnBeforeUnload);

    this._onrouteChange = this.onrouteChange.bind(this);
    this.router.on('routeWillChange', this._onrouteChange);

    this._beforeViewpointChange = this.beforeViewpointChange.bind(this);
    this.viewpoint.registerCallback(VIEWPOINT_CALLBACK.BEFORE_VIEWPOINT_CHANGE, this._beforeViewpointChange);

    this._onViewpointChange = this.onViewpointChange.bind(this);
    this.viewpoint.registerCallback(VIEWPOINT_CALLBACK.VIEWPOINT_CHANGE, this._onViewpointChange);
  }

  willDestroy() {
    window.removeEventListener('beforeunload', this._warnBeforeUnload);
    this.router.off('routeWillChange', this._onrouteChange);
    this.viewpoint.unregisterCallback(VIEWPOINT_CALLBACK.VIEWPOINT_CHANGE, this._onViewpointChange);
    this.viewpoint.unregisterCallback(VIEWPOINT_CALLBACK.BEFORE_VIEWPOINT_CHANGE, this._beforeViewpointChange);
    super.willDestroy();
  }

  get isDevMode() {
    return ENV.environment === 'development';
  }

  get hasSelectedSetting() {
    return this.selectedAction || this.showEventSettings || this.selectedCondition || this.showScheduleSettings;
  }

  get visibleAutomationActions() {
    return this.args.automation.automationActions.filter((action) => !action.isDeleted);
  }

  get automationTriggerAttributes() {
    return this.args.automation.automationTrigger?.automationTriggerAttributes.filter((attribute) => !attribute.get('isDeleted'));
  }

  get showNewConditionButton() {
    // don't show the new condition button when the trigger is frontend events and team filter has already been chosen
    const frontendEventWithTeamCondition = (!this.args.automation.team && this.selectedCondition !== "teamCondition") || this.args.automation.hasAutomationTrigger;

    return this.ownedByVariant && frontendEventWithTeamCondition;
  }

  get hasTeamCondition() {
    return this.args.automation.team || this.selectedCondition == "teamCondition";
  }

  get hasDirtyState() {
    return this.args.automation.isDirty || this.selectedAction?.isDirty;
  }

  get variantedObjectType() {
    return this.args.automation.objectType;
  }

  get ownedByVariant() {
    return this.args.automation.isOwnedByVariant;
  }

  get schedulePrompt() {
    switch (this.args.automation.scheduleType) {
    case 'delay':
      return this.intl.t('automation.schedule_options.after_n_minutes', { delay: this.args.automation.scheduleValue });
    case 'attribute':
      return `${this.intl.t('field')}: ${this.args.automation.scheduleAttribute?.name}`;
    case 'instant':
    default:
      return this.intl.t('automation.schedule_options.immediately');
    }
  }

  get conditionOperators() {
    return [{ value: CONDITION_OPERATOR.OR, label: this.intl.t('or') }, { value: CONDITION_OPERATOR.AND, label: this.intl.t('and') }];
  }

  get selectedConditionOperator() {
    return this.conditionOperators.find((operator) => operator.value == this.args.automation.automationTrigger?.conditionOperator);
  }

  get isDeleteableAutomation() {
    if (this.args.automation.automationActions.some((action) => !SUPPORTED_ACTIONS.includes(action.actionType))) return false;

    const triggerType = this.args.automation.automationTrigger?.triggerType;
    if (triggerType == TRIGGER_TYPE.DESTROY || triggerType == TRIGGER_TYPE.INTEGRATION) return false;

    return !this.args.automation.automationActions.some((action) => action.team);
  }

  @action
  async unselectAutomation() {
    if (await this.checkDirtyAutomation()) return;
    this.args.unselectAutomation();
    this.resetSelectedSettings();
  }

  @action
  async selectSettings(group: "when" | "teamCondition" | "schedule" | AutomationTriggerAttributeModel | AutomationActionModel) {
    if (group instanceof AutomationActionModel || this.selectedAction) {
      if (await this.checkDirtyAutomation()) return;
    }

    this.resetSelectedSettings();

    if (group instanceof AutomationActionModel) {
      this.rerenderActionSettings = !this.rerenderActionSettings;
      this.selectedAction = group;
    } else if (group == "when") {
      this.showEventSettings = true;
    } else if (group == "teamCondition" || group instanceof AutomationTriggerAttributeModel) {
      this.setSelectedCondition(group);
    } else if (group == "schedule") {
      this.showScheduleSettings = true;
    }
  }

  @action
  async saveSelectedAutomation() {
    try {
      if (!!this.args.automation.isNew || !this.selectedAction) {
        const deletedRelationships = this.findDeletedRelationships();
        await this.args.automation.save();
        this.args.automation.isDirty = false;
        this.unloadDeletedRelationships(deletedRelationships);
      }
      if (this.selectedAction) {
        await this.selectedAction?.save();
        this.selectedAction.isDirty = false;
      }
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  resetSelectedSettings() {
    this.selectedCondition = undefined;
    this.selectedAction = undefined;
    this.showEventSettings = false;
    this.showScheduleSettings = false;
  }

  findDeletedRelationships() {
    const deletedRelationships: Array<AutomationTriggerModel | AutomationTriggerAttributeModel | FrontendEventModel> = [];

    if (this.args.automation.automationTrigger?.get('isDeleted')) {
      deletedRelationships.push(this.args.automation.automationTrigger);
      this.args.automation.automationTrigger.automationTriggerAttributes.forEach((attr) => deletedRelationships.push(attr));
    } else {
      this.args.automation.automationTrigger?.automationTriggerAttributes.forEach((attr) => {
        if (attr.get('isDeleted')) deletedRelationships.push(attr);
      });
    }

    this.args.automation.frontendEvents.forEach((event) => {
      if (event.get('isDeleted')) deletedRelationships.push(event);
    });

    return deletedRelationships;
  }

  unloadDeletedRelationships(relationships: Array<AutomationTriggerModel | AutomationTriggerAttributeModel | FrontendEventModel>) {
    relationships.forEach((rel) => rel.unloadRecord());
  }

  warnBeforeUnload(event: BeforeUnloadEvent) {
    if (!this.hasDirtyState) return;
    event.preventDefault();
    // setting returnValue to a value other than null or undefined will prompt the dialog
    // In older browsers, the returnValue of the event is displayed in this dialog.
    return event.returnValue = this.intl.t("dialog.confirm_unsaved_changes_automation.text_close");
  }

  async onrouteChange(transition: Transition) {
    if (transition.isAborted) return;

    if (hasDirtyChecksEnabled(transition) && (this.hasDirtyState)) {
      abortTransition(transition, this.router);

      const result = await this.openDirtyAutomationDialog();

      if (result) {
        this.rollbackAutomation();
        ignoreDirtyChecks(transition);
        transition.retry();
      }
    }
  }

  @action
  async createNewAction(actionType: ACTION_TYPE) {
    if (!this.args.automation.isNew || this.selectedAction) {
      if (await this.checkDirtyAutomation()) return;
    }

    const automationAction = this.store.createRecord("automation-action", { automation: this.args.automation.original, actionType: actionType, objectTypeVariantId: this.variantedObjectType.objectTypeVariant?.id });
    if (actionType === ACTION_TYPE.MAIL) {
      const mailType = this.args.automation.automationTrigger?.triggerType == TRIGGER_TYPE.CREATE ? MAIL_TYPE.OBJECT_CREATION : MAIL_TYPE.OBJECT_UPDATE;
      this.store.createRecord('automation-mail-action', { mailType: mailType, automationAction: automationAction, settings: new AutomationMailActionSettings({ type: "delay", value: 15 }) });
    } else if (actionType === ACTION_TYPE.AI) {
      this.store.createRecord('automation-ai-action', { automationAction: automationAction });
    } else if (actionType === ACTION_TYPE.UPDATE_OBJECT) {
      this.store.createRecord('automation-update-object-action', { automationAction: automationAction });
    } else if (actionType === ACTION_TYPE.WEBHOOK) {
      this.store.createRecord('automation-webhook-action', { automationAction: automationAction });
    }

    this.resetSelectedSettings();
    this.selectedAction = automationAction;
    this.rerenderActionSettings = !this.rerenderActionSettings;
    automationAction.isDirty = true;
  }

  @action
  async removeAction(action: AutomationActionModel) {
    const lastSelectedAction = this.selectedAction;
    if (lastSelectedAction == action) {
      this.resetSelectedSettings();
    }
    try {
      await destroyRecord(action, () => {
        action.automationAiAction?.unloadRecord();
        action.automationMailAction?.unloadRecord();
        action.automationStaticDataAction?.unloadRecord();
      });
    } catch (error) {
      if (lastSelectedAction == action) {
        this.selectedAction = action;
      }
      // Reset Action.isDeleted to false to prevent the action from being removed from the UI
      action.rollbackAttributes();
      handleError(this, error);
    }
  }

  @action
  setSelectedCondition(condition?: 'teamCondition' | AutomationTriggerAttributeModel) {
    this.resetSelectedSettings();

    this.selectedCondition = condition;
  }

  @action
  async createNewCondition() {
    if (this.selectedAction) {
      if (await this.checkDirtyAutomation()) return;
    }
    let condition: 'teamCondition' | AutomationTriggerAttributeModel;
    if (this.args.automation.hasAutomationTrigger) {
      const objectTypeAttribute = this.args.automation.objectType
        .firstValidFormElement((fe: FormElementModel) => fe.objectTypeAttribute && !EXCLUDED_CONDITION_DATA_TYPES.includes(fe.objectTypeAttribute.dataType))?.objectTypeAttribute;
      condition = this.store.createRecord('automation-trigger-attribute', { automationTrigger: this.args.automation.automationTrigger, objectTypeAttribute: objectTypeAttribute, conditionType: CONDITION_TYPE.CHANGES });
    } else {
      condition = 'teamCondition';
      this.args.automation.team = this.args.automation.objectType.teams[0];
    }

    this.args.automation.isDirty = true;
    this.setSelectedCondition(condition);
  }

  @action
  async deleteCondition(condition: AutomationTriggerAttributeModel | 'teamCondition') {
    if (condition == 'teamCondition') {
      this.args.automation.team = undefined;
    } else if (condition instanceof AutomationTriggerAttributeModel) {
      condition.deleteRecord();

      // reset condtition operator to the default OR operator when all trigger attributes have been removed
      if (this.automationTriggerAttributes && this.automationTriggerAttributes.length <= 1 && this.args.automation.automationTrigger) {
        this.args.automation.automationTrigger.conditionOperator = CONDITION_OPERATOR.OR;
      }
    }

    this.setSelectedCondition();
    this.args.automation.isDirty = true;
  }

  async beforeViewpointChange() {
    return !(await this.checkDirtyAutomation());
  }

  onViewpointChange() {
    this.args.unselectAutomation();
  }

  @action
  /**
   * Check whether there is a dirty Automation
   * @returns true if there is a dirty Automation, false otherwise
   */
  async checkDirtyAutomation() {
    if (!this.hasDirtyState) return false;

    const result = await this.openDirtyAutomationDialog();
    if (result) await this.rollbackAutomation();

    return !result;
  }

  async rollbackAutomation() {
    // Either automation or action is dirty, so only reload the dirty object
    if (this.args.automation.isDirty) {
      if (!this.args.automation.isNew) await this.args.automation.reload();
      this.args.automation.rollback();
    } else {
      await this.selectedAction?.rollback();
    }
  }

  async openDirtyAutomationDialog(dialogText?: string) {
    const text = dialogText || this.intl.t(this.selectedAction ? "dialog.confirm_unsaved_changes_automation_action.text_close" : "dialog.confirm_unsaved_changes_automation.text_close");
    const result = await this.dialog.confirm(text);
    return result;
  }

  @action
  async toggleActive() {
    if (this.args.automation.get('isNew')) {
      this.args.automation.active = !this.args.automation.active;
      return;
    }

    if (this.hasDirtyState) {
      const dialogText = this.intl.t(this.selectedAction ? "dialog.confirm_unsaved_changes_automation_action.text_save" : "dialog.confirm_unsaved_changes_automation.text_save");
      if (!(await this.openDirtyAutomationDialog(dialogText))) return;
      if (this.selectedAction) {
        await this.selectedAction.save();
        this.selectedAction.isDirty = false;
      }
    }

    this.args.toggleActive();
  }

  @action
  setConditionOperator(operator: CONDITION_OPERATOR) {
    if (!this.args.automation.automationTrigger) return;

    this.args.automation.automationTrigger.conditionOperator = operator;
    this.args.automation.isDirty = true;
  }
}
