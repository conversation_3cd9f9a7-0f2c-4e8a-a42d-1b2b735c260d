import { action } from '@ember/object';
import { service } from '@ember/service';
import Component from '@glimmer/component';
import IntlService from 'ember-intl/services/intl';
import { ACTION_TYPE } from 'frontend/models/automation-action';
import { CONDITION_OPERATOR, TRIGGER_TYPE } from 'frontend/models/automation-trigger';
import DialogService from 'frontend/services/dialog';
import { ownedByVariant } from 'frontend/utils/variants';
import { isIncompleteAutomation } from 'frontend/utils/variants/publish';
import { VariantedAutomation } from 'frontend/utils/variants/varianted-automation';

interface AutomationThumbnailSignature {
  Args: {
    automation: VariantedAutomation;
    toggleActive?: () => void;
    deleteAutomation?: () => void;
    isLocked?: boolean;
    selectAutomation: (event: MouseEvent) => void;
  };
}

export const SUPPORTED_ACTIONS = [ACTION_TYPE.MAIL, ACTION_TYPE.AI, ACTION_TYPE.UPDATE_OBJECT, ACTION_TYPE.WEBHOOK];

export default class AutomationThumbnailComponent extends Component<AutomationThumbnailSignature> {
  @service
  declare intl: IntlService;

  @service
  declare dialog: DialogService;

  get automationTrigger() {
    return this.args.automation.automationTrigger;
  }

  get triggerType() {
    return this.automationTrigger?.triggerType ? this.intl.t('automation.' + this.automationTrigger?.triggerType) : this.intl.t('automation.frontend');
  }

  get isSelectableAutomation() {
    if (this.args.isLocked) return false;

    const onlySupportedActions = this.args.automation.automationActions.every((action) => SUPPORTED_ACTIONS.includes(action.actionType));
    const noDestroyTrigger = this.args.automation.automationTrigger?.triggerType != TRIGGER_TYPE.DESTROY;
    const noTeamOnActions = this.args.automation.automationActions.every((action) => !action.team);

    return onlySupportedActions && noDestroyTrigger && noTeamOnActions;
  }

  get isDeleteableAutomation() {
    if (this.args.isLocked) return false;

    if (this.args.automation.automationActions.some((action) => !SUPPORTED_ACTIONS.includes(action.actionType))) return false;

    const triggerType = this.args.automation.automationTrigger?.triggerType;
    if (triggerType == TRIGGER_TYPE.DESTROY || triggerType == TRIGGER_TYPE.INTEGRATION) return false;

    return !this.args.automation.automationActions.some((action) => action.team);
  }

  get conditionsText() {
    let resultText = "";
    if (!this.automationTrigger?.automationTriggerAttributes.length && !this.args.automation.team) return resultText;
    if (this.args.automation.team) resultText += `${this.intl.t('team')} ${this.intl.t('condition.operators.equals')} ${this.args.automation.team.name}`;

    const conditionOperatorText = this.automationTrigger?.conditionOperator == CONDITION_OPERATOR.AND ? this.intl.t('condition.and_join') : this.intl.t('condition.or_join');
    this.automationTrigger?.automationTriggerAttributes.forEach((triggerAttribute, index) => {
      if (index > 0) {
        resultText += ` ${conditionOperatorText.toUpperCase()} `;
      } else if (this.args.automation.team) {
        resultText += ` ${this.intl.t('condition.and_join').toUpperCase()} `;
      }

      resultText += triggerAttribute.text;
    });

    return resultText;
  }

  get actionsText() {
    let resultText = "";
    this.args.automation.automationActions.forEach((action) => {
      if (resultText.length) {
        resultText += ` ${this.intl.t('condition.and_join').toUpperCase()} `;
      }
      switch (action.actionType) {
      case ACTION_TYPE.MAIL: {
        resultText += action.automationMailAction?.recipientsText;
        break;
      }
      case ACTION_TYPE.STATIC_DATA: {
        resultText += this.intl.t('automation.get_static_data');
        break;
      }
      case ACTION_TYPE.INTEGRATION: {
        resultText += this.intl.t('automation.run_integration');
        break;
      }
      case ACTION_TYPE.AI: {
        resultText += this.intl.t('automation.fill_fields_ai');
        break;
      }
      case ACTION_TYPE.UPDATE_OBJECT: {
        resultText += this.intl.t('automation.update_record');
        break;
      }
      case ACTION_TYPE.WEBHOOK: {
        resultText += this.intl.t('automation.send_webhook');
        break;
      }
      }
    });

    return resultText;
  }

  get isOwnedByVariant() {
    return ownedByVariant(this.args.automation, this.args.automation.objectType.objectTypeVariant);
  }

  get isIncompleteAutomation() {
    return isIncompleteAutomation(this.args.automation);
  }

  @action
  setActive(event: GenericEvent<HTMLInputElement>) {
    event.stopImmediatePropagation();
    this.args.toggleActive?.();
  }
}
