<div class="mb-4">
  <label class="col-form-label" for="webhook-action-url">{{t "input_type.url"}}</label>
  <input
    type="text"
    id="webhook-action-url"
    class="form-control"
    value={{@automationWebhookAction.url}}
    placeholder="https://"
    {{on "input" this.setUrl}}
  >
</div>

<div class="mb-4">
  <label class="col-form-label">{{t "automation.webhook_payload_fields"}}</label>
  <ColumnWindow
    @columns={{this.columns}}
    @objectType={{this.variantedObjectType}}
    @onColumnsChange={{this.onColumnsChange}}
    @withoutDropMenu={{true}}
    @hideObjectRelationships={{true}}
    @attributeFilter={{this.attributeFilter}}
    @hideSettingHeader={{true}}
  />
</div>
