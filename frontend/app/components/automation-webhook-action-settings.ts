import Component from '@glimmer/component';
import { action } from '@ember/object';
import { service } from "@ember/service";
import Viewpoint from "frontend/services/viewpoint";
import { findObjectTypeVariantFor, VariantedObjectTypeAttribute, variantFor } from "frontend/utils/variants";
import AutomationWebhookActionModel from 'frontend/models/automation-webhook-action';
import { cached } from '@glimmer/tracking';
import { Column } from 'frontend/utils/visualisation';
import Store from '@ember-data/store';
import ObjectTypeProperty from 'frontend/utils/object-type-property';
import { DATA_TYPE } from 'frontend/models/object-type-attribute';

interface AutomationUpdateObjectActionSettingsSignature {
  Args: {
    automationWebhookAction: AutomationWebhookActionModel;
  };
}

export default class AutomationUpdateObjectActionSettings extends Component<AutomationUpdateObjectActionSettingsSignature> {
  @service
  declare viewpoint: Viewpoint;

  @service
  declare store: Store;

  // TODO: why
  @cached
  get columns() {
    const selectedAttributeIds = this.args.automationWebhookAction.automationWebhookActionAttributes.map((a) => {
      return a.columnName ?? a.belongsTo('objectTypeAttribute').id();
    });

    return this.variantedObjectType.fields.filter((field) => selectedAttributeIds.includes(field.id)).map((field) => {
      return new Column(field);
    });
  }

  @cached
  get objectTypeVariant() {
    const team = this.viewpoint.selectedTeam;
    return findObjectTypeVariantFor(team, this.args.automationWebhookAction.automationAction.automation.objectType);
  }

  // TODO: Why are we varianting here?
  get variantedObjectType() {
    return variantFor(this.args.automationWebhookAction.automationAction.automation.objectType, this.objectTypeVariant);
  }

  @action
  attributeFilter(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    return ![DATA_TYPE.ATTACHMENT, DATA_TYPE.HTML].includes(field.dataType);
  }

  @action
  setUrl(event: GenericEvent<HTMLInputElement>) {
    this.args.automationWebhookAction.url = event.target.value;
    this.args.automationWebhookAction.automationAction.isDirty = true;
  }

  @action
  onColumnsChange() {
    const columns = this.columns; // cached updated version
    const oldAttributes = this.args.automationWebhookAction.automationWebhookActionAttributes.slice();
    columns.forEach((column) => {
      if (column.columnName) {
        const old = oldAttributes.find((a) => a.columnName === column.columnName);
        if (old) {
          oldAttributes.splice(oldAttributes.indexOf(old), 1);
        } else {
          this.args.automationWebhookAction.automationWebhookActionAttributes.createRecord({ columnName: column.columnName });
        }
      } else if (column.objectTypeAttributeId) {
        const old = oldAttributes.find((a) => a.belongsTo('objectTypeAttribute').id() === column.objectTypeAttributeId);
        if (old) {
          oldAttributes.splice(oldAttributes.indexOf(old), 1);
        } else {
          const field = this.store.peekRecord('object-type-attribute', column.objectTypeAttributeId);
          this.args.automationWebhookAction.automationWebhookActionAttributes.createRecord({ objectTypeAttribute: field });
        }
      }
    });

    oldAttributes.forEach((a) => a.unloadRecord());
    this.args.automationWebhookAction.automationAction.isDirty = true;
  }
}
