import Component from '@glimmer/component';
import { action } from '@ember/object';
import { init as echartInit, EChartsType, RadarComponentOption } from 'echarts';
import { debounce } from '@ember/runloop';
import { ChartOptions } from 'frontend/utils/visualisation';

/** delay in ms before chart is rerendered after resize */
const RESIZE_DELAY = 250;

interface ChartSignature {
  Args: {
    options: ChartOptions;
    onClick: (value: Record<string, unknown>, indicators?: unknown) => Promise<void>;
  };
}

export default class ChartComponent extends Component<ChartSignature> {
  declare chart: EChartsType;

  @action
  setup(element: HTMLElement) {
    this.chart = echartInit(element);
    const options = this.args.options.buildOptions();
    this.chart.setOption(options);
    if (this.args.onClick) {
      this.chart.on('click', (eventParams) => {
        this.args.onClick(eventParams, (options.radar as RadarComponentOption)?.indicator);
      });
    } else {
      // Echarts uses a 'pointer' style cursor when hovering over series
      // when there is no click handler, we revert back to a normal cursor
      this.chart.on('mousemove', () => {
        this.chart.getZr().setCursorStyle('default');
      });
    }
  }

  @action
  resizeChart() {
    this.chart.resize();
  }

  @action
  onResize() {
    debounce(this.resizeChart, RESIZE_DELAY);
  }
}
