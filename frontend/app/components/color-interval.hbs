<button
  type="button"
  class="color-circle"
  style={{style "background-color" @colorInterval.color}}
  {{on "click" this.selectColor}}
>
</button>
<NumberInput
  @onInput={{this.setMinInterval}}
  value={{@colorInterval.min}}
  class="form-control"
  placeholder={{t "value"}}
/>
<NumberInput
  @onInput={{this.setMaxInterval}}
  value={{@colorInterval.max}}
  class="form-control"
  placeholder={{t "value"}}
/>
<button type="button" class="btn badge text-dark ms-auto py-2" {{on "click" @removeInterval}}>
  <i class="fa fa-xmark opacity-50"/>
</button>
