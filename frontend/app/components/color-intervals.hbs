<div class="mb-4">
  <label class="col-form-label d-block">{{t "conditional_colors"}}</label>
  {{#if @colorIntervals.length}}
    <div class="color-intervals-grid">
      <span></span>
      <label class="col-form-label">{{t "charts.targets.lower"}}</label>
      <label class="col-form-label">{{t "charts.targets.upper"}}</label>
      <span></span>
      {{#each @colorIntervals as |interval|}}
        <ColorInterval @colorInterval={{interval}} @removeInterval={{fn this.removeInterval interval}} @onChange={{@onChange}}/>
      {{/each}}
    </div>
  {{/if}}
  <button type="button" class="btn btn-primary btn-sm py-0 mt-2" {{on "click" this.addInterval}}>
    <i class="fa fa-plus fa-sm fa-fw" />
    {{t "new"}}
  </button>
</div>
