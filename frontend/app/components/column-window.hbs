{{!-- TODO: Replace by moving content outside of menu --}}
{{#if @withoutDropMenu}}
  <div class="m-2">
    <SortableContainer @items={{@columns}} @onMoveItem={{@onColumnsChange}} @itemId="id" as |item isDragging touchStartHandler dragStartHandler|>
      <ColumnWindowRow
        @objectType={{@objectType}}
        @column={{item}}
        @columns={{@columns}}
        @isDragging={{isDragging}}
        @onTouchStart={{touchStartHandler}}
        @onDragStart={{dragStartHandler}}
        @onColumnsChange={{@onColumnsChange}}
      />
    </SortableContainer>
  </div>
  <div class="hide-ts-selected my-2">
    <TomSelects::AttributeSelector
      @multiple={{true}}
      @attributeFilter={{this.attributeFilter}}
      @onChange={{this.selectColumn}}
      @objectType={{@objectType}}
      @selected={{this.selectedColumns}}
      @placeholder={{t "select_placeholder"}}
      @hidePlaceholder={{false}}
      @disableClear={{true}}
    />
  </div>
  <div class="d-flex m-2">
    <button class="btn btn-primary btn-sm py-0 me-2" type="button" {{on "click" (fn this.toggleAll true)}}>{{t "show_all"}}</button>
    <button class="btn btn-primary btn-sm py-0" type="button" {{on "click" (fn this.toggleAll false)}}>{{t "hide_all"}}</button>
  </div>
  {{#unless @hideSettingHeader}}
    <div class="d-flex p-2 border-top">
      <div class="form-check form-switch">
        <CheckBox
            @checked={{@visualisation.layout.showBreadcrumb}}
            @onChange={{this.toggleBreadcrumb}}
          />
      </div>
      <div class="px-2">{{t "show_details_in_header"}}</div>
    </div>
  {{/unless}}
{{else}}
  <DropMenu class="query-pane column-window">
    <:body>
      <div class="m-2">
        <SortableContainer
          @items={{@columns}}
          @itemId="id"
          @onDragEnd={{@onColumnsChange}}
          as |item isDragging touchStartHandler dragStartHandler|
        >
          <ColumnWindowRow
            @objectType={{@objectType}}
            @column={{item}}
            @columns={{@columns}}
            @isDragging={{isDragging}}
            @onTouchStart={{touchStartHandler}}
            @onDragStart={{dragStartHandler}}
            @onColumnsChange={{@onColumnsChange}}
          />
        </SortableContainer>
      </div>
    </:body>
    <:footer>
      <div class="hide-ts-selected my-2">
        <TomSelects::AttributeSelector
          @multiple={{true}}
          @attributeFilter={{this.attributeFilter}}
          @onChange={{this.selectColumn}}
          @objectType={{@objectType}}
          @selected={{this.selectedColumns}}
          @placeholder={{t "select_placeholder"}}
          @hidePlaceholder={{false}}
          @disableClear={{true}}
        />
      </div>
      <div class="d-flex m-2">
        <button class="btn btn-primary btn-sm me-2" type="button" {{on "click" (fn this.toggleAll true)}}>{{t "show_all"}}</button>
        <button class="btn btn-primary btn-sm" type="button" {{on "click" (fn this.toggleAll false)}}>{{t "hide_all"}}</button>
      </div>
      {{#unless @hideSettingHeader}}
        <div class="d-flex p-2 border-top">
          <div class="form-check form-switch">
            <CheckBox
                @checked={{@visualisation.layout.showBreadcrumb}}
                @onChange={{this.toggleBreadcrumb}}
              />
          </div>
          <div class="px-2">{{t "show_details_in_header"}}</div>
        </div>
      {{/unless}}
    </:footer>
  </DropMenu>
{{/if}}
