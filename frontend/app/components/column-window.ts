import Component from '@glimmer/component';
import { action } from '@ember/object';
import { service } from '@ember/service';
import Viewpoint from 'frontend/services/viewpoint';
import { VariantedObjectType, VariantedObjectTypeAttribute } from 'frontend/utils/variants';
import ObjectTypeProperty from 'frontend/utils/object-type-property';
import { Column } from 'frontend/utils/visualisation';
import VisualisationModel from 'frontend/models/visualisation';
import { DATA_TYPE } from 'frontend/models/object-type-attribute';

interface ColumnWindowSignature {
  Args: {
    columns: Array<Column>;
    objectType: VariantedObjectType;
    visualisation?: VisualisationModel;
    onColumnsChange?: () => void;
    withoutDropMenu?: boolean; // TODO: make generic
    hideObjectRelationships: boolean;
    hideSettingHeader?: boolean;
    attributeFilter?: (attribute: VariantedObjectType['fields'][number]) => boolean;
  };
}

export default class ColumnWindowComponent extends Component<ColumnWindowSignature> {
  @service
  declare viewpoint: Viewpoint;

  get selectedColumns() {
    const selectedColumns = this.args.columns;
    return selectedColumns.reduce((result, column) => {
      const col = this.args.objectType.fields.find((field) => field.id === column.id);
      if (col) {
        result.push(col);
      }
      return result;
    }, [] as Array<VariantedObjectTypeAttribute | ObjectTypeProperty>);
  }

  get showBreadcrumb() {
    return this.args.visualisation?.layout.showBreadcrumb;
  }

  @action
  toggleAll(visible: boolean) {
    const { columns, objectType } = this.args;

    if (visible) {
      const columnIds = columns.map((column) => column.id);
      const filteredFields = objectType.fields.filter((field) => this.attributeFilter(field) &&
                                                                  !columnIds.includes(field.id) &&
                                                                  !field.archived);
      columns.pushObjects(filteredFields.map((field) => new Column(field)));
    } else {
      columns.clear();
    }
    this.args.onColumnsChange?.();
  }

  @action
  attributeFilter(field: ObjectTypeProperty | VariantedObjectTypeAttribute) {
    if (field instanceof ObjectTypeProperty && field.key == 'team_id') {
      return false;
    }
    if (field instanceof VariantedObjectTypeAttribute && field.hidden) {
      return false;
    }
    if (this.args.hideObjectRelationships && field instanceof VariantedObjectTypeAttribute && field.dataType == DATA_TYPE.RELATIONSHIP && field.targetClass == "Object") {
      return false;
    }

    if (this.args.attributeFilter) {
      return this.args.attributeFilter(field);
    }

    return true;
  }

  @action
  selectColumn(selectedFields: Array<ObjectTypeProperty | VariantedObjectTypeAttribute>) {
    if (!selectedFields.lastObject) return;
    this.args.columns.pushObject(new Column(selectedFields.lastObject));
    this.args.onColumnsChange?.();
  }

  @action
  toggleBreadcrumb() {
    if (!this.args.visualisation) return;

    this.args.visualisation.layout.showBreadcrumb = !this.args.visualisation.layout.showBreadcrumb;
    this.args.visualisation.view.isDirty = true;
  }
}
