import DialogComponent, { DialogDialogSignature } from 'frontend/components/dialog/dialog';
import { action } from '@ember/object';
import { service } from '@ember/service';
import Store from '@ember-data/store';
import Owner from '@ember/owner';
import { htmlSafe } from '@ember/template';
import IntlService from 'ember-intl/services/intl';
import ObjectTypeModel from 'frontend/models/object-type';
import { VariantedObjectType } from "frontend/utils/variants";
import { cached, tracked } from '@glimmer/tracking';
import ObjectTypeAttributeModel, { RELATIONSHIP_KIND } from 'frontend/models/object-type-attribute';
import { task, all } from 'ember-concurrency';
import { handleError } from 'frontend/utils/handle-error';
import { loadObjectTypeAttributes } from 'frontend/utils/object-type-attributes-loader';
import { buildCardinalityExplanation } from 'frontend/utils/relationship-cardinality';
import ObjectTypeProperty from 'frontend/utils/object-type-property';
import Viewpoint from 'frontend/services/viewpoint';

export interface CreateObjectTypeRelationshipDialogOptions {
  options: {
    objectType: VariantedObjectType;
    objectTypeAttribute: ObjectTypeAttributeModel;
  };
}

export default class CreateObjectTypeRelationshipDialogComponent extends DialogComponent<CreateObjectTypeRelationshipDialogOptions> {
  @service
  declare store: Store;

  @service
  declare intl: IntlService;

  @service
  declare viewpoint: Viewpoint;

  @tracked
  declare incomingRelationships?: Record<string, Array<ObjectTypeAttributeModel>>;

  @tracked
  declare selectedObjectType?: ObjectTypeModel;

  constructor(owner: Owner, options: DialogDialogSignature<CreateObjectTypeRelationshipDialogOptions>['Args']) {
    super(owner, options);
    this.loadIncomingRelationships();
  }

  async loadIncomingRelationships() {
    try {
      const relationalObjectTypeAttributes = await this.store.query(
        'object-type-attribute',
        {
          filter: {
            target_object_type_id: this.objectType.id,
            inverse: false,
            archived: false
          }
        }
      );
      this.incomingRelationships = relationalObjectTypeAttributes.reduce(
        (result: Record<string, Array<ObjectTypeAttributeModel>>, objectTypeAttribute) => {
          const relatedObjectTypeId = objectTypeAttribute.belongsTo('objectType').id();
          if (!result[relatedObjectTypeId]) result[relatedObjectTypeId] = [];
          result[relatedObjectTypeId].push(objectTypeAttribute);
          return result;
        },
        {}
      );
    } catch (error) {
      handleError(this, error);
    }
  }

  get objectTypeAttribute() {
    return this.args.dialogOptions.options.objectTypeAttribute;
  }

  get objectType() {
    return this.args.dialogOptions.options.objectType;
  }

  get selectedViewpoint() {
    return this.viewpoint.selectedViewpoint;
  }

  @cached
  get objectTypeRelationships() {
    if (!this.incomingRelationships) return [];

    return Object.keys(this.incomingRelationships).reduce((result: Array<ObjectTypeModel>, id: string) => {
      const relatedObjectType = this.store.peekRecord('object-type', id);
      if (relatedObjectType) result.push(relatedObjectType);
      return result;
    }, []);
  }

  get invalidObjectTypeAttributeConfiguration() {
    if (!this.objectTypeAttribute.hasValidName) return true;
    if (!this.objectTypeAttribute.targetObjectTypeId) return true;
    if (this.objectTypeAttribute.inverse && this.objectTypeAttribute.linkedObjectTypeAttributes.length === 0) return true;
    return false;
  }

  @cached
  get relationshipKinds() {
    return Object.values(RELATIONSHIP_KIND).map((value) => {
      return {
        id: value,
        text: this.intl.t(value)
      };
    });
  }

  get selectedRelationshipKind() {
    if (!this.objectTypeAttribute.relationshipKind) return;

    return {
      id: this.objectTypeAttribute.relationshipKind,
      text: this.intl.t(this.objectTypeAttribute.relationshipKind)
    };
  }

  get selectedInverseRelationshipKind() {
    if (!this.objectTypeAttribute.inverseRelationshipKind) return;

    return {
      id: this.objectTypeAttribute.inverseRelationshipKind,
      text: this.intl.t(this.objectTypeAttribute.inverseRelationshipKind)
    };
  }

  @action
  setName(event: GenericEvent<HTMLInputElement>) {
    this.objectTypeAttribute.name = event.target.value;
  }

  @action
  setRelationshipKind(direction: string, kind: RELATIONSHIP_KIND) {
    if (direction === 'forward') {
      this.objectTypeAttribute.relationshipKind = kind;
    } else {
      this.objectTypeAttribute.inverseRelationshipKind = kind;
    }
  }

  @action
  setInverse(inverse: boolean) {
    this.objectTypeAttribute.inverse = inverse;
    this.objectTypeAttribute.linkedObjectTypeAttributes.setObjects([]);
    this.selectObjectType(undefined);
    this.setRelationshipKind('forward', RELATIONSHIP_KIND.MULTIPLE);
    this.setRelationshipKind('inverse', RELATIONSHIP_KIND.MULTIPLE);
  }

  get forwardRelationExplanation() {
    const forwardCardinality = this.objectTypeAttribute.relationshipKind;
    if (!forwardCardinality || !this.selectedObjectType) return '';

    return htmlSafe(buildCardinalityExplanation(forwardCardinality, this.objectType.original, this.selectedObjectType));
  }

  get inverseRelationExplanation() {
    const inverseCardinality = this.objectTypeAttribute.inverseRelationshipKind;
    if (!inverseCardinality || !this.selectedObjectType) return '';

    return htmlSafe(buildCardinalityExplanation(inverseCardinality, this.selectedObjectType, this.objectType.original));
  }

  @action
  selectFields(otas: Array<ObjectTypeAttributeModel>) {
    this.objectTypeAttribute.linkedObjectTypeAttributes.setObjects(otas);
  }

  @action
  selectObjectType(objectType?: ObjectTypeModel) {
    this.selectedObjectType = objectType;
    this.objectTypeAttribute.targetObjectTypeId = objectType?.id;
    this.loadDataForSelectedObjectType.perform();
  }

  loadDataForSelectedObjectType = task(async() => {
    if (!this.selectedObjectType) return;

    try {
      await all([
        loadObjectTypeAttributes(this.store, this.selectedObjectType.id),
        this.store.query('form-tab', { filter: { object_type_id: this.selectedObjectType.id } })
      ]);
    } catch (error) {
      handleError(this, error);
    }
  });

  @action
  objectTypeFilter(ot: ObjectTypeModel) {
    if (this.objectTypeAttribute.inverse) {
      return this.objectTypeRelationships.find((type) => type.id == ot.id);
    }
    return true;
  }

  @action
  attributeFilter(attr: ObjectTypeAttributeModel | ObjectTypeProperty) {
    if (attr instanceof ObjectTypeAttributeModel && attr.targetObjectType) {
      return attr.targetObjectType.id == this.objectType.id && !attr.inverse;
    }
    return false;
  }

  @action
  confirm() {
    this.dialogService.close(true);
  }

  @action
  cancel() {
    this.dialogService.close(false);
  }
}
