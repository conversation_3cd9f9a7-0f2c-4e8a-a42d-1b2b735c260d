import Component from '@glimmer/component';
import { action } from '@ember/object';
import { tracked } from '@glimmer/tracking';
import { service } from '@ember/service';
import { VariantedObjectType, VariantedFormTab, VariantedFormElement, variantFor } from 'frontend/utils/variants';
import { handleError } from 'frontend/utils/handle-error';
import { DATA_TYPE } from 'frontend/models/object-type-attribute';
import { FORM_ELEMENT_TYPE } from 'frontend/types/ObjectTypeAttribute';
import FormTabModel from 'frontend/models/form-tab';
import DialogService, { DIALOG_TYPE } from 'frontend/services/dialog';
import IntlService from 'ember-intl/services/intl';
import Store from '@ember-data/store';
import Owner from '@ember/owner';
import ObjectModel from 'frontend/models/object';
import RouterService from '@ember/routing/router-service';
import AllowedValues from 'frontend/utils/allowed-values';
import { inputTypeOptions } from 'frontend/utils/input-type-options';
import { task } from 'ember-concurrency';
import { dasherize } from '@ember/string';
import { decrementItemPositions, incrementItemPositions } from 'frontend/utils/item-positions';
import Viewpoint, { VIEWPOINT_CALLBACK } from 'frontend/services/viewpoint';
import { all } from 'rsvp';
import { abortTransition, hasDirtyChecksEnabled, ignoreDirtyChecks } from 'frontend/utils/transition';
import Transition from '@ember/routing/transition';
import { dogmaticSet } from 'frontend/utils/store/dogmatic-set';
import saveFormElement from 'frontend/utils/form-builder/save-form-element';
import CreateLookupFormElementModal from 'frontend/utils/modals/create-lookup-form-element-modal';
import ModalService from 'frontend/services/modal';
import ObjectTypeAttributePathModel from 'frontend/models/object-type-attribute-path';
import { generateObjectTypeAttributeKey } from 'frontend/utils/form-builder/helpers';
import CalculationModel, { CALCULATION_TYPE } from 'frontend/models/calculation';
import ToastService from 'frontend/services/toast';
import { TOAST_TYPES } from 'frontend/utils/toast';
import { OBJECT_TYPE_ATTRIBUTES_LIMIT } from 'frontend/models/object-type';
import { loadFormTabs } from 'frontend/utils/form-tab-loader';
import { destroyRecord } from 'frontend/utils/destroy-record';
import FormElementModel from 'frontend/models/form-element';

interface FormBuilderIndexSignature {
  Args: {
    objectType: VariantedObjectType;
    mockObject: ObjectModel;
  };
}

export default class FormBuilderIndexComponent extends Component<FormBuilderIndexSignature> {
  @service
  declare store: Store;

  @service('dialog')
  declare dialog: DialogService;

  @service
  declare intl: IntlService;

  @service('modal')
  declare modalService: ModalService;

  @service('toast')
  declare toastService: ToastService;

  @tracked
  declare selectedFormElement?: VariantedFormElement;

  @tracked
  declare selectedFormTab?: VariantedFormTab;

  @tracked
  declare selectedDefaultObjects?: Array<ObjectModel>;

  @tracked hideHiddenFormItems = false;

  @tracked
  declare showRightPanelMobile: boolean;

  @service
  declare router: RouterService;

  @service
  declare viewpoint: Viewpoint;

  declare _warnBeforeUnload: () => void;

  declare _onrouteChange: (transition: Transition) => void;

  declare _onViewpointChange: () => void;

  declare _beforeViewpointChange: () => Promise<boolean>;

  declare collapsedSections: Array<VariantedFormTab>;

  constructor(owner: Owner, args: FormBuilderIndexSignature['Args']) {
    super(owner, args);
    this.collapsedSections = [];

    this._warnBeforeUnload = this.warnBeforeUnload.bind(this);
    window.addEventListener('beforeunload', this._warnBeforeUnload);

    this._onrouteChange = this.onrouteChange.bind(this);
    this.router.on('routeWillChange', this._onrouteChange);

    this._beforeViewpointChange = this.beforeViewpointChange.bind(this);
    this.viewpoint.registerCallback(VIEWPOINT_CALLBACK.BEFORE_VIEWPOINT_CHANGE, this._beforeViewpointChange);

    this._onViewpointChange = this.onViewpointChange.bind(this);
    this.viewpoint.registerCallback(VIEWPOINT_CALLBACK.VIEWPOINT_CHANGE, this._onViewpointChange);

    loadFormTabs(this.store, this.args.objectType.id);
  }

  willDestroy() {
    window.removeEventListener('beforeunload', this._warnBeforeUnload);
    this.router.off('routeWillChange', this._onrouteChange);
    this.viewpoint.unregisterCallback(VIEWPOINT_CALLBACK.VIEWPOINT_CHANGE, this._onViewpointChange);
    this.viewpoint.unregisterCallback(VIEWPOINT_CALLBACK.BEFORE_VIEWPOINT_CHANGE, this._beforeViewpointChange);
    super.willDestroy();
  }

  get inputTypeOptions() {
    return inputTypeOptions(this);
  }

  get selectedInputType() {
    const elementType = Object.values(FORM_ELEMENT_TYPE).find((type) => {
      return (
        type.dataType == this.selectedFormElement?.objectTypeAttribute?.dataType
        && type.inputType == this.selectedFormElement?.inputType
        && type.targetClass == this.selectedFormElement?.objectTypeAttribute?.targetClass
      );
    });

    return this.inputTypeOptions.find((option) => (option.value == elementType?.key));
  }

  @action
  setShowRightPanelMobile(boolean: boolean) {
    this.showRightPanelMobile = boolean;
  }

  @action
  async fetchSelectedDefaultObjects() {
    this.selectedDefaultObjects = await this.fetchSelectedObjects.perform();
  }

  @action
  async selectFormElement(formElement?: VariantedFormElement) {
    if (await this.checkDirtyState()) return;
    if (this.selectedFormElement === formElement) {
      this.selectedFormElement = undefined;
    } else {
      this.selectedFormElement = formElement;
      this.selectedFormTab = undefined;
      this.resetRightPanelTab();

      if (formElement?.objectTypeAttribute?.dataType === DATA_TYPE.RELATIONSHIP) {
        this.fetchSelectedDefaultObjects();
      } else {
        this.selectedDefaultObjects = [];
      }
    }
    this.setShowRightPanelMobile(false);
  }

  resetRightPanelTab() {
    document.getElementById('builder-properties')?.classList.add('active');
    document.getElementById('builder-properties')?.classList.add('show');
    document.getElementById('builder-properties-tab')?.classList.add('active');

    document.getElementById('builder-conditionals')?.classList.remove('active');
    document.getElementById('builder-conditionals')?.classList.remove('show');
    document.getElementById('builder-conditionals-tab')?.classList.remove('active');

    document.getElementById('builder-validations')?.classList.remove('active');
    document.getElementById('builder-validations')?.classList.remove('show');
    document.getElementById('builder-validations-tab')?.classList.remove('active');
  }

  fetchSelectedObjects = task(async() => {
    try {
      const attribute = this.selectedFormElement?.objectTypeAttribute;
      let ids = this.selectedFormElement?.objectTypeAttribute?.defaultValue?.value;
      if (!ids || (Array.isArray(ids) && ids.length <= 0)) return [];
      if (Array.isArray(ids)) { ids = ids.join(','); }

      let objects;
      if (attribute?.targetClass === "Object") {
        const filter = {
          object_type_id: attribute.targetObjectTypeId,
          ids
        };
        objects = await this.store.query('object', { filter });
      } else {
        objects = await this.store.query(dasherize(attribute?.targetClass ? attribute.targetClass : ""), { filter: { ids } });
      }
      return objects ? objects.slice() : [];
    } catch {
      // Since we're just loading data in a select, no need to show an error message
      return [];
    }
  });

  @action
  deleteSelectedFormItem() {
    if (this.selectedFormElement) {
      this.deleteFormElement();
    } else if (this.selectedFormTab) {
      this.deleteFormTab();
    }
  }

  @action
  async deleteFormElement() {
    if (!this.selectedFormElement) return;

    try {
      const result = await this.dialog.confirm(this.intl.t("dialog.confirm_delete_form_element", { name: this.selectedFormElement.objectTypeAttribute?.name }));
      if (result) {
        const formTab = this.selectedFormElement.formTab;
        const objectTypeAttribute = this.selectedFormElement.objectTypeAttribute;
        await destroyRecord(this.selectedFormElement.original);
        if (this.selectedFormElement && formTab) {
          decrementItemPositions(this.selectedFormElement.position, formTab.original.formItems);
        }
        if (objectTypeAttribute?.original.isNew) {
          objectTypeAttribute.original.unloadRecord();
        } else if (objectTypeAttribute) {
          dogmaticSet(objectTypeAttribute.original, { archived: true });
        }
        this.selectedFormElement = undefined;
      }
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  async deleteFormTab() {
    if (!this.selectedFormTab) return;
    try {
      let dialogText;
      const hasChildren = !!this.selectedFormTab.formItems.length;
      const tabName = this.selectedFormTab.name;
      if (!this.selectedFormTab.parent) {
        if (hasChildren) {
          dialogText = this.intl.t("dialog.confirm_delete_form_tab", { name: tabName, confirmText: this.selectedFormTab.name, htmlSafe: true });
        } else {
          dialogText = this.intl.t("dialog.confirm_delete_empty_form_tab", { name: tabName });
        }
      } else if (hasChildren) {
        dialogText = this.intl.t("dialog.confirm_delete_section", { name: tabName, confirmText: this.selectedFormTab.name, htmlSafe: true });
      } else {
        dialogText = this.intl.t("dialog.confirm_delete_empty_section", { name: tabName });
      }

      const options = {
        confirmText: hasChildren ? tabName : undefined,
        title: hasChildren ? this.intl.t("dialog.confirmation_required") : this.intl.t("dialog.title")
      };

      const result = await this.dialog.confirm(dialogText, options);
      if (result) {
        if (this.selectedFormTab) {
          const items = this.selectedFormTab.parent?.original.formItems ?? this.args.objectType.original.rootFormTabs;
          decrementItemPositions(this.selectedFormTab.position, items);

          // Keep references as they are gone when the form tab is destroyed
          const children = this.selectedFormTab.original.children.slice();
          const elements = this.selectedFormTab.original.formElements.slice();
          await destroyRecord(this.selectedFormTab.original, (formTab) => {
            this.removeChildrenRecursively(formTab, children, elements);
          });
        }
        this.selectedFormTab = undefined;
      }
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  async setSelectedFormTab(tab?: VariantedFormTab) {
    if (await this.checkDirtyState()) return;
    if (this.selectedFormTab === tab) {
      this.selectedFormTab = undefined;
      this.setShowRightPanelMobile(false);
    } else {
      this.resetRightPanelTab();
      this.selectedFormElement = undefined;
      this.selectedFormTab = tab;
      this.setShowRightPanelMobile(true);
    }
  }

  async beforeViewpointChange() {
    return !(await this.checkDirtyState());
  }

  onViewpointChange() {
    this.selectedFormElement = undefined;
    this.selectedFormTab = undefined;
    this.resetRightPanelTab();
  }

  async onrouteChange(transition: Transition) {
    if (transition.isAborted) return;

    if (hasDirtyChecksEnabled(transition) && (this.selectedFormElement?.isDirty || this.selectedFormTab?.isDirty)) {
      abortTransition(transition, this.router);

      const result = await this.dialog.confirm(this.intl.t("dialog.confirm_unsaved_changes_object"));

      if (result) {
        this.resetDirtyState();
        ignoreDirtyChecks(transition);
        transition.retry();
      }
    }
  }

  warnBeforeUnload(event: BeforeUnloadEvent) {
    if (!(this.selectedFormElement?.isDirty || this.selectedFormTab?.isDirty)) return;
    event.preventDefault();
    // setting returnValue to a value other than null or undefined will prompt the dialog
    // In older browsers, the returnValue of the event is displayed in this dialog.
    return event.returnValue = this.intl.t("dialog.confirm_unsaved_form");
  }

  calculationChanged(calculation: CalculationModel) {
    const formulaChanged = Object.keys(calculation.changedAttributes()).includes('formula');
    const pathsChanged = calculation.objectTypeAttributePaths.some((path) => path.hasDirtyAttributes);

    return formulaChanged || pathsChanged;
  }

  async confirmRecalculation(calculation: CalculationModel) {
    const message = this.intl.t("dialog.confirm_recalculation", { htmlSafe: true });

    if (!await this.dialog.confirm(message)) return;

    try {
      await this.store.adapterFor("calculation").recalculate(calculation);
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  async saveFormElement() {
    if (!this.selectedFormElement) return;

    const formElementIsNew = !!this.selectedFormElement.isNew;

    const calculation = this.selectedFormElement.objectTypeAttribute?.calculation;
    const shouldRecalculate = !formElementIsNew && calculation && (calculation.hasDirtyAttributes || this.calculationChanged(calculation));

    try {
      const formElement = await saveFormElement(this.selectedFormElement, this.args.objectType);

      if (shouldRecalculate) this.confirmRecalculation(calculation);

      return formElement;
    } catch (error) {
      if (error.errors?.[0]?.code == 'CircularReference') {
        handleError(this, error.errors[0], { text: error.errors[0].detail });
        return undefined;
      }
      handleError(this, error);
      return undefined;
    }
  }

  @action
  async checkDirtyState() {
    if (this.selectedFormElement?.isDirty || this.selectedFormTab?.isDirty) {
      const result = await this.dialog.confirm(this.intl.t("dialog.confirm_unsaved_form"));
      if (result) {
        await this.resetDirtyState();
      }
      return !result;
    }
    return false;
  }

  async resetDirtyState() {
    await all([
      this.selectedFormElement?.resetDirtyState(),
      this.selectedFormTab?.resetDirtyState()
    ]);
  }

  @action
  async createFormElement(key: keyof typeof FORM_ELEMENT_TYPE | 'LOOKUP', formTab: VariantedFormTab, position: number, event?: GenericEvent<HTMLInputElement>) {
    if (await this.checkDirtyState()) return;
    if (this.args.objectType.nonArchivedAttributes.length >= OBJECT_TYPE_ATTRIBUTES_LIMIT) {
      if (key === 'LOOKUP' || FORM_ELEMENT_TYPE[key]?.dataType) {
        this.showOtaLimitToast();
        return;
      }
    }
    event?.stopPropagation();

    if (key === 'LOOKUP') {
      this.openLookupFormElementModal(formTab, position);
      return;
    }

    const type = FORM_ELEMENT_TYPE[key];
    let objectTypeAttribute;

    if (type.dataType) {
      const allowedValues = type.defaultAllowedValues && new AllowedValues(type.defaultAllowedValues);
      objectTypeAttribute = this.store.createRecord('object-type-attribute', {
        dataType: type.dataType,
        targetClass: type.targetClass,
        targetColumnName: type.targetColumnName,
        canUpdate: true,
        allowedValues: allowedValues,
        relationshipKind: type.relationshipKind,
        inverseRelationshipKind: type.inverseRelationshipKind,
        objectTypeVariantId: this.args.objectType.objectTypeVariant?.id,
        inverse: false,
        locked: true
      });

      if (type.key == FORM_ELEMENT_TYPE.OBJECT.key) {
        const options = { options: { objectType: this.args.objectType, objectTypeAttribute: objectTypeAttribute } };
        const result = await this.dialog.openDialog(DIALOG_TYPE.CREATE_OBJECT_TYPE_RELATIONSHIP, options) as false;
        // Stop form element creation when there's no targetObjectTypeId
        if (!result) {
          objectTypeAttribute.unloadRecord();
          return;
        }
      }
    }

    incrementItemPositions(position, formTab.original.formItems);

    const formElement = this.store.createRecord('form-element', {
      formTab: formTab.original,
      inputType: type.inputType,
      position: position,
      objectTypeAttribute: objectTypeAttribute,
      text: type.textKey ? `${this.intl.t("input_type." + type.textKey)} - ${this.intl.t("new")}` : undefined,
      objectTypeVariantId: this.args.objectType.objectTypeVariant?.id,
      locked: true
    });

    formElement.isDirty = true;
    if (type.key == FORM_ELEMENT_TYPE.OBJECT.key) {
      try {
        formElement.customDisplay = "Thumbnail";
        await saveFormElement(variantFor(formElement), this.args.objectType);
      } catch (error) {
        objectTypeAttribute?.unloadRecord();
        formElement.unloadRecord();
        if (error.errors && error.errors[0].code == 'ObjectTypeAttributesLimit') {
          this.showOtaLimitToast();
        } else {
          handleError(this, error);
        }

        return;
      }
    }
    await this.selectFormElement(variantFor(formElement, this.args.objectType.objectTypeVariant));
    this.setShowRightPanelMobile(true);
  }

  removeChildrenRecursively(formTab: FormTabModel, children: Array<FormTabModel>, formElements: Array<FormElementModel>) {
    children.forEach((child) => {
      this.removeChildrenRecursively(child, child.children.slice(), child.formElements.slice());
      child.unloadRecord();
    });
    formElements.forEach((element) => element.unloadRecord());
    const collapsedTab = this.collapsedSections.find((variantedTab) => variantedTab.original === formTab);
    if (collapsedTab) this.collapsedSections.removeObject(collapsedTab);
  }

  openLookupFormElementModal(formTab: VariantedFormTab, position: number) {
    const modal = new CreateLookupFormElementModal({
      context: {
        objectType: formTab.objectType,
        createLookupField: async(name, objectTypeAttributePath) => {
          await this.createLookupFormElement(formTab, position, name, objectTypeAttributePath);
          return true;
        }
      }
    });
    this.modalService.open(modal);
  }

  @action
  async createLookupFormElement(formTab: VariantedFormTab, position: number, name: string, objectTypeAttributePath: ObjectTypeAttributePathModel) {
    const objectTypeAttribute = this.store.createRecord('object-type-attribute', {
      name: name,
      key: generateObjectTypeAttributeKey(this.args.objectType.original, name),
      dataType: objectTypeAttributePath.dataAttribute?.dataType,
      objectTypeVariantId: this.args.objectType.objectTypeVariant?.id,
      canUpdate: false,
      calculated: true,
      inverse: false,
      locked: true
    });

    const calculation = this.store.createRecord('calculation', { objectTypeAttribute: objectTypeAttribute, objectTypeAttributePaths: [objectTypeAttributePath],
      calculationType: CALCULATION_TYPE.LOOKUP });

    // increment first for some reason because otherwise it also increments the new item...
    incrementItemPositions(position, formTab.original.formItems);

    const formElement = this.store.createRecord('form-element', {
      formTab: formTab.original,
      inputType: objectTypeAttributePath.dataAttribute?.formElements?.[0]?.inputType, // this might be a problem
      position: position,
      objectTypeAttribute: objectTypeAttribute,
      objectTypeVariantId: this.args.objectType.objectTypeVariant?.id,
      locked: true
    });

    try {
      await saveFormElement(variantFor(formElement), this.args.objectType);
      await this.selectFormElement(variantFor(formElement, this.args.objectType.objectTypeVariant));
      this.setShowRightPanelMobile(true);
    } catch (error) {
      objectTypeAttributePath.unloadRecord();
      calculation.unloadRecord();
      objectTypeAttribute.unloadRecord();
      formElement.unloadRecord();
      throw error;
    }
  }

  @action
  async toggleHiddenFormItems() {
    // check dirty state since toggling the icon can set selectedFormElement undefined
    if (await this.checkDirtyState()) return;

    if (this.selectedFormElement?.isHidden) {
      this.selectedFormElement = undefined;
    }

    this.hideHiddenFormItems = !this.hideHiddenFormItems;
  }

  showOtaLimitToast() {
    this.toastService.create({
      title: this.intl.t('error.title'),
      type: TOAST_TYPES.FAILURE,
      text: this.intl.t('error.object_type_attributes_limit', { object_type_attributes_limit: OBJECT_TYPE_ATTRIBUTES_LIMIT })
    });
  }
}
