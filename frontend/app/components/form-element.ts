import Component from '@glimmer/component';
import { action, set, get } from '@ember/object';
import { service } from '@ember/service';
import { cached, tracked } from '@glimmer/tracking';
import { task, timeout } from 'ember-concurrency';
import { trackedFunction } from "reactiveweb/function";
import { guidFor } from '@ember/object/internals';
import Store from '@ember-data/store';
import IntlService from 'ember-intl/services/intl';
import ObjectModel from 'frontend/models/object';
import BaseModel from 'frontend/models/base-model';
import { INPUT_TYPE } from 'frontend/models/form-element';
import { VariantedFormElement, VariantedObjectTypeAttribute } from 'frontend/utils/variants';
import { triggerFrontendEvent } from 'frontend/models/frontend-event';
import DialogService from 'frontend/services/dialog';
import JobService from 'frontend/services/job';
import RouterService from '@ember/routing/router-service';
import { toNumber } from 'frontend/utils/data-type-helpers';
import { FORM_ELEMENT_TYPE, ObjectTypeAttributeValidationError } from 'frontend/types/ObjectTypeAttribute';
import CurrentUser from 'frontend/services/current-user';
import { handleError } from 'frontend/utils/handle-error';
import ObjectQueryRuleModel from 'frontend/models/object-query-rule';
import ObjectAttachmentModel from 'frontend/models/object-attachment';
import Viewpoint from 'frontend/services/viewpoint';
import { DISPLAY_TYPE } from 'frontend/models/conditional-display';
import { DATA_TYPE } from 'frontend/models/object-type-attribute';
import { VALIDATION_TYPE } from 'frontend/models/object-type-attribute-validation';
import { isIncompleteFormElement } from 'frontend/utils/variants/publish';
import { getDefaultGridThumbnail } from 'frontend/utils/thumbnail';
import CalculationModel, { CALCULATION_TYPE } from 'frontend/models/calculation';
import { generateCalculationPayload, getCalculationsDependingOnObjectTypeAttribute } from 'frontend/utils/calculations';
import escapeHtml from 'frontend/utils/escape-html';
import { sanitize } from 'dompurify';
import autosize from 'autosize';
import { addNewRelationshipObject } from 'frontend/utils/add-new-relationship-object';
import QrScannerModal from 'frontend/utils/modals/qr-scanner-modal';
import ModalService from 'frontend/services/modal';

const SUGGESTION_TIMOUT = 800;
const CALCULATION_TIMEOUT = 500;
const MIN_LENGTH_SUGGESTION = 3;
const FILTER_RELATIONSHIPS_PAGE_SIZE = 15;

interface FormElementSignature {
  Args: {
    formElement: VariantedFormElement;
    object: ObjectModel;
    disabled?: boolean;
    openEditObjectModal: (object: ObjectModel) => void;
    openAttachments: (object: ObjectModel, selectedAttachment: ObjectAttachmentModel, event: Event) => void;
    editMode?: boolean;
    promisesToAwait?: Array<Promise<unknown>>;
    errors?: Array<ObjectTypeAttributeValidationError>;
    hideLabel?: boolean;
  };
}

interface ObjectQueryParams {
  object_type_id?: string;
  rules: Array<{
    rule_type: ObjectQueryRuleModel["ruleType"];
    value?: ObjectQueryRuleModel["value"];
    sort_order?: ObjectQueryRuleModel["sortOrder"];
    column_name?: ObjectQueryRuleModel["columnName"];
    operator?: ObjectQueryRuleModel["operator"];
    object_type_attribute_id?: string;
  }>;
  team_id?: string;
  target_link?: {
    object_id: string;
    object_type_attribute_id: string;
  };
}

export default class FormElementComponent extends Component<FormElementSignature> {
  @service
  declare store: Store;

  @service
  declare dialog: DialogService;

  @service('job')
  declare jobService: JobService;

  @service
  declare intl: IntlService;

  @service
  declare router: RouterService;

  @service
  declare viewpoint: Viewpoint;

  @service
  declare currentUser: CurrentUser;

  @service('modal')
  declare modalService: ModalService;

  @tracked recommendations?: Array<ObjectModel>;

  declare _fetchRelationsId: string;
  declare _updatedAt: Date;

  // Needed to load attachments/relations of the related objects, so they can be shown in the thumbnails and when opening the related object in a modal
  fetchRelations = trackedFunction(this, async() => {
    // If any query below includes this.args.object in its response, the properties used here will be invalidated.
    // Would those properties be tracked, this function would be triggered again, potentially resulting in an infinite loop.
    // Therefore we scope reactivity more narrowinlgy to object id.
    if (this._fetchRelationsId === this.args.object.id && this._updatedAt === this.args.object.updatedAt) return;
    this._fetchRelationsId = this.args.object.id;
    this._updatedAt = this.args.object.updatedAt;
    await Promise.resolve();

    if (this.args.formElement.customDisplay !== "Thumbnail" && this.args.formElement.customDisplay !== "Table") return;
    if (!this.attribute || !this.args.object.id) return;
    const relationshipObjects = this.args.object.getRelationshipObjects(this.attribute.original);
    if (relationshipObjects.length) {
      if (this.attribute.relationshipKind === "single") {
        await this.store.findRecord('object', relationshipObjects[0].id, { reload: true });
      } else {
        const ids = relationshipObjects.map((rel) => { return rel.id; });
        await this.store.query('object', { reload: true, filter: { ids: ids.join(',') } });
      }
    }
  });

  get uniqueId() {
    return guidFor(this);
  }

  get placeholder() {
    if (this.args.formElement.inputType === INPUT_TYPE.RELATIONSHIP && this.attribute?.inverse) return "";
    if (this.disabled && !this.args.editMode) return "";
    if (this.args.formElement.placeholder) return this.args.formElement.placeholder;

    const key = Object.values(FORM_ELEMENT_TYPE).find((type) => {
      return type.dataType == this.args.formElement?.objectTypeAttribute?.dataType
        && type.inputType == this.args.formElement.inputType
        && type.targetClass == this.args.formElement?.objectTypeAttribute?.targetClass;
    })?.placeholder;

    return key ? this.intl.t(key) : "";
  }

  get attribute() {
    return this.args.formElement.objectTypeAttribute;
  }

  get isRequired() {
    return this.attribute?.objectTypeAttributeValidations.some((validation) => validation.validationType === VALIDATION_TYPE.PRESENCE);
  }

  get defaultValue() {
    return this.attribute?.defaultValue;
  }

  get shouldRecommend() {
    return this.args.object.objectType.recommendedObjectTypeAttribute?.id == this.attribute?.id && this.args.object.isNew;
  }

  get relationshipModels() {
    if (!this.attribute) return;

    return this.args.object.getRelationshipObjects(this.attribute.original);
  }

  get relationshipDisplayKey() {
    return this.attribute?.relationshipDisplayKey;
  }

  get hideRelationshipSelect() {
    const disabled = this.args.formElement.options?.select?.disabled;
    const canShowRelationships = ["Thumbnail", "Table"].includes(this.args.formElement.customDisplay ?? "");
    const isSingle = this.attribute?.relationshipKind == "single";
    const isSelected = this.relationshipModels?.length;
    const isInverse = this.attribute?.inverse;

    if (disabled) return !isSelected || canShowRelationships;
    if (canShowRelationships) return isSelected && (isSingle || isInverse);
    return false;
  }

  get isRelationshipSelectRemoveDisabled() {
    return this.disabled || this.attribute?.inverse;
  }

  get allowRelationshipDataLoad() {
    return !this.args.formElement.options?.select?.disabled;
  }

  get showNewRelationshipButton() {
    if (!this.attribute) return false;

    // the new button is disabled in the settings of the field
    if (this.args.formElement.options?.newRelationshipButton?.disabled) return false;

    // the relationship is an inverse one
    if (this.attribute.inverse) {
      // and the underlying object is not yet persisted
      if (this.args.object.get('isNew') && !this.args.object.isMockObject) return false;

      // and is linked with multiple forward relationships
      if (((this.attribute.linkedObjectTypeAttributes?.length) ?? 0) > 1) return false;
    }

    // the relationship is of type single and already has a value
    if (this.attribute.isSingleRelationship && this.relationshipModels?.length) return false;

    return true;
  }

  get hideRelationshipSelected() {
    return ["Thumbnail", "Table"].includes(this.args.formElement.customDisplay ?? "");
  }

  get showUnlockedIcon() {
    return this.args.editMode && !this.args.formElement.isLocked;
  }

  get showHiddenIcon() {
    return this.showUnlockedIcon && this.args.formElement.isHidden;
  }

  get thumbnail() {
    if (!this.args.formElement.objectTypeAttribute?.targetObjectType) return;

    return this.args.formElement.thumbnail ?? getDefaultGridThumbnail(this.args.formElement.objectTypeAttribute.targetObjectType);
  }

  get isLookupField() {
    return this.args.formElement.objectTypeAttribute?.calculation?.calculationType == CALCULATION_TYPE.LOOKUP;
  }

  get isFormula() {
    return this.args.formElement.objectTypeAttribute?.calculated && !this.isLookupField;
  }

  get formulaPopover() {
    if (!this.attribute?.calculation?.formula) return '';

    let res = `<div><p class="fw-bold small-font mb-1">= ${escapeHtml(this.attribute?.calculation?.formula)}</p>
      <p class="mb-0">
        ${this.intl.t('query.where')}:
      </p>
    `;
    this.attribute?.calculation?.objectTypeAttributePaths.forEach((path) => {
      res += `<div class="d-flex my-1">
        <div class="bg-info me-2 px-2 fw-32 text-center text-white rounded align-self-start">
          ${escapeHtml(path.key)}
        </div>
        <div class="fw-bold small-font w-100">
          ${escapeHtml(path.explanation ?? "")}
        </div>
      </div>`;
    });
    res += '</div>';

    return sanitize(res);
  }

  @cached
  get calculations() {
    if (!this.args.formElement.objectTypeAttribute) { return; }

    return getCalculationsDependingOnObjectTypeAttribute(this.args.formElement.objectTypeAttribute);
  }

  get userSelectorFilter() {
    let teamId;

    if (this.attribute?.targetTeamId) {
      teamId = this.attribute.targetTeamId;
    } else if (this.args.formElement.teamFilterable) {
      teamId = this.viewpoint.selectedTeam?.id;
    }

    return { team_id: teamId };
  }

  // Returns a list of selectable values to use in the tom select. This is a function instead of a getter to avoid many
  // unnecessary calculations and rerenders of the select when the select is not used. This function is called when the tom select opens
  @action
  selectableValuesTomSelect() {
    let values = this.attribute?.allowedValues?.selectableValues || [];
    values = values.filter((value) => !value.hidden);

    const selectableFilters = this.args.formElement.conditionalDisplays.filter((display) => display.displayType === DISPLAY_TYPE.FILTER_SELECTABLE);
    if (selectableFilters.length === 0) { return values; }

    // Create a union of all values for which the conditional evaluates to true
    // This union of values are selectable through the select
    const selectableValues = selectableFilters.reduce((set, conditionalDisplay) => {
      if (conditionalDisplay.applicableSelectableValues && conditionalDisplay.evaluateFor(this.args.object)) {
        conditionalDisplay.applicableSelectableValues.forEach((val) => set.add(val));
      }
      return set;
    }, new Set<unknown>());

    return values.filter((val) => {
      return selectableValues.has(val.value);
    });
  }

  get tomSelectedField() {
    if (!this.attribute?.key) return;

    const value = get(this.args.object.values, this.attribute.key);
    return this.attribute.allowedValues?.values?.find((val) => val.value === value);
  }

  get disabled() {
    if (this.args.disabled) return true;
    if (this.args.formElement.uneditable(this.args.object)) return true;
    if (this.showMasterLock) return true;

    return false;
  }

  get showMasterLock() {
    const item = this.attribute || this.args.formElement;
    const isEditableSelect = this.args.formElement.inputType === INPUT_TYPE.SELECT && (this.attribute?.allowVariantOptions || this.selectableValuesTomSelect().some((val) => !val.locked));
    return this.args.editMode && item.isOwnedByMaster && !isEditableSelect;
  }

  get showLinkedObjectsLink() {
    return !this.args.editMode && (this.attribute?.dataType === DATA_TYPE.RELATIONSHIP && this.attribute.targetClass === "Object");
  }

  get isIncompleteFormElement() {
    return isIncompleteFormElement(this.args.formElement);
  }

  @action
  loadSelectData(query: string) {
    return this.fetchSelectableObjects.perform(query);
  }

  fetchSelectableObjects = task(async(query: string) => {
    const page = { size: FILTER_RELATIONSHIPS_PAGE_SIZE, number: 1 };
    const element = this.attribute;

    if (!element?.targetObjectTypeId) return [];

    try {
      let teamId;
      if (this.args.formElement.teamFilterable && !(this.currentUser.user.admin) && this.viewpoint.selectedTeam) {
        teamId = this.viewpoint.selectedTeam.id;
      }

      const filter: ObjectQueryParams = {
        object_type_id: element.targetObjectTypeId,
        rules: [{ rule_type: "sort", sort_order: "desc", column_name: "id" }],
        team_id: teamId,
        target_link: { object_id: this.args.object.id, object_type_attribute_id: element.id }
      };

      if (query) {
        const filterRule: typeof filter['rules'][number] = {
          rule_type: "filter",
          value: query
        };
        if (element.targetColumnName) {
          filterRule.column_name = element.targetColumnName;
          filterRule.operator = 'equals';
        } else if (element.targetObjectTypeAttributeId) {
          filterRule.object_type_attribute_id = element.targetObjectTypeAttributeId;
          filterRule.operator = 'contains';
        }
        filter.rules.push(filterRule);
      }

      const objects = await this.store.query('object', { filter, page });
      return objects?.slice();
    } catch {
      // Since we're just loading data in a select, no need to show an error message
      return [];
    }
  });

  @action
  selectObject(selection: BaseModel | Array<BaseModel>) {
    if (!this.attribute) return;
    if (this.attribute.relationshipKind == "single") {
      this.args.object.updateSingleRelationshipForAttribute(this.attribute.original, selection as BaseModel);
    } else {
      this.args.object.updateRelationshipsForAttribute(this.attribute.original, selection as Array<BaseModel>);
    }
    this.args.object.setDirtyState(true);
    this.triggerCalculations();
  }

  getRecommendations = task({ restartable: true }, async(value: string) => {
    try {
      await timeout(SUGGESTION_TIMOUT);
      if (value.length >= MIN_LENGTH_SUGGESTION) {
        const objects = await this.store.query('object', { filter: { object_type_id: this.args.formElement.formTab.objectType.id, suggestion: value }, page: { size: 5, number: 1 } });
        this.recommendations = objects.slice();
      }
    } catch {
      // Recommendations are optional, so no need to show an error message
    }
  });

  @action
  updateProp(value: unknown) {
    if (this.args.object.isMockObject) return;

    if (this.attribute) {
      if (this.attribute.dataType === DATA_TYPE.NUMBER) {
        value = toNumber(value);
        if (value === undefined) return;
      }
      set(this.args.object.values, this.attribute.key, value);
      this.args.object.setDirtyState(true);
      this.triggerCalculations();
    }
  }

  triggerCalculations() {
    if (!this.calculations?.length) { return; }

    this.sendCalculationRequest.perform(this.calculations);
  }

  sendCalculationRequest = task({ restartable: true }, async(calculations: Array<CalculationModel>) => {
    await timeout(CALCULATION_TIMEOUT);

    const payload = generateCalculationPayload(calculations, this.args.object);

    try {
      const response = await this.store.adapterFor('object-type').previewCalculate(this.args.object.objectType, payload);
      if (response.attributes) {
        Object.keys(response.attributes).forEach((key) => {
          this.args.object.updateAttribute(key, response.attributes[key]);
        });
      }
    } catch (error) {
      // do something here???
      // eslint-disable-next-line no-console
      console.error(error);
    }
  });

  @action
  toggleProp() {
    if (this.attribute) {
      this.updateProp(!this.args.object.values[this.attribute.key]);
    }
  }

  @action
  setProp(event: GenericEvent<HTMLInputElement>) {
    const value = event.target.value;
    this.updateProp(value);
    if (this.shouldRecommend) {
      this.getRecommendations.perform(value);
    }
  }

  get isDatePickerType() {
    return this.args.formElement.inputType === INPUT_TYPE.DATE || this.args.formElement.inputType === INPUT_TYPE.DATETIME;
  }

  @action
  unselectObject(object: BaseModel) {
    if (!this.attribute) return;
    if (this.attribute.relationshipKind == "single") {
      this.args.object.updateSingleRelationshipForAttribute(this.attribute.original);
    } else {
      const objects = this.relationshipModels?.filter((obj) => { return obj.id !== object.id; }) ?? [];
      this.args.object.updateRelationshipsForAttribute(this.attribute.original, objects);
    }
    this.args.object.setDirtyState(true);
    this.triggerCalculations();
  }

  @action
  showLinkedObjects() {
    const targetObjectTypeId = this.attribute?.targetObjectTypeId;
    if (!targetObjectTypeId) return;

    const transition = this.router.transitionTo('objects', targetObjectTypeId);
    transition.data.linkedObject = this.args.object;
  }

  async saveObject() {
    await this.args.object.save();
  }

  frontendEventTask = task({ drop: true }, async() => {
    if (!this.args.formElement.frontendEvent) return;
    if (this.args.object.isMockObject) return;

    try {
      const saveBeforeExecute = this.args.formElement.frontendEvent.saveBeforeExecute;
      if (!saveBeforeExecute) {
        if (this.attribute) {
          this.updateProp(true);
        }
      } else {
        if ((this.args.object.isDirty || !this.args.object.id) && !(await this.dialog.confirm(this.intl.t("dialog.confirm_trigger_event"))) as boolean) return;
        if (this.attribute) {
          this.updateProp(true);
        }
        await this.saveObject();
        this.args.object.setDirtyState(false);
      }
      const job = await triggerFrontendEvent(this.args.formElement.frontendEvent, this.args.object);
      if (!job) return;

      await new Promise<void>((resolve) => {
        this.jobService.subscribeJobActivity(job, {
          completed: () => resolve(),
          failed: () => resolve()
        });
      });
    } catch (error) {
      handleError(this, error);
    }
  });

  @action
  triggerFrontEndEvent() {
    this.frontendEventTask.perform();
  }

  @action
  autoSizeTextarea(element: HTMLElement) {
    autosize.update(element);
  }

  @action
  addNewRelationship(relationshipAttribute: VariantedObjectTypeAttribute) {
    const team = this.args.object.team;
    addNewRelationshipObject(this, relationshipAttribute, this.args.object, team, { calculations: this.calculations });
  }

  @action
  openQrScannerModal() {
    const modal = new QrScannerModal({
      context: {
        returnRawData: true
      },
      callbacks: {
        onFinishScan: (code: string) => {
          this.updateProp(code);
        }
      }
    });
    this.modalService.open(modal);
  }
}
