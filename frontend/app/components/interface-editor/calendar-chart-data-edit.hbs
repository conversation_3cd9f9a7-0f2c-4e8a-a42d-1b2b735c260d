<div class="mb-4">
  <label class="col-form-label">{{t "object_type"}}</label>

  <TomSelects::ObjectTypeSelector
    @placeholder={{t "select_object"}}
    @onChange={{this.selectObjectType}}
    @selected={{@view.objectType}}
    @disableClear={{true}}
    @disabled={{true}}
  />
</div>

{{#if @view.objectType}}
  <Template::TimeRangeConfiguration
    @view={{@view}}
    @updateTemplate={{this.updateTemplate}}
  />

  <div class="mb-4">
    <label class="col-form-label">{{t "charts.display_value"}}</label>
    <div class="form-check form-switch">
      <CheckBox @checked={{this.serieOptions.label.show}} @onChange={{this.toggleShowValue}} />
    </div>
  </div>

  <Template::SelectConfiguration
    @templateSelect={{this.templateSelect}}
    @objectType={{@view.objectType}}
    @updateTemplate={{this.updateTemplate}}
  />

  <ColorIntervals
    @colorIntervals={{@view.visualisation.layout.colorIntervals}}
    @onChange={{this.markInterfaceElementDirty}}
  />

  <Template::FilterConfiguration
    @template={{@view.template}}
    @objectType={{@view.objectType}}
    @updateTemplate={{this.updateTemplate}}
    @interfaceElement={{@view.interfaceElement}}
  />
{{/if}}
