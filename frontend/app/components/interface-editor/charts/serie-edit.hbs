<div class="mb-2">
  <label for="{{@prefix}}name" class="col-form-label">{{t "attributes.name"}}</label>
  <input
    class="form-control"
    type="text"
    id="{{@prefix}}name"
    value={{@serie.name}}
    {{on "input" this.setName}}
  >
</div>

{{yield}}

<div class="mb-2">
  <label for="{{@prefix}}type" class="col-form-label">{{t "type"}}</label>
  <select class="form-select" id="{{@prefix}}type" {{on "change" this.setType}}>
    <option selected={{if (eq @serie.type "line") "selected"}} value="line">{{t "charts.line"}}</option>
    <option selected={{if (eq @serie.type "bar") "selected"}} value="bar">{{t "charts.bar"}}</option>
    <option selected={{if (eq @serie.type "area") "selected"}} value="area">{{t "charts.area"}}</option>
  </select>
</div>

{{#if (eq @serie.type "bar")}}
  <div class="mb-2">
    <label for="{{@prefix}}bar-stack-id" class="col-form-label">{{t "charts.bar_stack_id"}}</label>
    <input
      class="form-control"
      type="text"
      id="{{@prefix}}bar-stack-id"
      value={{@serie.stack}}
      {{on "input" this.setStack}}
    >
  </div>
{{/if}}

<div class="mb-2">
  <label for="{{@prefix}}color" class="col-form-label">{{t "color"}}</label>
  <div class="input-group">
    <button
      type="button"
      id="{{@prefix}}color"
      class="color-circle border border-secondary"
      style={{style "background-color" this.serieColor}}
      {{on "click" this.showColorDialog}}
    >
    </button>
  </div>
</div>

<div class="mb-2">
  <label for="{{@prefix}}label" class="col-form-label">{{t "charts.display_value"}}</label>
  <div class="d-flex">
    <div class="form-check form-switch">
      <CheckBox
        id="{{@prefix}}label"
        @checked={{this.showLabel}}
        @onChange={{this.toggleLabel}}
      />
    </div>
    {{#if (and this.showLabel this.showLabelPosition)}}
      <div class="form-check ms-2">
        <RadioButton
          @value="top"
          @selection={{@serie.label.position}}
          @onChange={{this.setLabelPosition}}
          name="{{@prefix}}label-position"
          id="{{@prefix}}label-position-top"
        />
        <label class="form-check-label" for="{{@prefix}}label-position-top">
          {{t "position.top"}}
        </label>
      </div>
      <div class="form-check ms-2">
        <RadioButton
          @value="inside"
          @selection={{@serie.label.position}}
          @onChange={{this.setLabelPosition}}
          name="{{@prefix}}label-position"
          id="{{@prefix}}label-position-inside"
        />
        <label class="form-check-label" for="{{@prefix}}label-position-inside">
          {{t "position.inside"}}
        </label>
      </div>
    {{/if}}
  </div>
</div>
