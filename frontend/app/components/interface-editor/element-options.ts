import { service } from "@ember/service";
import Component from "@glimmer/component";
import { IntlService } from "ember-intl";
import InterfaceElementModel from "frontend/models/interface-element";
import SectionModel from "frontend/models/section";

interface ElementOptionsSignature {
  Args: {
    createInterfaceElement: (type: INTERFACE_ELEMENT_OPTIONS, parent: SectionModel | InterfaceElementModel, interfaceElement?: InterfaceElementModel) => void;
    parent: SectionModel | InterfaceElementModel;
    interfaceElement?: InterfaceElementModel;
    allowGridOptions?: boolean;
  };
  Element: HTMLDivElement;
}

export enum INTERFACE_ELEMENT_OPTIONS {
  HTML = 'html',
  TEXT = 'text',
  ATTACHMENT = 'attachment',
  BUTTON = 'button',
  RECORD_PICKER = 'record_picker',
  OBJECT_TABLE = 'objectTable',
  OBJECT_LIST = 'objectList',
  OBJECT_GALLERY = 'objectGallery',
  OBJECT_KANBAN = 'objectKanban',
  OBJECT_CALENDAR = 'objectCalendar',
  MULTI_OBJECT_LIST = 'multiObjectList',
  INTERFACE_FIELD = 'interfaceField',
  NUMBER = 'number',
  CHART = 'lineChart',
  PIE = 'pieChart',
  GAGE = 'gageChart',
  RADAR = 'radarChart',
  PYRAMID = 'pyramidChart',
  FILTER = 'filter',
  NESTED_GRID = 'nested_grid',
  OBJECT_TIMELINE = 'objectTimeline',
  LINK = 'link',
  CALENDAR_CHART = 'calendarChart'
}

export const INTERFACE_ELEMENT_META = {
  [INTERFACE_ELEMENT_OPTIONS.CHART]: { label: 'interface.linebar_chart', icon: 'fal fa-chart-line' },
  [INTERFACE_ELEMENT_OPTIONS.PIE]: { label: 'interface.pie_chart', icon: 'fal fa-chart-pie-simple' },
  [INTERFACE_ELEMENT_OPTIONS.RADAR]: { label: 'interface.radar_chart', icon: 'fal fa-chart-radar' },
  [INTERFACE_ELEMENT_OPTIONS.GAGE]: { label: 'interface.gage_chart', icon: 'fal fa-gauge-simple-high' },
  [INTERFACE_ELEMENT_OPTIONS.NUMBER]: { label: 'interface.number', icon: 'fal fa-sharp fa-square-1' },
  [INTERFACE_ELEMENT_OPTIONS.PYRAMID]: { label: 'interface.pyramid_chart', icon: 'fal chart-pyramid' },
  [INTERFACE_ELEMENT_OPTIONS.HTML]: { label: 'interface.html', icon: 'fal fa-code' },
  [INTERFACE_ELEMENT_OPTIONS.TEXT]: { label: 'interface.text', icon: 'fal fa-text' },
  [INTERFACE_ELEMENT_OPTIONS.ATTACHMENT]: { label: 'interface.attachment', icon: 'fal fa-photo-film' },
  [INTERFACE_ELEMENT_OPTIONS.BUTTON]: { label: 'interface.button', icon: 'fal fa-play' },
  [INTERFACE_ELEMENT_OPTIONS.RECORD_PICKER]: { label: 'interface.record_picker', icon: 'fal fa-list-dropdown' },
  [INTERFACE_ELEMENT_OPTIONS.INTERFACE_FIELD]: { label: 'field', icon: 'fal fa-sharp fa-input-text' },
  [INTERFACE_ELEMENT_OPTIONS.FILTER]: { label: 'interface.filter', icon: 'fal fa-filter' },
  [INTERFACE_ELEMENT_OPTIONS.OBJECT_TABLE]: { label: 'visualisation.objectTable', icon: 'fal fa-table' },
  [INTERFACE_ELEMENT_OPTIONS.OBJECT_LIST]: { label: 'visualisation.objectList', icon: 'fal fa-bars' },
  [INTERFACE_ELEMENT_OPTIONS.OBJECT_GALLERY]: { label: 'visualisation.objectGallery', icon: 'fal fa-grid' },
  [INTERFACE_ELEMENT_OPTIONS.OBJECT_KANBAN]: { label: 'visualisation.objectKanban', icon: 'fal fa-square-kanban' },
  [INTERFACE_ELEMENT_OPTIONS.OBJECT_CALENDAR]: { label: 'visualisation.objectCalendar', icon: 'fal fa-calendar' },
  [INTERFACE_ELEMENT_OPTIONS.NESTED_GRID]: { label: 'interface.nested_grid', icon: 'fal fa-border-none' },
  [INTERFACE_ELEMENT_OPTIONS.OBJECT_TIMELINE]: { label: 'visualisation.objectTimeline', icon: 'fal fa-bars-staggered' },
  [INTERFACE_ELEMENT_OPTIONS.MULTI_OBJECT_LIST]: { label: 'visualisation.multiObjectList', icon: 'fal fa-list-ol' },
  [INTERFACE_ELEMENT_OPTIONS.LINK]: { label: 'interface.links', icon: 'fal fa-grid-2-plus' },
  [INTERFACE_ELEMENT_OPTIONS.CALENDAR_CHART]: { label: 'interface.calendar_chart', icon: 'fal fa-calendar-days' }
};

export default class ElementOptions extends Component<ElementOptionsSignature> {
  @service
  declare intl: IntlService;

  INTERFACE_ELEMENT_META = INTERFACE_ELEMENT_META;

  get allowGridOptions() {
    return this.args.allowGridOptions ?? true;
  }

  get gridOptions() {
    return [
      INTERFACE_ELEMENT_OPTIONS.NESTED_GRID
    ];
  }

  get interfaceElementOptions() {
    return [
      INTERFACE_ELEMENT_OPTIONS.CHART,
      INTERFACE_ELEMENT_OPTIONS.PIE,
      INTERFACE_ELEMENT_OPTIONS.RADAR,
      INTERFACE_ELEMENT_OPTIONS.GAGE,
      INTERFACE_ELEMENT_OPTIONS.CALENDAR_CHART,
      INTERFACE_ELEMENT_OPTIONS.NUMBER,
      INTERFACE_ELEMENT_OPTIONS.HTML,
      INTERFACE_ELEMENT_OPTIONS.TEXT,
      INTERFACE_ELEMENT_OPTIONS.ATTACHMENT,
      INTERFACE_ELEMENT_OPTIONS.BUTTON,
      INTERFACE_ELEMENT_OPTIONS.RECORD_PICKER,
      INTERFACE_ELEMENT_OPTIONS.INTERFACE_FIELD,
      INTERFACE_ELEMENT_OPTIONS.FILTER,
      INTERFACE_ELEMENT_OPTIONS.LINK
    ];
  }

  get viewElementOptions() {
    return [
      INTERFACE_ELEMENT_OPTIONS.OBJECT_TABLE,
      INTERFACE_ELEMENT_OPTIONS.OBJECT_LIST,
      INTERFACE_ELEMENT_OPTIONS.OBJECT_GALLERY,
      INTERFACE_ELEMENT_OPTIONS.OBJECT_KANBAN,
      INTERFACE_ELEMENT_OPTIONS.OBJECT_CALENDAR,
      INTERFACE_ELEMENT_OPTIONS.OBJECT_TIMELINE,
      INTERFACE_ELEMENT_OPTIONS.MULTI_OBJECT_LIST
    ];
  }
}
