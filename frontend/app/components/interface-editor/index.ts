import Component from "@glimmer/component";
import { action } from "@ember/object";
import RouterService from "@ember/routing/router-service";
import Transition from "@ember/routing/transition";
import { service } from "@ember/service";
import { tracked } from "@glimmer/tracking";
import InterfaceElementModel, { INTERFACE_ELEMENT_TYPE } from "frontend/models/interface-element";
import SectionModel from "frontend/models/section";
import { IntlService } from "ember-intl";
import { handleError } from "frontend/utils/handle-error";
import Store from '@ember-data/store';
import { next } from "@ember/runloop";
import { updateItemPositions } from "frontend/utils/item-positions";
import DialogService, { DIALOG_TYPE } from "frontend/services/dialog";
import { abortTransition, hasDirtyChecksEnabled, ignoreDirtyChecks } from "frontend/utils/transition";
import { INTERFACE_ELEMENT_OPTIONS } from "frontend/components/interface-editor/element-options";
import { WIDGET_TYPE } from "frontend/models/widget";
import { DISPLAY_TYPE } from "frontend/models/interface-element-attachment";
import { loadObjectTypeAttributes } from "frontend/utils/object-type-attributes-loader";
import ObjectTypeModel from "frontend/models/object-type";
import { VIEW_TYPE } from "frontend/models/visualisation";
import { setAllMasterColumns } from "frontend/utils/visualisation/column";
import { all } from "rsvp";
import { TrackedSeriesOptions, VisualisationLayout } from "frontend/utils/visualisation";
import { defaultChartTemplate, defaultNumberTemplate, defaultPieTemplate, defaultRadarTemplate } from "frontend/utils/template";
import { isScrolledIntoView } from "frontend/utils/html-utils";
import FilterProperties from "frontend/utils/view/filter-properties";
import { cloneInterfaceElement } from "frontend/utils/clone-interface-element";
import { task } from "ember-concurrency";
import { loadFormTabs } from "frontend/utils/form-tab-loader";
import { DATA_TYPE } from "frontend/models/object-type-attribute";
import GridStackService from "frontend/services/grid-stack";
import { ROLE_TYPE } from "frontend/models/object-type-role";
import CurrentUserService from "frontend/services/current-user";
import Viewpoint, { SYSTEM_VIEWPOINT } from "frontend/services/viewpoint";
import InterfaceModel from "frontend/models/interface";
import Owner from "@ember/owner";
import ViewModel from "frontend/models/view";
import ModalService from "frontend/services/modal";
import ObjectTimelineSettingsModal from "frontend/utils/modals/object-timeline-settings-modal";
import { SERIES_DISPLAY_TYPE } from "frontend/utils/visualisation/series";
import { destroyRecord } from "frontend/utils/destroy-record";
import { unloadDependents } from "frontend/utils/interface-element";

interface InterfaceEditorIndexSignature {
  Args: {
    interface: InterfaceModel;
    tabIndex: number;
    setTab: (tab: SectionModel) => void;
  };
}

export default class InterfaceEditorIndexComponent extends Component<InterfaceEditorIndexSignature> {
  @service
  declare store: Store;

  @service
  declare intl: IntlService;

  @service('modal')
  declare modalService: ModalService;

  @service
  declare dialog: DialogService;

  @service
  declare router: RouterService;

  @service
  declare currentUser: CurrentUserService;

  @service
  declare viewpoint: Viewpoint;

  @service('grid-stack')
  declare gridStackService: GridStackService;

  @tracked selectedInterfaceElement?: InterfaceElementModel;

  @tracked selectedSection?: SectionModel;

  @tracked showRightPanelMobile = false;

  declare _warnBeforeUnload: () => void;

  declare _onrouteChange: (transition: Transition) => void;

  get tab() {
    return this.args.interface.rootSections[this.args.tabIndex];
  }

  get hasDirtyState() {
    return this.selectedInterfaceElement?.isDirty || this.selectedSection?.isDirty || this.selectedInterfaceElement?.interfaceElementAttachment?.isDirty;
  }

  constructor(owner: Owner, args: InterfaceEditorIndexSignature['Args']) {
    super(owner, args);

    this.registerDirtyWarnings();
  }

  willDestroy() {
    this.unregisterDirtyWarnings();
    super.willDestroy();
  }

  registerDirtyWarnings() {
    this._warnBeforeUnload = this.warnBeforeUnload.bind(this);
    window.addEventListener('beforeunload', this._warnBeforeUnload);

    this._onrouteChange = this.onrouteChange.bind(this);
    this.router.on('routeWillChange', this._onrouteChange);
  }

  async onrouteChange(transition: Transition) {
    if (transition.isAborted) return;

    if (hasDirtyChecksEnabled(transition) && !transition.queryParamsOnly && this.hasDirtyState) {
      abortTransition(transition, this.router);

      const result = await this.dialog.confirm(this.intl.t("dialog.confirm_unsaved_interface"));

      if (result) {
        await this.rollbackInterfaceElement();
        ignoreDirtyChecks(transition);
        transition.retry();
      }
    }
  }

  warnBeforeUnload(event: BeforeUnloadEvent) {
    if (!this.hasDirtyState) return;
    event.preventDefault();
    // setting returnValue to a value other than null or undefined will prompt the dialog
    // In older browsers, the returnValue of the event is displayed in this dialog.
    return event.returnValue = this.intl.t("dialog.confirm_unsaved_changes_interface");
  }

  unregisterDirtyWarnings() {
    window.removeEventListener('beforeunload', this._warnBeforeUnload);
    this.router.off('routeWillChange', this._onrouteChange);
  }

  @action
  async selectInterfaceElement(interfaceElement?: InterfaceElementModel) {
    this.setShowRightPanelMobile(false);
    if (await this.checkDirtyInterfaceElement()) return;

    if (this.selectedInterfaceElement === interfaceElement) {
      this.selectedInterfaceElement = undefined;
    } else {
      if (interfaceElement) {
        await loadAssociatedInterfaceElementData(interfaceElement);
      }

      this.selectedSection = undefined;
      this.selectedInterfaceElement = interfaceElement;
      this.resetRightPanelTab();
      next(() => {
        if (interfaceElement) {
          this.scrollInterfaceElementIntoView(interfaceElement);
        }
      });
    }
  }

  scrollInterfaceElementIntoView(interfaceElement: InterfaceElementModel) {
    const element = document.querySelector(`.grid-stack-item[data-interface-element-id="${interfaceElement.id}"]`);
    const leftContainer = document.querySelector('.left');
    const sectionContainer = document.querySelector('.section');
    if (!element || !leftContainer || !sectionContainer) return;
    if (!isScrolledIntoView(element, leftContainer) || !isScrolledIntoView(element, sectionContainer))
      element.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "nearest" });
  }

  resetRightPanelTab() {
    document.getElementById('builder-appearance')?.classList.add('active');
    document.getElementById('builder-appearance')?.classList.add('show');
    document.getElementById('builder-appearance-tab')?.classList.add('active');

    const nonActiveTabIds = ['builder-type', 'builder-action', 'builder-data', 'builder-link'];

    nonActiveTabIds.forEach((tabId) => {
      document.getElementById(tabId)?.classList.remove('active');
      document.getElementById(tabId)?.classList.remove('show');
      document.getElementById(`${tabId}-tab`)?.classList.remove('active');
    });
  }

  @action
  async createInterfaceElement(type: INTERFACE_ELEMENT_OPTIONS, parent: SectionModel | InterfaceElementModel, sourceInterfaceElement?: InterfaceElementModel) {
    if (await this.checkDirtyInterfaceElement()) return;

    const gridPosition = this.generateGridPosition(parent, sourceInterfaceElement, type);

    const interfaceElement = this.store.createRecord("interface-element", {
      section: parent instanceof SectionModel ? parent : parent.section,
      parent: parent instanceof SectionModel ? undefined : parent,
      gridPosition: gridPosition
    });

    if (interfaceElement.parent?.backgroundColor) {
      interfaceElement.backgroundColor = '#FFFFFF';
    }

    switch (type) {
    case INTERFACE_ELEMENT_OPTIONS.HTML:
    case INTERFACE_ELEMENT_OPTIONS.TEXT:
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.WIDGET;
      this.createWidget(type as unknown as WIDGET_TYPE, interfaceElement);
      break;
    case INTERFACE_ELEMENT_OPTIONS.ATTACHMENT:
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.ATTACHMENT;
      this.createInterfaceElementAttachment(interfaceElement);
      break;
    case INTERFACE_ELEMENT_OPTIONS.BUTTON:
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.BUTTON;
      this.createInterfaceElementButton(interfaceElement);
      break;
    case INTERFACE_ELEMENT_OPTIONS.RECORD_PICKER: {
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.RECORD_PICKER;
      const recordPicker = await this.createRecordPicker(interfaceElement);
      if (recordPicker?.objectType) {
        await all([loadFormTabs(this.store, recordPicker.objectType.id), loadObjectTypeAttributes(this.store, recordPicker.objectType.id)]);
      } else {
        interfaceElement.unloadRecord();
        return;
      }
      break;
    }
    case INTERFACE_ELEMENT_OPTIONS.INTERFACE_FIELD:
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.FIELD;
      this.createInterfaceElementField(interfaceElement);
      break;
    case INTERFACE_ELEMENT_OPTIONS.FILTER: {
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.FILTER;
      const filterElement = await this.createInterfaceFilterElement(interfaceElement);

      if (!filterElement) return;

      if (filterElement?.objectType) {
        await all([loadFormTabs(this.store, filterElement.objectType.id), loadObjectTypeAttributes(this.store, filterElement.objectType.id)]);
      }
      break;
    }
    case INTERFACE_ELEMENT_OPTIONS.LINK: {
      this.createInterfaceElementLink(interfaceElement);
      break;
    }
    case INTERFACE_ELEMENT_OPTIONS.OBJECT_TABLE:
    case INTERFACE_ELEMENT_OPTIONS.OBJECT_LIST:
    case INTERFACE_ELEMENT_OPTIONS.MULTI_OBJECT_LIST:
    case INTERFACE_ELEMENT_OPTIONS.OBJECT_GALLERY:
    case INTERFACE_ELEMENT_OPTIONS.OBJECT_KANBAN:
    case INTERFACE_ELEMENT_OPTIONS.OBJECT_CALENDAR:
    case INTERFACE_ELEMENT_OPTIONS.OBJECT_TIMELINE:
    case INTERFACE_ELEMENT_OPTIONS.NUMBER:
    case INTERFACE_ELEMENT_OPTIONS.GAGE:
    case INTERFACE_ELEMENT_OPTIONS.CHART:
    case INTERFACE_ELEMENT_OPTIONS.PIE:
    case INTERFACE_ELEMENT_OPTIONS.RADAR:
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.VIEW;
      await this.createView(type as unknown as VIEW_TYPE, interfaceElement);
      if (!interfaceElement.view) return;
      break;
    case INTERFACE_ELEMENT_OPTIONS.NESTED_GRID:
      interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.NESTED_GRID;
    }

    await this.saveNewInterfaceElement(interfaceElement);

    if (type == INTERFACE_ELEMENT_OPTIONS.OBJECT_TIMELINE && interfaceElement.view) {
      this.setupTimeline(interfaceElement.view);
    }
  }

  async saveNewInterfaceElement(interfaceElement: InterfaceElementModel) {
    try {
      await interfaceElement.save();
      next(() => {
        this.selectInterfaceElement(interfaceElement);
        this.setShowRightPanelMobile(true);
      });
    } catch (error) {
      interfaceElement.unloadRecord();
      handleError(this, error);
    }
  }

  generateGridPosition(parent: SectionModel | InterfaceElementModel, sourceInterfaceElement?: InterfaceElementModel, type?: INTERFACE_ELEMENT_OPTIONS, useSourceSize = false) {
    const isSmallElement = type && [
      INTERFACE_ELEMENT_OPTIONS.BUTTON,
      INTERFACE_ELEMENT_OPTIONS.INTERFACE_FIELD,
      INTERFACE_ELEMENT_OPTIONS.NUMBER,
      INTERFACE_ELEMENT_OPTIONS.RECORD_PICKER
    ].includes(type);
    let h = isSmallElement ? 2 : 6;
    let w = isSmallElement ? 6 : 12;

    let y = 0;
    let x = 0;

    if (useSourceSize && sourceInterfaceElement) {
      w = sourceInterfaceElement.gridPosition.w ?? w;
      h = sourceInterfaceElement.gridPosition.h ?? h;
    }

    if (!sourceInterfaceElement) {
      // Check if the sorted can be avoided as it's not really needed here
      parent.sortedInterfaceElements.forEach((element) => {
        const elementEndHeight = (element.gridPosition.y ?? 0) + (element.gridPosition.h ?? 0);
        y = elementEndHeight > y ? elementEndHeight : y;
      });
    } else {
      // set y so it's right under source element
      y = (sourceInterfaceElement.gridPosition.y ?? 0) + (sourceInterfaceElement.gridPosition.h ?? 0);
      x = sourceInterfaceElement.gridPosition.x ?? 0;
      // new element should not be wider than source element
      if (sourceInterfaceElement.gridPosition.w && w > sourceInterfaceElement.gridPosition.w) w = sourceInterfaceElement.gridPosition.w;
    }
    return { x, y, w, h };
  }

  createInterfaceElementField(interfaceElement: InterfaceElementModel) {
    return this.store.createRecord("interface-element-field", { interfaceElement: interfaceElement });
  }

  createWidget(type: WIDGET_TYPE, interfaceElement: InterfaceElementModel) {
    return this.store.createRecord("widget", { widgetType: type, interfaceElement: interfaceElement });
  }

  createInterfaceElementAttachment(interfaceElement: InterfaceElementModel) {
    return this.store.createRecord("interface-element-attachment", { displayType: DISPLAY_TYPE.GRID, interfaceElement: interfaceElement });
  }

  createInterfaceElementButton(interfaceElement: InterfaceElementModel) {
    return this.store.createRecord('interface-element-button', {
      interfaceElement,
      label: `${this.intl.t('input_type.button')} - ${this.intl.t('new')}`
    });
  }

  async createRecordPicker(interfaceElement: InterfaceElementModel) {
    const objectType = await this.openObjectTypeDialog();
    if (!objectType) {
      interfaceElement.unloadRecord();
      return;
    }

    return this.store.createRecord("interface-element-record-picker", { interfaceElement: interfaceElement, objectType: objectType });
  }

  async createInterfaceFilterElement(interfaceElement: InterfaceElementModel) {
    const objectType = await this.openObjectTypeDialog();
    if (!objectType) {
      interfaceElement.unloadRecord();
      return;
    }

    return this.store.createRecord("interface-element-filter", { interfaceElement: interfaceElement, objectType: objectType });
  }

  createInterfaceElementLink(interfaceElement: InterfaceElementModel) {
    interfaceElement.elementType = INTERFACE_ELEMENT_TYPE.LINK;
    const link = this.store.createRecord('interface-element-link', { interfaceElement: interfaceElement });
    this.store.createRecord('interface-element-link-tab', {
      interfaceElementLink: link,
      name: this.intl.t('general'),
      position: 0
    });
  }

  async createView(type: VIEW_TYPE, interfaceElement: InterfaceElementModel) {
    let objectType;
    if (![VIEW_TYPE.MULTI_OBJECT_LIST].includes(type)) {
      objectType = await this.openObjectTypeDialog();
      if (!objectType) {
        interfaceElement.unloadRecord();
        return;
      }
    }

    const view = this.store.createRecord("view", { name: this.intl.t("query.new_view"), interfaceElement: interfaceElement, objectType: objectType, filterProperties: new FilterProperties(), allowCreateNewObject: !!objectType });
    const visualisation = this.store.createRecord("visualisation", { view: view, viewType: type, layout: new VisualisationLayout({}) });
    if (!objectType) {
      return view;
    }

    await loadObjectTypeAttributes(this.store, objectType.id);
    switch (type) {
    case VIEW_TYPE.OBJECT_TABLE:
      setAllMasterColumns(view);
      break;
    case VIEW_TYPE.OBJECT_KANBAN:
      view.filterAttribute = objectType.objectTypeAttributes.find((ota) => ota.allowedValues?.type == "Select");
      break;
    case VIEW_TYPE.NUMBER:
    case VIEW_TYPE.GAGE_CHART:
      visualisation.layout.series = { serie1: new TrackedSeriesOptions({}) };
      view.template = defaultNumberTemplate(objectType.id, "serie1");
      break;
    case VIEW_TYPE.LINE_CHART:
      visualisation.layout.series = { serie1: new TrackedSeriesOptions({ name: `${this.intl.t('charts.data_serie_options')} 1`, type: SERIES_DISPLAY_TYPE.BAR }) };
      visualisation.layout.label = "time";
      view.template = defaultChartTemplate(objectType.id, "serie1", "time");
      break;
    case VIEW_TYPE.PIE_CHART:
      visualisation.layout.label = "category";
      view.template = defaultPieTemplate(objectType.id, "category", "value");
      break;
    case VIEW_TYPE.RADAR_CHART:
      visualisation.layout.label = "category";
      visualisation.layout.legend = { show: false };
      view.template = defaultRadarTemplate(objectType.id, "category", "value");
      break;
    case VIEW_TYPE.OBJECT_CALENDAR:
      view.filterAttribute = objectType.objectTypeAttributes.find((ota) => [DATA_TYPE.DATE, DATA_TYPE.DATETIME].includes(ota.dataType));
      view.filterProperties = new FilterProperties({ date_period: 'week', date_offset: 'current' });
    }
    return view;
  }

  setupTimeline(view: ViewModel) {
    if (!view.objectType) return;

    const modal = new ObjectTimelineSettingsModal({
      context: {
        visualisation: view.visualisation,
        objectType: view.objectType
      },
      callbacks: {
        onConfirm: async() => {
          try {
            await view.save();
            if (view.interfaceElement) view.interfaceElement.isDirty = false;
          } catch (error) {
            handleError(this, error);
          }
        }
      }
    });
    this.modalService.open(modal);
  }

  async openObjectTypeDialog() {
    let objectType: ObjectTypeModel | undefined;
    await this.dialog.openDialog(DIALOG_TYPE.SELECT_OBJECT_TYPE, {
      context: {
        team: this.args.interface.dataScopeTeams[0],
        allowCreateObjectType: true
      },
      callbacks: {
        onChange: (value?: ObjectTypeModel) => {
          objectType = value;
        },
        afterObjectTypeCreate: async(objectType: ObjectTypeModel) => {
          const editorRole = objectType.objectTypeRoles.find((role) => role.roleType == ROLE_TYPE.EDITOR);
          const creatorRole = objectType.objectTypeRoles.find((role) => role.roleType == ROLE_TYPE.CREATOR);
          const sharePromises = this.args.interface.dataScopeTeams.map((team) => {
            if (editorRole) {
              this.store.createRecord('object-type-role-team-relationship', {
                team: team,
                objectTypeRole: editorRole
              });
            }

            if (creatorRole) {
              this.store.createRecord('object-type-role-team-relationship', {
                team: team,
                objectTypeRole: creatorRole
              });
            }

            const options = { data: { attributes: { team_id: team.id } } };
            return this.store.adapterFor('object-type').share(objectType.id, options);
          });

          try {
            await Promise.all([editorRole?.save(), creatorRole?.save(), ...sharePromises]);
            objectType.teams.push(...this.args.interface.dataScopeTeams);

            // Have to ensure sharing is finished before fetching the variant
            await this.store.query('object-type-variant', { url: { objectTypeId: objectType.id } });
          } catch {
            // No need to show error if the roles aren't updated
          }

          // Set viewpoint to owned to ensure the user is not editing the variant
          await this.viewpoint.setViewpoint(SYSTEM_VIEWPOINT.OWNED);
          this.router.transitionTo('objects.edit', objectType.id);
        }
      }
    });

    return objectType;
  }

  @action
  selectSection(section?: SectionModel) {
    this.resetRightPanelTab();
    this.setShowRightPanelMobile(true);
    this.selectedInterfaceElement = undefined;
    this.selectedSection = section == this.selectedSection ? undefined : section;
  }

  @action
  async setTab(tab: SectionModel) {
    if (await this.checkDirtyInterfaceElement()) return;

    this.args.setTab(tab);
  }

  @action
  moveRootSection(startSection: SectionModel, endSection: SectionModel) {
    updateItemPositions(startSection, endSection.position, this.args.interface.rootSections);
    this.args.setTab(startSection);
  }

  @action
  async saveMovedRootSection(tab: SectionModel) {
    try {
      await this.store.adapterFor('section').move(this.store, tab.id, tab.position);
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  async createNewRootSection() {
    if (await this.checkDirtyInterfaceElement()) return;

    try {
      const section = this.store.createRecord('section', {
        position: this.args.interface.rootSections.length,
        name: this.intl.t('new_tab'),
        interface: this.args.interface
      });
      await section.save();
      this.args.setTab(section);
      this.selectSection(section);
      this.setShowRightPanelMobile(true);
      next(() => {
        document.getElementById('section-title')?.focus();
      });
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  async deleteInterfaceElement() {
    if (!this.selectedInterfaceElement) return;

    const dialogText = this.selectedInterfaceElement.elementType === INTERFACE_ELEMENT_TYPE.NESTED_GRID
      ? this.intl.t("dialog.confirm_delete_nested_grid")
      : this.intl.t("dialog.confirm_delete_interface_element");
    const result = await this.dialog.confirm(dialogText);
    if (result) {
      const model = this.selectedInterfaceElement.parent ?? this.selectedInterfaceElement.section;
      try {
        // store children array reference before deletion
        const children = this.selectedInterfaceElement.children.slice();
        await destroyRecord(this.selectedInterfaceElement, (interfaceElement) => {
          unloadDependents(interfaceElement);
          children.forEach((nestedElement) => {
            unloadDependents(nestedElement);
            nestedElement.unloadRecord();
          });
        });

        this.selectedInterfaceElement = undefined;
        await this.gridStackService.savePositionsInterface(this.args.interface.id, model);
      } catch (e) {
        handleError(this, e);
      }
    }
  }

  cloneAndSelectInterfaceElement = task({ drop: true }, async(interfaceElement: InterfaceElementModel) => {
    if (await this.checkDirtyInterfaceElement()) return;

    const newGridPosition = this.generateGridPosition(interfaceElement.section, interfaceElement, undefined, true);
    const clonedInterfaceElement = await cloneInterfaceElement(this, interfaceElement, newGridPosition);

    if (clonedInterfaceElement) {
      next(() => {
        this.selectInterfaceElement(clonedInterfaceElement);
        this.setShowRightPanelMobile(true);
      });
    }
  });

  async rollbackInterfaceElement() {
    this.selectedInterfaceElement?.rollback();
    this.selectedSection?.rollbackAttributes();

    if (this.selectedInterfaceElement) this.selectedInterfaceElement.isDirty = false;
    if (this.selectedSection) this.selectedSection.isDirty = false;
    if (this.selectedInterfaceElement?.interfaceElementAttachment) this.selectedInterfaceElement.interfaceElementAttachment.isDirty = false;
  }

  get showRightPanel() {
    if (document.body.clientWidth < 992) return (this.selectedInterfaceElement || this.selectedSection) && this.showRightPanelMobile;
    return this.selectedInterfaceElement || this.selectedSection;
  }

  @action
  setShowRightPanelMobile(show: boolean) {
    this.showRightPanelMobile = show;
  }

  // Checks if an interface element is dirty
  // If method returns false, the element wasn't dirty or the user confirmed to rollback the dirty changes
  // If yes, the element won't be rollbacked and stays dirty
  async checkDirtyInterfaceElement() {
    if (!this.hasDirtyState) return false;

    const result = await this.dialog.confirm(this.intl.t("dialog.confirm_unsaved_changes_interface"));
    if (result) await this.rollbackInterfaceElement();
    return !result;
  }
}

async function loadAssociatedInterfaceElementData(interfaceElement: InterfaceElementModel) {
  let objectType: ObjectTypeModel | undefined;
  switch (interfaceElement.elementType) {
  case INTERFACE_ELEMENT_TYPE.RECORD_PICKER:
    objectType = interfaceElement.interfaceElementRecordPicker?.objectType;
    break;
  case INTERFACE_ELEMENT_TYPE.FIELD:
    objectType = interfaceElement.interfaceElementField?.interfaceElementRecordPicker?.objectType;
    break;
  case INTERFACE_ELEMENT_TYPE.FILTER:
    objectType = interfaceElement.interfaceElementFilter?.objectType;
    break;
  case INTERFACE_ELEMENT_TYPE.VIEW:
    if (interfaceElement?.view?.objectQueryView ||
        ([VIEW_TYPE.LINE_CHART, VIEW_TYPE.NUMBER, VIEW_TYPE.PIE_CHART, VIEW_TYPE.GAGE_CHART, VIEW_TYPE.RADAR_CHART].includes(interfaceElement.view?.visualisation.viewType as VIEW_TYPE) && interfaceElement.view?.configurableTemplate)) {
      objectType = interfaceElement.view?.objectType;
    }
  }

  if (objectType) {
    await Promise.all([
      loadFormTabs(objectType.store, objectType.id),
      loadObjectTypeAttributes(interfaceElement.store, objectType.id)
    ]);
  }
}
