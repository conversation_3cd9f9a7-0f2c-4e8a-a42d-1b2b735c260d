<div class="mb-4">
  <label class="col-form-label">{{t "object_type"}}</label>

  <TomSelects::ObjectTypeSelector
    @placeholder={{t "select_object"}}
    @onChange={{(noop)}}
    @selected={{@view.objectType}}
    @disableClear={{true}}
    @disabled={{true}}
  />
</div>

{{#if @view.objectType}}
  <Template::SelectConfiguration
    @templateSelect={{this.templateSelect}}
    @objectType={{@view.objectType}}
    @updateTemplate={{this.updateTemplate}}
  />

  <InterfaceEditor::Charts::TargetConfiguration
    @view={{@view}}
    @targetable={{@view.visualisation.layout}}
  />

  <ColorIntervals
    @colorIntervals={{@view.visualisation.layout.colorIntervals}}
    @onChange={{this.markInterfaceElementDirty}}
  />

  <Template::FilterConfiguration
    @template={{@view.template}}
    @objectType={{@view.objectType}}
    @updateTemplate={{this.updateTemplate}}
    @interfaceElement={{@view.interfaceElement}}
  />
{{/if}}
