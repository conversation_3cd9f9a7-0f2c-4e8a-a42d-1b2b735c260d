import Component from "@glimmer/component";
import ViewModel from "frontend/models/view";
import { action } from "@ember/object";
import { cached } from "@glimmer/tracking";
import { TemplateSelect } from "frontend/utils/template";
import Template from "frontend/utils/template-definition";

interface NumberDataEditSignature {
  Args: {
    view: ViewModel;
  };
}

export default class NumberDataEditSignatureComponent extends Component<NumberDataEditSignature> {
  @cached
  get templateSelect() {
    // TODO: verify whether template edits should happen through this class setup, or through template directly
    return new TemplateSelect(this.args.view.template);
  }

  @action
  updateTemplate(template: Template) {
    this.args.view.template = template;
    this.markInterfaceElementDirty();
  }

  @action
  markInterfaceElementDirty() {
    if (this.args.view.interfaceElement) {
      this.args.view.interfaceElement.isDirty = true;
    }
  };
}
