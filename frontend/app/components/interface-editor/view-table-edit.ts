import { action } from "@ember/object";
import { service } from "@ember/service";
import Component from "@glimmer/component";
import { cached } from "@glimmer/tracking";
import ObjectModel from "frontend/models/object";
import ObjectTypeModel from "frontend/models/object-type";
import ModalService from "frontend/services/modal";
import { buildMockObject, destroyMockObject } from "frontend/utils/mock-object";
import FilterSortWindowModal from "frontend/utils/modals/filter-sort-window-modal";
import ThumbnailEditorModal from "frontend/utils/modals/thumbnail-editor-modal";
import { loadObjectTypeAttributes } from "frontend/utils/object-type-attributes-loader";
import { changeThumbnailType, cloneFromDefaultThumbnail, getDefaultCardThumbnail, getDefaultGridThumbnail, getDefaultThumbnail, THUMBNAIL_TYPE } from "frontend/utils/thumbnail";
import { variantFor } from "frontend/utils/variants";
import { VIEW_TYPE } from "frontend/models/visualisation";
import { trackedFunction } from "reactiveweb/function";
import { setAllMasterColumns } from "frontend/utils/visualisation/column";
import ObjectTypeField from "frontend/types/ObjectTypeField";
import ObjectTypeAttributeModel, { DATA_TYPE } from "frontend/models/object-type-attribute";
import Store from '@ember-data/store';
import ViewModel from "frontend/models/view";
import InterfaceEventsService from "frontend/services/interface-events";
import { loadFormTabs } from "frontend/utils/form-tab-loader";
import FilterProperties from "frontend/utils/view/filter-properties";
import { SYSTEM_VIEWPOINT } from "frontend/services/viewpoint";

interface ViewTableEditSignature {
  Args: {
    view: ViewModel;
    // PLEASE DO NOT COPY THIS SETUP. THIS IS NOT GOOD
    tab: 'data' | 'type'; // TODO: quick and dirty
  };
}

export default class ViewTableEditComponent extends Component<ViewTableEditSignature> {
  @service('modal')
  declare modalService: ModalService;

  @service
  declare store: Store;

  @service
  declare interfaceEvents: InterfaceEventsService;

  _mockObject?: ObjectModel | null = null;

  get filterRulesSize() {
    return this.args.view.objectQueryView?.visibleRules.filter((rule) => rule.ruleType === 'filter').length || 0;
  }

  get sortRulesSize() {
    return this.args.view.objectQueryView?.visibleRules.filter((rule) => rule.ruleType === 'sort').length || 0;
  }

  get variantedObjectType() {
    if (this.args.view.objectType) {
      return variantFor(this.args.view.objectType);
    }
    return undefined;
  }

  get allowedThumbnailTypes() {
    const viewType = this.args.view.visualisation.viewType;
    if (!viewType) return [];

    switch (viewType) {
    case VIEW_TYPE.OBJECT_GALLERY: return [THUMBNAIL_TYPE.CARD];
    case VIEW_TYPE.OBJECT_KANBAN:
    case VIEW_TYPE.OBJECT_CALENDAR: return [THUMBNAIL_TYPE.CARD, THUMBNAIL_TYPE.GRID, THUMBNAIL_TYPE.HORIZONTAL, THUMBNAIL_TYPE.VERTICAL];
    default: return [THUMBNAIL_TYPE.GRID, THUMBNAIL_TYPE.HORIZONTAL];
    }
  }

  get isKanbanOrCalendar() {
    return this.args.view.visualisation.isCalendar || this.args.view.visualisation.isKanban;
  }

  get isTimeline() {
    return this.args.view.visualisation.isTimeline;
  }

  get objectTypeTeamScope() {
    return this.args.view.interfaceElement?.section.interface.dataScopeTeams[0] ?? SYSTEM_VIEWPOINT.OWNED;
  }

  @action
  groupedByAttributeFilter(field: ObjectTypeField) {
    if (this.args.view.visualisation.isKanban) {
      return field instanceof ObjectTypeAttributeModel && field.allowedValues?.type == "Select";
    }

    return field instanceof ObjectTypeAttributeModel && [DATA_TYPE.DATE, DATA_TYPE.DATETIME].includes(field.dataType);
  }

  mockObject = trackedFunction(this, async() => {
    const objectType = this.args.view.objectType;
    if (this._mockObject && this._mockObject.objectType === objectType) {
      return this._mockObject;
    }

    await Promise.resolve();
    if (objectType) {
      if (this._mockObject) {
        destroyMockObject(this._mockObject);
      }
      const variantObjectType = variantFor(objectType);
      this._mockObject = buildMockObject(this, variantObjectType);
    }

    return this._mockObject;
  });

  willDestroy(): void {
    super.willDestroy();
    if (this._mockObject) {
      destroyMockObject(this._mockObject);
      this._mockObject = null;
    }
  }

  @cached
  get thumbnail() {
    const view = this.args.view;
    if (!view.objectType) return;

    switch (view.visualisation.viewType) {
    case VIEW_TYPE.OBJECT_GALLERY: return view.visualisation.mainGalleryThumbnail ?? getDefaultCardThumbnail(view.objectType);
    case VIEW_TYPE.OBJECT_KANBAN:
    case VIEW_TYPE.OBJECT_CALENDAR: return view.visualisation.mainThumbnail ?? getDefaultCardThumbnail(view.objectType);
    default: return view.visualisation.mainListThumbnail ?? getDefaultGridThumbnail(view.objectType);
    }
  }

  get isThumbnailCard() {
    return this.thumbnail?.thumbnailType == THUMBNAIL_TYPE.CARD;
  }

  @action
  setViewType(viewType: VIEW_TYPE) {
    const visualisation = this.args.view.visualisation;
    if (visualisation) {
      visualisation.viewType = viewType;
      this.markInterfaceElementDirty();
      if (viewType === VIEW_TYPE.OBJECT_TABLE && visualisation.layout.columns.length === 0) {
        setAllMasterColumns(this.args.view);
      }
    }

    // set or reset filter properties
    this.args.view.filterAttribute = undefined;
    this.args.view.filterProperties = new FilterProperties();
  }

  @action
  async selectObjectType(objectType: ObjectTypeModel) {
    await Promise.all([
      loadObjectTypeAttributes(objectType.store, objectType.id),
      loadFormTabs(objectType.store, objectType.id)
    ]);
    this.args.view.objectType = objectType;

    if (this._mockObject) {
      destroyMockObject(this._mockObject);
    }
    const variantObjectType = variantFor(objectType);
    this._mockObject = buildMockObject(this, variantObjectType);
  }

  @action
  editFilterRules() {
    if (!this.args.view.objectQueryView) return;

    const modal = new FilterSortWindowModal({
      context: {
        objectQueryView: this.args.view.objectQueryView,
        objectType: this.args.view.objectType,
        type: 'filter',
        onRuleChange: this.markInterfaceElementDirty
      }
    });
    this.modalService.open(modal);
  }

  @action
  editSortRules() {
    if (!this.args.view.objectQueryView) return;

    const modal = new FilterSortWindowModal({
      context: {
        objectQueryView: this.args.view.objectQueryView,
        objectType: this.args.view.objectType,
        type: 'sort',
        onRuleChange: this.markInterfaceElementDirty
      }
    });
    this.modalService.open(modal);
  }

  markInterfaceElementDirty = () => {
    if (this.args.view.interfaceElement) {
      this.args.view.interfaceElement.isDirty = true;
    }
  };

  @action
  customizeThumbnail() {
    const view = this.args.view;
    if (!view?.objectType) { return; }

    const viewType = view.visualisation.viewType;
    let thumbnail = view.visualisation.mainThumbnail;

    if (!thumbnail) {
      // create thumbnail when there's no custom thumbnail
      const defaultThumbnail = getDefaultThumbnail([VIEW_TYPE.OBJECT_GALLERY, VIEW_TYPE.OBJECT_KANBAN, VIEW_TYPE.OBJECT_CALENDAR].includes(viewType) ? THUMBNAIL_TYPE.CARD : THUMBNAIL_TYPE.GRID, view.objectType);
      thumbnail = cloneFromDefaultThumbnail(defaultThumbnail, this.store);
    } else if (thumbnail.isTypeCard && viewType == VIEW_TYPE.OBJECT_LIST || !thumbnail.isTypeCard && viewType == VIEW_TYPE.OBJECT_GALLERY) {
      // Update thumbnail when the card type doesn't match the viewType
      changeThumbnailType(thumbnail, viewType == VIEW_TYPE.OBJECT_GALLERY ? THUMBNAIL_TYPE.CARD : THUMBNAIL_TYPE.GRID);
    }

    const modal = new ThumbnailEditorModal({
      context: {
        thumbnail: thumbnail,
        allowedThumbnailTypes: this.allowedThumbnailTypes,
        disallowThumbnailSave: false
      },
      callbacks: {
        setParentThumbnail: (thumbnail) => {
          thumbnail.visualisation = this.args.view.visualisation;
        }
      }
    });

    this.modalService.open(modal);
  }

  @action
  setFilterAttribute(field: ObjectTypeField) {
    // reset the filterAttribute first to make sure the view gets rerendered to fetch the data.
    this.args.view.filterAttribute = field as ObjectTypeAttributeModel;
    this.markInterfaceElementDirty();
    const interfaceElement = this.args.view.interfaceElement;
    if (interfaceElement) this.interfaceEvents.trigger(interfaceElement, 'refresh');
    if (this.args.view.visualisation.isCalendar) {
      if (this.args.view.filterProperties.noFilterSelected) {
        this.args.view.filterProperties.dateOffset = 'current';
        this.args.view.filterProperties.datePeriod = 'week';
      }
    }
  }

  @action
  multiObjectTypesFilter(objectType: ObjectTypeModel) {
    // This is a stop-gap solution for the object type list not being able to work with multiple selections
    return !this.args.view.objectTypes.includes(objectType);
  }

  @action
  addObjectType(objectType: ObjectTypeModel) {
    this.args.view.objectTypes.push(objectType);
  }

  @action
  removeObjectType(objectType: ObjectTypeModel) {
    this.args.view.objectTypes.removeObject(objectType);
  }
}
