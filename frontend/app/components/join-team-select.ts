import { action } from '@ember/object';
import { service } from '@ember/service';
import Component from '@glimmer/component';
import Team from 'frontend/models/team';
import User from 'frontend/models/user';
import CurrentUserService from 'frontend/services/current-user';
import { sortByKey } from 'frontend/utils/array';
import Store from '@ember-data/store';
import UserTeamJoinRequestModel from 'frontend/models/user-team-join-request';

interface JoinTeamSelectSignature {
  Args: {
    user: User;
  };
}

export default class JoinTeamSelectComponent extends Component<JoinTeamSelectSignature> {
  @service
  declare currentUser: CurrentUserService;

  @service('store')
  declare store: Store;

  get initialTeams() {
    return this.currentUser.user.teams.slice();
  }

  get teamJoinRequests() {
    return this.args.user.userTeamJoinRequests.filter((request) => !request.isDeleted);
  }

  get selectedTeams() {
    const requestTeams = this.args.user.userTeamJoinRequests.reduce((teams: Array<Team>, request) => {
      if (!request.isDeleted) teams.push(request.team);
      return teams;
    }, []);
    return this.args.user.teams.slice().concat(requestTeams);
  }

  @action
  addJoinTeamRequest(teams: Array<Team>) {
    const team = teams.lastObject;
    if (!team) return;

    if (this.initialTeams?.includes(team)) {
      this.args.user.teams.addObject(team);
    } else {
      const deletedRequest = this.args.user.userTeamJoinRequests.find((request) => request.team == team);
      if (deletedRequest) {
        deletedRequest.rollbackAttributes();
      } else {
        this.args.user.userTeamJoinRequests.createRecord({ team: team });
      }
    }
  }

  @action
  async loadTeams(query: string) {
    const filter = { breadcrumb: query, team_type: 'explicit' };
    try {
      return sortByKey((await this.store.query('team', { filter: filter })).slice(), "breadcrumb");
    } catch {
      // Since we're just loading data in a select, no need to show an error message
      return [];
    }
  }

  @action
  cancelJoinTeamRequest(request: UserTeamJoinRequestModel) {
    request.deleteRecord();
  }
}
