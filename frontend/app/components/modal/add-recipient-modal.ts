import Store from '@ember-data/store';
import { action } from '@ember/object';
import { service } from '@ember/service';
import { tracked } from '@glimmer/tracking';
import IntlService from 'ember-intl/services/intl';
import User from 'frontend/models/user';
import AddRecipientModal from 'frontend/utils/modals/add-recipient-modal';
import ModalComponent from 'frontend/components/modal/modal';
import { VariantedObjectTypeAttribute, VariantedObjectTypeRole, findObjectTypeVariantFor, variantFor } from 'frontend/utils/variants';
import ObjectTypeProperty from 'frontend/utils/object-type-property';
import { INPUT_TYPE } from 'frontend/models/form-element';
import ObjectTypeAttributeModel, { DATA_TYPE } from 'frontend/models/object-type-attribute';
import { loadObjectTypeAttributes } from 'frontend/utils/object-type-attributes-loader';
import Viewpoint from 'frontend/services/viewpoint';
import validateEmail from 'frontend/utils/validate-email';
import { loadFormTabs } from 'frontend/utils/form-tab-loader';

interface AddRecipientModalSignature {
  Args: {
    modal: AddRecipientModal;
  };
}

type RecipientType = 'user' | 'role' | 'field' | 'selectedRelatedUserField' | 'staticMailAddress';

const isAttribute = (field: unknown) => field instanceof VariantedObjectTypeAttribute;
const isProperty = (field: unknown) => field instanceof ObjectTypeProperty;

const isUserProperty = (field: ObjectTypeProperty | VariantedObjectTypeAttribute) => {
  return isProperty(field) && ['user_id', 'last_updated_by_id'].includes(field.key);
};

const isForwardRelation = (field: ObjectTypeProperty | VariantedObjectTypeAttribute) => {
  return isAttribute(field) && field.dataType === DATA_TYPE.RELATIONSHIP && !field.inverse;
};

const isUserRelation = (field: ObjectTypeProperty | VariantedObjectTypeAttribute) => {
  return isAttribute(field) && isForwardRelation(field) && field.targetClass === 'User';
};

export default class AddRecipientModalComponent extends ModalComponent<AddRecipientModalSignature> {
  @service
  declare intl: IntlService;

  @service
  declare store: Store;

  @service
  declare viewpoint: Viewpoint;

  @tracked recipientType: RecipientType = 'user';

  @tracked
  declare selectedRelationField?: VariantedObjectTypeAttribute;

  @tracked
  declare selectedRelatedUserField?: VariantedObjectTypeAttribute;

  @tracked
  declare selectedRole?: VariantedObjectTypeRole;

  @tracked
  declare selectedUser?: User;

  @tracked
  declare selectedMailAddressAttribute?: VariantedObjectTypeAttribute;

  @tracked
  declare selectedRelatedMailAddressAttribute?: VariantedObjectTypeAttribute;

  @tracked
  declare staticMailAddress?: string;

  get selectableRecipientTypes() {
    return [
      { label: this.intl.t("input_type.user"), value: 'user' },
      { label: this.intl.t("automation.user_selected_in_a_field"), value: 'selectedRelatedUserField' },
      { label: this.intl.t("role.role"), value: 'role' },
      { label: this.intl.t("automation.static_mail_address"), value: 'staticMailAddress' },
      { label: this.intl.t("automation.email_address_from_field"), value: 'field' }
    ] as const;
  }

  get selectedRecipientType() {
    return this.selectableRecipientTypes.find((type) => type.value === this.recipientType);
  }

  get selectableRoles() {
    return this.modal.context.variantedObjectType.objectTypeRoles.filter((role) => !this.mailAction.selectedRoles.includes(role.original));
  }

  get mailAction() {
    return this.modal.context.mailAction;
  }

  get variantedObjectType() {
    return this.modalContext.variantedObjectType;
  }

  get variantedRelatedObjectType() {
    if (!this.selectedMailAddressAttribute?.targetObjectType) return;

    const team = this.viewpoint.selectedTeam;
    const objectTypeVariant = findObjectTypeVariantFor(team, this.selectedMailAddressAttribute.targetObjectType);

    return variantFor(this.selectedMailAddressAttribute.targetObjectType, objectTypeVariant);
  }

  get variantedTargetObjectType() {
    const targetObjectType = this.selectedRelationField?.targetObjectType;

    if (!targetObjectType) return;

    const team = this.viewpoint.selectedTeam;
    const objectTypeVariant = findObjectTypeVariantFor(team, targetObjectType);

    return variantFor(targetObjectType, objectTypeVariant);
  }

  get disableConfirm() {
    if (this.selectedRole || this.selectedUser || validateEmail(this.staticMailAddress)) return false;

    if (this.selectedRelationField && !this.shouldSelectRelatedUser) return false;
    if (this.selectedRelationField && this.shouldSelectRelatedUser && this.selectedRelatedUserField) return false;

    if (this.selectedMailAddressAttribute) {
      if (this.selectedMailAddressAttribute.dataType == DATA_TYPE.RELATIONSHIP) return !this.selectedRelatedMailAddressAttribute;
      return false;
    }

    return true;
  }

  get typeText() {
    return ['field', 'selectedRelatedUserField'].includes(this.recipientType)
      ? this.intl.t("field")
      : this.intl.t("automation.recipient");
  }

  @action
  isSelectableMailAddressAttributeField(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    if (isProperty(field)) return false;

    if (field.dataType === DATA_TYPE.RELATIONSHIP && field.targetClass == "Object") return true;

    if (this.mailAction.automationMailActionAddressAttributes.some((addressAttribute) => addressAttribute.objectTypeAttribute == field.original && !addressAttribute.relationshipObjectTypeAttribute)) return false;

    return this.isTextAttribute(field);
  }

  @action
  isSelectableRelatedMailAddressAttributeField(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    if (!isAttribute(field)) return false;

    if (this.mailAction.automationMailActionAddressAttributes.some((addressAttribute) => {
      return addressAttribute.objectTypeAttribute == field.original && addressAttribute.relationshipObjectTypeAttribute == this.selectedMailAddressAttribute?.original;
    })) return false;

    return this.isTextAttribute(field);
  }

  isUserPropertyOrForwardRelation(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    return isUserProperty(field) || isForwardRelation(field);
  }

  isUserPropertyOrUserRelation(field: VariantedObjectTypeAttribute | ObjectTypeProperty) {
    return isUserProperty(field) || isUserRelation(field);
  }

  get recepientIds() {
    return {
      excluded_ids: this.mailAction.selectedUsers.map((user) => user.id)
    };
  }

  get shouldSelectRelatedUser() {
    return this.selectedRelationField && !this.isUserPropertyOrUserRelation(this.selectedRelationField);
  }

  @action
  async setSelectedRelation(association?: VariantedObjectTypeAttribute) {
    this.selectedRelatedUserField = undefined;
    this.selectedRelationField = association;

    if (association?.targetObjectType) {
      await loadFormTabs(this.store, association.targetObjectType.id);
    }
  }

  @action
  setSelectedRelatedUser(item?: VariantedObjectTypeAttribute) {
    this.selectedRelatedUserField = item;
  }

  isTextAttribute(attribute: VariantedObjectTypeAttribute) {
    if (!attribute.formElements[0]) return false;

    return [INPUT_TYPE.TEXTAREA, INPUT_TYPE.TEXTFIELD].includes(attribute.formElements[0].inputType) && attribute.dataType == DATA_TYPE.STRING;
  }

  @action
  setSelectedRole(role: VariantedObjectTypeRole) {
    this.selectedRole = role;
  }

  @action
  setSelectedUser(user: User) {
    this.selectedUser = user;
  }

  @action
  async setSelectedMailAddressAttribute(attribute?: VariantedObjectTypeAttribute) {
    if (attribute?.targetObjectType) {
      await Promise.all([
        loadFormTabs(this.store, attribute.targetObjectType.id),
        loadObjectTypeAttributes(this.store, attribute.targetObjectType.id)
      ]);
    }
    this.selectedMailAddressAttribute = attribute;
    this.selectedRelatedMailAddressAttribute = undefined;
  }

  @action
  setSelectedRelatedMailAddressAttribute(attribute: VariantedObjectTypeAttribute) {
    this.selectedRelatedMailAddressAttribute = attribute;
  }

  @action
  setRecipientType(value: RecipientType) {
    this.recipientType = value;
    this.selectedRelationField = undefined;
    this.selectedRelatedUserField = undefined;
    this.selectedUser = undefined;
    this.selectedRole = undefined;
    this.selectedMailAddressAttribute = undefined;
    this.selectedRelatedMailAddressAttribute = undefined;
    this.staticMailAddress = undefined;
  }

  @action
  confirmRecipient() {
    if (this.selectedRole) {
      this.mailAction.selectedRoles.push(this.selectedRole.original);
    } else if (this.selectedUser) {
      this.mailAction.selectedUsers.push(this.selectedUser);
    } else if (this.selectedMailAddressAttribute) {
      const addressAttributeOptions = {
        objectTypeAttribute: this.selectedRelatedMailAddressAttribute ? this.selectedRelatedMailAddressAttribute?.original : this.selectedMailAddressAttribute.original,
        relationshipObjectTypeAttribute: this.selectedRelatedMailAddressAttribute ? this.selectedMailAddressAttribute.original : undefined
      };

      const addressAttribute = this.store.createRecord('automation-mail-action-address-attribute', addressAttributeOptions);
      this.mailAction.automationMailActionAddressAttributes.push(addressAttribute);
    } else if (this.selectedRelationField) {
      const addressAttributeOptions: {
        columnName?: string;
        objectTypeAttribute?: ObjectTypeAttributeModel;
        relationshipObjectTypeAttribute?: ObjectTypeAttributeModel;
      } = {};

      if (isProperty(this.selectedRelationField)) {
        addressAttributeOptions.columnName = this.selectedRelationField.key;
      } else if (isProperty(this.selectedRelatedUserField)) {
        addressAttributeOptions.columnName = this.selectedRelatedUserField.key;
        addressAttributeOptions.relationshipObjectTypeAttribute = this.selectedRelationField.original;
      } else {
        addressAttributeOptions.objectTypeAttribute = this.selectedRelatedUserField?.original || this.selectedRelationField.original;
        addressAttributeOptions.relationshipObjectTypeAttribute = this.selectedRelatedUserField && this.selectedRelationField.original;
      }

      const addressAttribute = this.store.createRecord('automation-mail-action-address-attribute', addressAttributeOptions);
      this.mailAction.automationMailActionAddressAttributes.push(addressAttribute);
    } else if (this.staticMailAddress) {
      if (!this.mailAction.staticMailAddress) {
        this.mailAction.staticMailAddress = [this.staticMailAddress];
      } else {
        this.mailAction.staticMailAddress.pushObject(this.staticMailAddress);
      }
    }
    this.setRecipientType(this.selectableRecipientTypes[0].value);
    this.mailAction.automationAction.isDirty = true;
    this.closeModal();
  }

  @action
  setStaticMailAddress(event: GenericEvent<HTMLInputElement>) {
    this.staticMailAddress = event.target.value;
  }
}
