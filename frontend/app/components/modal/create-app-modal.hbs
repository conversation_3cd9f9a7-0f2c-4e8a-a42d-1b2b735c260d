<div class="modal modal-mobile-fullscreen d-block" tabindex="-1">
  <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content h-100">
      <div class="modal-header">
        <h5 class="modal-title d-flex align-items-center">
          <i class="me-2 fa-light fa-sparkles"></i>
          <button type="button" {{on "click" this.abortAppCreation}} class="btn d-lg-none"><i class="fa fa-arrow-left fa-lg"></i></button>
          {{t "app_creation.create_app"}}
        </h5>
        <button type="button" class="btn-close d-none d-lg-inline" aria-label={{t "close"}} {{on "click" this.abortAppCreation}}></button>
      </div>

      <div class="modal-body">
        {{#if this.processDescription.isRunning}}
          <h4 class="text-primary d-flex justify-content-center mt-2">
            {{t "app_creation.generating_your_app"}}
          </h4>
          <div class="d-flex justify-content-center">
            <div class="w-25">
              <Lottie
                @path="https://lottie.host/9d4aed0c-9166-4ba1-9d76-71dc57092f68/Cf4SEX4XxG.json"
                @loop={{true}}
                @autoplay={{true}}
                @speed={{1}}
              />
            </div>
          </div>
          <div class="text-primary d-flex justify-content-center mb-4">
            {{t "app_creation.parsing_mission_parameters"}}
          </div>
        {{else}}
          <div class="text-primary fw-bold mb-3">{{t "app_creation.describe_app"}}</div>
          <div class="">
            <textarea
              type="text"
              class="card card-body w-100 text-primary fw-normal input-card"
              value={{this.description}}
              rows="5"
              {{on "input" this.setDescription}}
              placeholder={{t "app_creation.description_placeholder"}}
              {{autosize}}
            />
          </div>
        {{/if}}
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary ms-auto" disabled={{this.isGenerateDisabled}} {{on "click" this.generateApp}}>
          {{t "app_creation.generate_with_ai"}}
        </button>
      </div>
    </div>
  </div>
</div>
