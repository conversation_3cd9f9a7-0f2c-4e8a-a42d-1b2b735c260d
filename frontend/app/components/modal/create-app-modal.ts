import Store from '@ember-data/store';
import { action } from '@ember/object';
import { isBlank } from '@ember/utils';
import { tracked } from '@glimmer/tracking';
import { service } from '@ember/service';
import { task } from 'ember-concurrency';
import ModalComponent from 'frontend/components/modal/modal';
import JobService from 'frontend/services/job';
import { handleError } from 'frontend/utils/handle-error';
import CreateAppModal from 'frontend/utils/modals/create-app-modal';
import EditAppModal from 'frontend/utils/modals/edit-app-modal';
import { MESSAGE_ORIGIN } from 'frontend/models/assistant-message';
import { AssistantResult } from 'frontend/utils/job/assistant-result';

interface CreateAppModalSignature {
  Args: {
    modal: CreateAppModal;
  };
}

export default class CreateAppModalComponent extends ModalComponent<CreateAppModalSignature> {
  @service
  declare store: Store;

  @service('job')
  declare jobService: JobService;

  @tracked
  declare description: string;

  @action
  abortAppCreation() {
    this.closeModal();
  }

  @action
  setDescription(event: GenericEvent<HTMLInputElement>) {
    this.description = event.target.value;
  }

  @action
  generateApp() {
    this.processDescription.perform();
  }

  processDescription = task({ drop: true }, async() => {
    const chat = await this.sendInitialMessage();
    const job = chat?.messages[0].job;
    if (!job) return;

    await new Promise<void>((resolve) => {
      this.jobService.subscribeJobActivity(job, {
        completed: async() => {
          resolve();
          const result = job.result as AssistantResult;
          const messageId = result?.data?.message_id;
          if (messageId) {
            try {
              await this.store.findRecord('assistant-message', messageId);
              const modal = new EditAppModal({ context: { assistantChat: chat } });
              this.closeModal();
              this.modalService.open((modal));
            } catch {
              handleError(this);
            }
          }
        },
        failed: () => {
          handleError(this);
          resolve();
        }
      });
    });
  });

  get isGenerateDisabled() {
    return isBlank(this.description) || this.processDescription.isRunning;
  }

  async sendInitialMessage() {
    const chat = this.store.createRecord('assistant-chat', { context: { intent: 'app_creation' } });
    this.store.createRecord('assistant-message', { origin: MESSAGE_ORIGIN.USER, content: [{ type: 'text', data: this.description }], chat: chat });
    try {
      return chat.save();
    } catch (e) {
      return handleError(this, e);
    }
  }
}
