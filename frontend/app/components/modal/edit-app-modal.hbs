<div class="modal modal-mobile-fullscreen d-block" tabindex="-1">
  <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content h-100">
      <div class="modal-header">
        <h5 class="modal-title d-flex align-items-center">
          <i class="me-2 fal fa-sparkles"></i>
          <button type="button" {{on "click" this.close}} class="btn d-lg-none"><i class="fa fa-arrow-left fa-lg"></i></button>
          {{t "app_creation.new_app"}}
        </h5>
        <button type="button" class="btn-close d-none d-lg-inline" aria-label={{t "close"}} {{on "click" this.close}}></button>
      </div>
      <div class="modal-body p-0 d-flex position-relative overflow-x-hidden">
        {{!-- Left side: Tabs content --}}
        <div class="content mini-scrollbar overflow-auto flex-grow-1">
          <div class="tabs bg-body p-3 pt-0">
            <div class="header">
              <ul class="nav nav-tabs-underlined d-flex align-items-center" role="tablist">
                <li class="nav-item active" data-bs-toggle="tab" id="edit-app-general-tab" data-bs-target="#edit-app-general" type="button" role="tab">
                  {{t "general"}}
                </li>
                <li class="nav-item" data-bs-toggle="tab" id="edit-app-objects-tab" data-bs-target="#edit-app-objects" type="button" role="tab">
                  {{t "navigation.objects"}}
                </li>
                <li class="nav-item" data-bs-toggle="tab" id="edit-app-kpis-tab" data-bs-target="#edit-app-kpis" type="button" role="tab">
                  {{t "app_creation.kpis"}}
                </li>
              </ul>
            </div>
            <div class="tab-content">
              <div class="tab-pane fade show active" id="edit-app-general" role="tabpanel">
                <AppCreation::GeneralTab @assistantChatSession={{this.assistantChatSession}}/>
              </div>
              <div class="tab-pane fade" id="edit-app-objects" role="tabpanel">
                <AppCreation::ObjectTypesData @assistantChatSession={{this.assistantChatSession}}/>
              </div>
              <div class="tab-pane fade" id="edit-app-kpis" role="tabpanel">
                {{t "app_creation.kpis"}}
              </div>
            </div>
          </div>
        </div>
        {{!-- Right side: Preview --}}
        <EditApp::RightPanel @assistantChatSession={{this.assistantChatSession}}/>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" {{on "click" this.close}} type="button">
          {{t "app_creation.create_app"}}
        </button>
      </div>
    </div>
  </div>
</div>
