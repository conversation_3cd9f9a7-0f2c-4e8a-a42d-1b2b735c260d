import { action } from '@ember/object';
import ModalComponent from 'frontend/components/modal/modal';
import EditAppModal from 'frontend/utils/modals/edit-app-modal';
import { AssistantChatSession } from 'frontend/utils/assistant-chat-session';
import Owner from '@ember/owner';

interface EditAppModalSignature {
  Args: {
    modal: EditAppModal;
  };
}

export default class EditAppModalComponent extends ModalComponent<EditAppModalSignature> {
  declare assistantChatSession: AssistantChatSession;

  constructor(owner: Owner, args: EditAppModalSignature['Args']) {
    super(owner, args);

    this.assistantChatSession = new AssistantChatSession(this.modalContext.assistantChat);
  }

  @action
  close() {
    this.closeModal();
  }
}
