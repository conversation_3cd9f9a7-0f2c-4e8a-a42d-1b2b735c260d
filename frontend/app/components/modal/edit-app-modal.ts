import { action } from '@ember/object';
import { cached } from '@glimmer/tracking';
import ModalComponent from 'frontend/components/modal/modal';
import EditAppModal from 'frontend/utils/modals/edit-app-modal';
import { AssistantChatSession } from 'frontend/utils/assistant-chat-session';

interface EditAppModalSignature {
  Args: {
    modal: EditAppModal;
  };
}

export default class EditAppModalComponent extends ModalComponent<EditAppModalSignature> {
  @action
  close() {
    this.closeModal();
  }

  @cached
  get assistantChatSession() {
    return new AssistantChatSession(this.modalContext.assistantChat);
  }
}
