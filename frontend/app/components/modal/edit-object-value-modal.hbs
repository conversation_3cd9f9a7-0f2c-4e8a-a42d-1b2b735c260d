<div class="modal d-block edit-object-value-modal transparent-background" {{on "click" this.handleOutsideClick}} tabindex="-1">
  <div class="modal-dialog modal-dialog-centered justify-content-center">
    <div class="modal-content">
      <div class="modal-body p-1">
        <div class="p-1" role="button" {{on "click" (stop-propagation)}} {{this.focusInput}}>
          <div class="d-flex">
            <label class="py-1 text-wrap inline-form-element-label" >
              {{this.modal.context.formElement.objectTypeAttribute.name}}{{#if this.isValueRequired}}*{{/if}}
            </label>
            <div role="button" class="ps-1 ms-auto text-secondary" {{on "click" this.cancel}}>
              <i class="fal fa-xmark"></i>
            </div>
          </div>
          <FormElement
            @formElement={{this.modal.context.formElement}}
            @object={{this.modal.context.object}}
            @openEditObjectModal={{this.modal.context.openEditObjectModal}}
            @openAttachments={{this.modal.context.openAttachments}}
            @disabled={{this.disabledInput}}
            @hideLabel={{true}}
          />
        </div>
        <div class="d-flex justify-content-end m-1">
          <button class="btn btn-sm py-0 btn-primary ms-1 position-relative" type="button" {{on "click" (perform this.saveObject)}} disabled={{this.saveButtonDisabled}}>
            {{#if this.saveObject.isRunning}}
              <div class="position-absolute top-50 start-50 translate-middle">
                <i class="fa fa-spin fa-spinner" role="status" aria-hidden="true"/>
                <span class="visually-hidden">{{t "saving"}}</span>
              </div>
            {{/if}}
            <span class="text {{if this.saveObject.isRunning "opacity-0"}}">
              {{t "save"}}
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
