import Store from '@ember-data/store';
import { action } from '@ember/object';
import { service } from '@ember/service';
import { tracked } from '@glimmer/tracking';
import IntlService from 'ember-intl/services/intl';
import ModalComponent from 'frontend/components/modal/modal';
import Team from 'frontend/models/team';
import ToastService from 'frontend/services/toast';
import { sortByKey } from 'frontend/utils/array';
import { handleError } from 'frontend/utils/handle-error';
import ShareObjectTypeModal from 'frontend/utils/modals/share-object-type-modal';
import { all } from 'rsvp';

interface ShareObjectTypeModalSignature {
  Args: {
    modal: ShareObjectTypeModal;
  };
}

export default class ShareObjectTypeModalComponent extends ModalComponent<ShareObjectTypeModalSignature> {
  @service
  declare store: Store;

  @service('toast')
  declare toastService: ToastService;

  @service
  declare intl: IntlService;

  declare message?: string;

  @tracked
  notifyAdmins = false;

  @tracked
  selectedTeams: Array<Team> = [];

  @action
  setTeams(teams: Array<Team>) {
    this.selectedTeams = teams;
  }

  @action
  async shareObjectType() {
    const options: { data: { attributes: { notify_admins: boolean, team_id?: string, message?: string } } } = { data: { attributes: { notify_admins: this.notifyAdmins } } };
    if (this.notifyAdmins) {
      options.data.attributes.message = this.message;
    }

    try {
      await all(this.selectedTeams.map((team) => {
        options.data.attributes.team_id = team.id;
        return this.store.adapterFor('object-type').share(this.modal.context.objectType.id, options);
      }));
      await this.store.query('object-type-variant', { url: { objectTypeId: this.modal.context.objectType.id } });
      this.modal.context.objectType.teams.push(...this.selectedTeams);
      this.modal.trigger('onShareObjectType');
      this.closeModal();
    } catch (e) {
      if (e.errors?.length && e.errors[0].code == "ObjectTypeSharedViolation") {
        handleError(this, e.errors[0], { text: this.intl.t('object_type_share_violation') });
        await this.store.query('object-type-variant', { url: { objectTypeId: this.modal.context.objectType.id } });
        this.modal.context.objectType.teams.push(...this.selectedTeams);
        this.modal.trigger('onShareObjectType');
        this.closeModal();
      } else {
        handleError(this, e);
      }
    }
  }

  @action
  setMessage(event: GenericEvent<HTMLInputElement>) {
    this.message = event.target.value;
  }

  @action
  toggleNotifyAdmin() {
    this.notifyAdmins = !this.notifyAdmins;
  }

  @action
  async loadTeams(query: string) {
    const filter = { breadcrumb: query, team_type: 'explicit', not_related_to_object_type_id: this.modal.context.objectType.id };
    try {
      return sortByKey((await this.store.query('team', { filter: filter })).slice(), "breadcrumb");
    } catch {
      // Since we're just loading data in a select, no need to show an error message
      return [];
    }
  }
}
