import Store from '@ember-data/store';
import { service } from '@ember/service';
import ModalComponent from 'frontend/components/modal/modal';
import Owner from '@ember/owner';
import TranslationModal from 'frontend/utils/modals/translation-modal';
import CurrentUserService from 'frontend/services/current-user';
import { action } from '@ember/object';
import { tracked } from '@glimmer/tracking';
import { IntlService } from 'ember-intl';
import { dogmaticSet } from 'frontend/utils/store/dogmatic-set';
import DialogService from 'frontend/services/dialog';
import Transition from '@ember/routing/transition';
import { abortTransition, hasDirtyChecksEnabled, ignoreDirtyChecks } from 'frontend/utils/transition';
import RouterService from '@ember/routing/router-service';
import { isBlank } from '@ember/utils';
import { handleError } from 'frontend/utils/handle-error';
import { MAX_NAME_LENGTH } from 'frontend/models/object-type-attribute';
import { TRANSLATABLE_OBJECT } from '../translatable-icon';
import Model from '@ember-data/model';
import AutomationMailActionModel from 'frontend/models/automation-mail-action';

interface TranslationModalSignature<T extends TRANSLATABLE_OBJECT> {
  Args: {
    modal: TranslationModal<T>;
  };
}

export enum AVAILABLE_LOCALES {
  EN = "en",
  FR = "fr",
  ES = "es",
  NL = "nl"
}

export type TRANSLATIONS_OBJECT = {
  [key in AVAILABLE_LOCALES]?: string;
};

export default class TranslationModalComponent<T extends TRANSLATABLE_OBJECT> extends ModalComponent<TranslationModalSignature<T>> {
  @service
  declare store: Store;

  @service
  declare currentUser: CurrentUserService;

  @service
  declare intl: IntlService;

  @service('dialog')
  declare dialog: DialogService;

  @service
  declare router: RouterService;

  @tracked
  declare translations: TRANSLATIONS_OBJECT;

  @tracked
  declare targetLocale?: AVAILABLE_LOCALES;

  @tracked
  declare sourceTranslation: string;

  @tracked
  declare targetTranslation?: string;

  @tracked
  declare rerenderTargetTextEdit?: boolean;

  declare _warnBeforeUnload: () => void;

  declare _onrouteChange: (transition: Transition) => void;

  get translatableObject() {
    return this.modal.context.translatableObject;
  }

  get key() {
    return this.modal.context.key;
  }

  get isRichText() {
    return this.modal.context.isRichText;
  }

  get customId() {
    return `${this.translatableObject.id}-${this.key.toString()}`;
  }

  get sourceLocale() {
    return this.currentUser.user.language;
  }

  get sourceLocales() {
    return [{ value: this.sourceLocale, label: this.intl.t(`languages.${this.sourceLocale}`) }];
  }

  get targetLocales() {
    return Object.values(AVAILABLE_LOCALES).reduce((targetLocales: Array<{ value: AVAILABLE_LOCALES, label: string }>, locale) => {
      if (locale != this.sourceLocale) {
        targetLocales.push({ value: locale, label: this.intl.t(`languages.${locale}`) });
      }
      return targetLocales;
    }, []);
  }

  get selectedTargetLocale() {
    return this.targetLocales.find((target) => target.value == this.targetLocale);
  }

  get disabled() {
    if (this.modal.context.allowEmptyTranslations) return false;

    // disable save button when source translation is empty or when the fallback translation is empty (which is used as target)
    return isBlank(this.sourceTranslation) || isBlank(this.targetTranslation) && this.targetLocale == AVAILABLE_LOCALES.EN
      || this.sourceTranslation.length > MAX_NAME_LENGTH || (this.targetTranslation && this.targetTranslation.length > MAX_NAME_LENGTH);
  }

  get dirtyTargetTranslation() {
    return this.targetLocale && this.targetTranslation != this.translations[this.targetLocale];
  }

  get hasDirtyTranslations() {
    return this.dirtyTargetTranslation || (this.sourceLocale && this.sourceTranslation != this.translations[this.sourceLocale]);
  }

  get isAutomationSubjectOrBody() {
    return this.translatableObject instanceof AutomationMailActionModel && ['subject', 'body'].includes(this.key as string);
  }

  constructor(owner: Owner, args: TranslationModalSignature<T>['Args']) {
    super(owner, args);
    this.loadTranslations();

    this._warnBeforeUnload = this.warnBeforeUnload.bind(this);
    window.addEventListener('beforeunload', this._warnBeforeUnload);

    this._onrouteChange = this.onrouteChange.bind(this);
    this.router.on('routeWillChange', this._onrouteChange);
  }

  willDestroy() {
    window.removeEventListener('beforeunload', this._warnBeforeUnload);
    this.router.off('routeWillChange', this._onrouteChange);
    super.willDestroy();
  }

  async loadTranslations() {
    try {
      const response = await this.store.adapterFor('translations').fetchTranslations(this.translatableObject, this.customId);
      this.translations = response.attributes;
    } catch (error) {
      handleError(this, error);
      super.closeModal();
    }

    if (this.sourceLocale) {
      this.translations[this.sourceLocale] = (this.translatableObject[this.key] as string);
      this.sourceTranslation = (this.translatableObject[this.key] as string);
    }
    this.initializeTargetLocale();
    if (this.targetLocale) this.targetTranslation = this.translations[this.targetLocale];
  }

  initializeTargetLocale() {
    this.targetLocale = this.currentUser.user.localSettings?.targetLocale;
    if (!this.targetLocale || this.targetLocale == this.sourceLocale) {
      this.targetLocale = Object.values(AVAILABLE_LOCALES).find((locale) => locale != this.sourceLocale);
    }
  }

  @action
  setSourceTranslation(event: GenericEvent<HTMLInputElement>) {
    this.sourceTranslation = event.target.value;
  }

  @action
  setTargetTranslation(event: GenericEvent<HTMLInputElement>) {
    this.targetTranslation = event.target.value;
  }

  @action
  setSourceText(value: string) {
    this.sourceTranslation = this.parseTextValue(value);
  }

  @action
  setTargetText(value: string) {
    this.targetTranslation = this.parseTextValue(value);
  }

  parseTextValue(value: string) {
    // Check textContent of the html to see if we have an empty text
    const parser = new DOMParser();
    const textContent = parser.parseFromString(value, 'text/html').body.textContent;
    return isBlank(textContent) ? "" : value;
  }

  @action
  setTranslation(type: "target" | "source", value: string) {
    if (type == "target") {
      this.targetTranslation = value;
    } else {
      this.sourceTranslation = value;
    }
  }

  @action
  async saveTranslations() {
    const updatedTranslations: TRANSLATIONS_OBJECT = {};

    if (this.sourceLocale) updatedTranslations[this.sourceLocale] = this.sourceTranslation;
    if (this.targetLocale) updatedTranslations[this.targetLocale] = this.targetTranslation;

    await this.store.adapterFor('translations').updateTranslations(this.translatableObject, this.customId, updatedTranslations);
    if (this.translatableObject instanceof Model) dogmaticSet(this.translatableObject, ({ [this.key]: this.sourceTranslation } as Partial<T>));

    this.modal.trigger('onTranslationSave', this.sourceTranslation);
    super.closeModal();
  }

  @action
  async setTargetLocale(locale: AVAILABLE_LOCALES) {
    if (this.dirtyTargetTranslation) {
      // reset the tomSelect by resetting the targetLocale to the old value
      this.targetLocale = this.selectedTargetLocale?.value;
      if (!await this.openDirtyTranslationDialog(this.intl.t("dialog.confirm_unsaved_translations.target_text"))) return;
    }

    this.targetLocale = locale;
    this.targetTranslation = this.translations[locale];
    this.currentUser.user.localSettings.targetLocale = locale;

    if (this.isAutomationSubjectOrBody) {
      this.rerenderTargetTextEdit = !this.rerenderTargetTextEdit;
    }
  }

  @action
  async closeModal() {
    if (this.hasDirtyTranslations) {
      if (!await this.openDirtyTranslationDialog()) return;
    }

    super.closeModal();
  }

  async openDirtyTranslationDialog(text?: string) {
    const dialogText = text || this.intl.t("dialog.confirm_unsaved_translations.text");
    return await this.dialog.confirm(dialogText);
  }

  warnBeforeUnload(event: BeforeUnloadEvent) {
    if (!this.hasDirtyTranslations) return;
    event.preventDefault();
    // setting returnValue to a value other than null or undefined will prompt the dialog
    // In older browsers, the returnValue of the event is displayed in this dialog.
    return event.returnValue = this.intl.t("dialog.confirm_unsaved_translations.text");
  }

  async onrouteChange(transition: Transition) {
    if (transition.isAborted) return;

    if (hasDirtyChecksEnabled(transition) && this.hasDirtyTranslations) {
      abortTransition(transition, this.router);

      const result = await this.openDirtyTranslationDialog();

      if (result) {
        ignoreDirtyChecks(transition);
        transition.retry();
      }
    }
  }
}
