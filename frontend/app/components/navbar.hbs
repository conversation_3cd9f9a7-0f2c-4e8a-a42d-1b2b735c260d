<nav class="navbar p-0 border-bottom h-100 overflow-none">
  {{!-- desktop --}}
  <div class="d-none p-2 d-lg-flex align-items-center w-100">
    {{!-- burger --}}
    {{#unless this.sidebar.showSidebarDesktop}}
      <button
        type="button"
        class="btn px-1 ms-1"
        {{on "click" this.sidebar.toggleSidebarDesktop}}
      >
        <i class="fa fa-fw fa-bars"/>
      </button>
    {{/unless}}
    {{!-- viewpoint --}}
    <div class="btn-group">
      <ViewpointSelector class="dropdown-menu dropdown-menu-start" data-bs-offset="0,0" />
    </div>
    {{!-- help --}}
    <div class="dropdown px-4 ms-auto">
      <a type="button" data-bs-toggle="dropdown" class="text-decoration-none">
        {{t "help"}} <i class="fa fa-caret-down fa-sm"></i>
      </a>
      <div class="dropdown-menu dropdown-menu-end">
        <a class="dropdown-item" href={{this.userGuideUrl}}>
          <i class="fal fa-book fa-fw" />
          {{t "user_guide"}}
        </a>
        <a class="dropdown-item" href="mailto:<EMAIL>">
          <i class="fal fa-life-ring fa-fw" />
          {{t "contact_support"}}
        </a>
      </div>
    </div>
    {{!-- avatar --}}
    <span class="dropdown">
      <a role="button" id="dropdown-menu-avatar" data-bs-toggle="dropdown" class="text-decoration-none" aria-expanded="false">
        <UserAvatar @user={{this.currentUser.user}} @instance={{this.currentInstance}} class="d-none d-lg-block"/>
      </a>
      <div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdown-menu-avatar">
        <LinkTo @route="user" class="dropdown-item">
          <i class="fal fa-gear fa-fw" /> {{t "settings"}}
        </LinkTo>
        <button type="button" class="dropdown-item" {{on "click" this.signOut}}><i class="fal fa-power-off fa-fw"/> {{t "sign_out"}}</button>
      </div>
    </span>

  </div>

  {{!-- mobile --}}
  <div class="d-flex px-2 pt-1 pb-2 fs-120 align-items-center d-lg-none w-100">
    {{!-- burger --}}
    <button
      type="button"
      class="btn burger-button ms-2 {{if this.sidebar.showSidebarMobile "active"}}"
      {{on "click" this.sidebar.toggleSidebarMobile}}
    >
      {{!-- placeholder for sizing --}}
      <i class="fa fa-fw fa-square invisible"/>
      <i class="fa fa-fw fa-bars text-white"/>
      <i class="fa fa-fw fa-xmark text-white"/>
    </button>
    {{!-- viewpoint --}}
    <div class="btn-group dropup me-auto position-static">
      <ViewpointSelector class="ms-3 dropdown-menu dropdown-menu-end"/>
    </div>
    {{!-- more --}}
    <div class="dropup px-2 position-static">
      <a role="button" class="btn" id="dropdown-menu-avatar-mobile" data-bs-toggle="dropdown" aria-expanded="false" data-bs-offset="0,0">
        <i class="fa fa-ellipsis-vertical text-white"/>
      </a>
      <div class="dropdown-menu vw-100">
        <a class="dropdown-item p-2" href={{this.userGuideUrl}}>
          <i class="fal fa-book fa-fw" /> {{t "user_guide"}}
        </a>
        <a class="dropdown-item p-2" href="mailto:<EMAIL>">
          <i class="fal fa-life-ring fa-fw" /> {{t "contact_support"}}
        </a>
        <div class="dropdown-divider"></div>
        <LinkTo @route="user" class="dropdown-item p-2">
          <i class="fal fa-gear fa-fw" /> {{t "settings"}}
        </LinkTo>
        <button type="button" class="dropdown-item p-2" {{on "click" this.signOut}}><i class="fal fa-power-off fa-fw"/> {{t "sign_out"}}</button>
      </div>
    </div>
  </div>
</nav>
