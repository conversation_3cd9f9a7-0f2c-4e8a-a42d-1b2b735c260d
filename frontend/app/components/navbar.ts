import Component from '@glimmer/component';
import { service } from '@ember/service';
import { action } from '@ember/object';
import { userGuideUrl } from 'frontend/utils/user-guide-url';
import CurrentInstanceService from 'frontend/services/current-instance';
import CurrentUserService from 'frontend/services/current-user';
import SidebarService from 'frontend/services/sidebar';

export default class NavbarComponent extends Component {
  @service
  declare currentInstance: CurrentInstanceService;

  @service
  declare currentUser: CurrentUserService;

  @service
  declare sidebar: SidebarService;

  get userGuideUrl() {
    return userGuideUrl(this.currentUser.user.language);
  }

  @action
  signOut() {
    this.currentUser.signOut();
  }
}
