import Component from '@glimmer/component';
import ObjectModel from 'frontend/models/object';
import ObjectAttachmentModel from 'frontend/models/object-attachment';
import ObjectTypeAttributeModel, { DATA_TYPE } from 'frontend/models/object-type-attribute';
import ThumbnailEditorModal from 'frontend/utils/modals/thumbnail-editor-modal';
import { service } from '@ember/service';
import ModalService from 'frontend/services/modal';
import Store from '@ember-data/store';
import { loadObjectTypeAttributes } from 'frontend/utils/object-type-attributes-loader';
import { all } from 'rsvp';
import { IntlService } from 'ember-intl';
import ThumbnailModel from 'frontend/models/thumbnail';
import { DefaultThumbnail, THUMBNAIL_TYPE, cloneFromDefaultThumbnail, getDefaultHorizontalThumbnail } from 'frontend/utils/thumbnail';
import { findObjectTypeVariantFor } from 'frontend/utils/variants';
import { Column } from 'frontend/utils/visualisation';
import ObjectTypeField from 'frontend/types/ObjectTypeField';
import Viewpoint from 'frontend/services/viewpoint';
import { task } from 'ember-concurrency';
import { sortByKey } from 'frontend/utils/array';
import { action } from '@ember/object';
import { fieldIsFilterable, fieldIsSortable } from 'frontend/utils/object-type-field';
import { addFilterRule } from 'frontend/utils/object-query-rules';
import ObjectTypeProperty, { PROPERTIES } from 'frontend/utils/object-type-property';
import VisualisationModel from 'frontend/models/visualisation';
import ObjectTypeModel from 'frontend/models/object-type';
import ObjectQueryViewModel from 'frontend/models/object-query-view';
import { loadFormTabs } from 'frontend/utils/form-tab-loader';
import FormTabModel from 'frontend/models/form-tab';

interface ObjectTableSignature {
  Args: {
    objectType: ObjectTypeModel;
    visualisation?: VisualisationModel;
    objectQueryView?: ObjectQueryViewModel;
    objects: Array<ObjectModel>;
    checkedObjects?: Array<ObjectModel>;
    columns: Array<Column>;
    allowFilter: boolean;
    disableRowSelection: boolean;
    queryContainerRef?: string;
    checkObject?: (object?: ObjectModel) => void;
    uncheckObject?: (object?: ObjectModel) => void;
    selectObject: (object: ObjectModel, event: GenericEvent<HTMLElement>) => void;
    openAttachments: (object: ObjectModel, selectedAttachment: ObjectAttachmentModel, event: Event) => void;
    onRuleChange?: () => void;
    disableSorting?: boolean;
    disallowThumbnailSave?: boolean;
    allowInlineEditing?: boolean;
  };
  Element: HTMLDivElement;
}

export default class ObjectTableComponent extends Component<ObjectTableSignature> {
  @service('modal')
  declare modalService: ModalService;

  @service
  declare store: Store;

  @service
  declare intl: IntlService;

  @service
  declare viewpoint: Viewpoint;

  get visibleColumns() {
    return this.args.columns.reduce((result: Array<{ field: ObjectTypeField, thumbnail?: ThumbnailModel | DefaultThumbnail, breadcrumb: string }>, column) => {
      const attribute = this.args.objectType?.fields.find((field) => field.id === column.id);
      if (!attribute) return result;
      let thumbnail;
      if (attribute?.dataType === DATA_TYPE.RELATIONSHIP) {
        thumbnail = this.args.visualisation?.thumbnails.find((thu) => thu.visualisationAttribute == attribute);

        if (!thumbnail && attribute instanceof ObjectTypeAttributeModel && attribute.targetObjectType) thumbnail = getDefaultHorizontalThumbnail(attribute.targetObjectType);
      }
      result.push({ field: attribute, thumbnail: thumbnail, breadcrumb: this.breadcrumbForColumn(attribute) });
      return result;
    }, []);
  }

  columnIsSortable = (column: typeof this.visibleColumns[number]) => {
    const field = column.field;
    return !this.args.disableSorting && fieldIsSortable(field);
  };

  columnIsFilterable = (column: typeof this.visibleColumns[number]) => {
    const field = column.field;

    // We filter on the team.id property, but this component uses the team.name property, so a conversion is needed.
    return (fieldIsFilterable(field) || field.id === "team.name") && this.args.allowFilter;
  };

  showColumnDropdown = (column: typeof this.visibleColumns[number]) => {
    const isObjectRelationship = column.field instanceof ObjectTypeAttributeModel && column.field.targetClass == "Object";
    return this.columnIsSortable(column) || this.columnIsFilterable(column) || isObjectRelationship;
  };

  @action
  sortOnColumn(column: typeof this.visibleColumns[number], order: 'asc' | 'desc') {
    if (!this.args.objectQueryView) return;
    const field = column.field;

    this.args.objectQueryView.objectQueryRules.slice().forEach((rule) => {
      if (rule.ruleType === 'sort' && (rule.objectTypeAttributeId == field.id || rule.columnName == field.id)) rule.deleteRecord();
    });

    sortByKey(this.args.objectQueryView.objectQueryRules.filter((rule) => rule.ruleType === 'sort' && !rule.isDeleted), 'position')
      .forEach((rule, index) => rule.position = index + 1);

    const options: Dict = {
      ruleType: 'sort',
      sortOrder: order,
      objectQueryView: this.args.objectQueryView,
      position: 0
    };

    const rule = this.store.createRecord('object-query-rule', options);
    rule.updateObjectTypeField(field.id);
    this.args.onRuleChange?.();
  }

  @action
  async filterColumn(column: typeof this.visibleColumns[number], event: GenericEvent<HTMLElement>) {
    if (!this.args.objectQueryView) return;

    this.closeColumnDropdown(event.currentTarget); // Column dropdown needs to be closed manually because we prevent it with stopPropagation, which is needed for the filter-window.
    this.openFilterDropdown();
    let field = column.field;

    // We filter on the team.id property, but this component uses the team.name property, so a conversion is needed.
    if (field.id === 'team.name') {
      field = PROPERTIES.find((prop) => prop.id == 'team_id') as ObjectTypeProperty;
    }

    const rule = await addFilterRule(field, this.args.objectQueryView);

    if (rule.valid) this.args.onRuleChange?.();
  }

  @action
  openFilterDropdown() {
    const filterWindowButton = document.querySelector(`${this.args.queryContainerRef} .filter-window-button`);
    if (!filterWindowButton) return;
    // This does not make a new dropdown but references an existing one. https://getbootstrap.com/docs/5.0/components/dropdowns/#via-javascript
    const filterDropdown = new window.bootstrap.Dropdown(filterWindowButton);
    filterDropdown.show();
  }

  @action
  closeColumnDropdown(element: HTMLElement) {
    const columnDropdown = element.closest('.column-dropdown');
    if (!columnDropdown) return;
    // This does not make a new dropdown but references an existing one. https://getbootstrap.com/docs/5.0/components/dropdowns/#via-javascript
    const dropdown = new window.bootstrap.Dropdown(columnDropdown);
    dropdown.hide();
  }

  get objectTypeVariant() {
    if (!this.args.objectType) return;
    return findObjectTypeVariantFor(this.viewpoint.selectedTeam, this.args.objectType);
  }

  breadcrumbForColumn(field: ObjectTypeField) {
    if (field instanceof ObjectTypeAttributeModel) {
      if (!field.formElements.length) {
        return this.intl.t('unassigned');
      }

      let formTab: FormTabModel | undefined = field.formElements[0]?.formTab;
      const formTabNames = [];
      while (formTab) {
        formTabNames.unshift(formTab.name);
        formTab = formTab.parent;
      }
      return formTabNames.join(' > ');
    }
    return this.intl.t('system');
  }

  customizeThumbnail = task({ drop: true }, async(column: { field: ObjectTypeField, thumbnail?: ThumbnailModel | DefaultThumbnail }) => {
    let thumbnail;
    if (column.thumbnail instanceof DefaultThumbnail || column.thumbnail?.default) {
      if (!(column.field instanceof ObjectTypeAttributeModel) || !column.field.targetObjectType) return;

      const team = this.viewpoint.selectedTeam;
      const objectTypeVariant = findObjectTypeVariantFor(team, column.field.targetObjectType);
      thumbnail = cloneFromDefaultThumbnail(column.thumbnail, this.store, objectTypeVariant);
      thumbnail.visualisationAttribute = column.field;
      thumbnail.visualisation = this.args.visualisation;
    } else if (column.thumbnail instanceof ThumbnailModel) {
      thumbnail = column.thumbnail;
    }

    if (!thumbnail) return;

    await all([
      loadFormTabs(this.store, thumbnail.objectType.id),
      loadObjectTypeAttributes(this.store, thumbnail.objectType.id)
    ]);

    const disallowThumbnailSave = this.args.disallowThumbnailSave || !this.args.visualisation?.view || this.args.visualisation.view.ephemeral || this.objectTypeVariant?.id != this.args.visualisation.view.objectTypeVariantId;

    const modal = new ThumbnailEditorModal({
      context: {
        thumbnail: thumbnail,
        allowedThumbnailTypes: [THUMBNAIL_TYPE.HORIZONTAL],
        disallowThumbnailSave: disallowThumbnailSave
      },
      callbacks: {}
    });

    this.modalService.open(modal);
  });
}
