{{#if this.showDropdownButton}}
  <button type="button" disabled={{this.disabled}} class="p-1 btn btn-sm p-0 edit-icon" data-bs-toggle="dropdown" data-bs-auto-close="outside" data-bs-offset="0,0"
    {{did-insert this.referenceDropdown}}
    {{on "hide.bs.dropdown" this.onDropdownHide}}
    {{on "show.bs.dropdown" this.onDropdownShow}}
  >
    <i class="fa fa-pencil"></i>
  </button>
  <DropMenu class="query-pane dropdown-menu dropdown-menu-end p-1 inline-edit-dropdown" {{on "click" (stop-propagation)}}>
    <:body>
      {{#if this.isOpen}}
        <div class="p-1 overflow-x-hidden" {{this.focusInput}}>
          <div class="d-flex">
            <label class="text-wrap py-1 inline-form-element-label" >
              {{this.objectTypeAttribute.name}}{{#if this.isValueRequired}}*{{/if}}
            </label>
            <div role="button" class="ps-1 ms-auto text-secondary" {{on "click" this.cancel}}>
              <i class="fal fa-xmark"></i>
            </div>
          </div>
          <FormElement
            @formElement={{this.variantedFormElement}}
            @object={{@object}}
            @openEditObjectModal={{@selectObject}}
            @openAttachments={{@openAttachments}}
            @disabled={{this.disabledInput}}
            @hideLabel={{true}}
          />
        </div>
        <div class="d-flex justify-content-end m-1">
          <button class="btn btn-sm py-0 btn-primary ms-1 position-relative" type="button" {{on "click" (perform this.saveObject)}} disabled={{this.saveButtonDisabled}}>
            {{#if this.saveObject.isRunning}}
              <div class="position-absolute top-50 start-50 translate-middle">
                <i class="fa fa-spin fa-spinner" role="status" aria-hidden="true"/>
                <span class="visually-hidden">{{t "saving"}}</span>
              </div>
            {{/if}}
            <span class="text {{if this.saveObject.isRunning "opacity-0"}}">
              {{t "save"}}
            </span>
          </button>
        </div>
      {{/if}}
    </:body>
  </DropMenu>
{{else}}
  <button type="button" disabled={{this.disabled}} class="p-1 btn btn-sm p-0 edit-icon" {{on "click" this.openEditObjectValueModal}}>
    <i class="fa fa-pencil"></i>
  </button>
{{/if}}
