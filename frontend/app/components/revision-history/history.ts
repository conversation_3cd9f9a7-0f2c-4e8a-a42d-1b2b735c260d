import { action } from '@ember/object';
import { service } from '@ember/service';
import { task } from 'ember-concurrency';
import { tracked } from '@glimmer/tracking';
import Component from '@glimmer/component';
import ObjectModel from 'frontend/models/object';
import Store from '@ember-data/store';

interface RevisionHistoryHistoryComponentSignature {
  Args: {
    object: ObjectModel;
  };
}

export const REVISION_HISTORY_PAGE_SIZE = 25;

export default class RevisionHistoryHistoryComponent extends Component<RevisionHistoryHistoryComponentSignature> {
  @service
  declare store: Store;

  @tracked totalCount: number = 0;

  constructor(owner: unknown, args: RevisionHistoryHistoryComponentSignature['Args']) {
    super(owner, args);

    this.historyFetcherTask.perform();
  }

  get snapshots() {
    return this.args.object.snapshots;
  }

  historyFetcherTask = task({ drop: true }, async() => {
    const currentPage = Math.floor((this.args.object.snapshots.length as number) / REVISION_HISTORY_PAGE_SIZE);

    const snapshots = await this.store.query('snapshot', {
      filters: { object: this.args.object },
      page: { number: currentPage + 1, size: REVISION_HISTORY_PAGE_SIZE }
    });

    this.totalCount = (snapshots as any).meta.total_count;
  });

  @action
  onScroll(e: Event) {
    const revisionHistoryPanel = e.currentTarget as HTMLDivElement;

    if ((this.snapshots.length as number) >= this.totalCount) return;
    if (this.historyFetcherTask.isRunning) return;
    if (revisionHistoryPanel.scrollHeight + revisionHistoryPanel.scrollTop - 10 > revisionHistoryPanel.clientHeight) {
      return;
    }

    this.historyFetcherTask.perform();
  }
}
