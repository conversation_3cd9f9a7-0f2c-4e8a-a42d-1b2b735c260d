{{#if @view.template}}
  {{#if this.options}}
    {{#if (eq this.visualisation.viewType "table")}}
      <Table @columns={{this.options.columns}} @rows={{this.options.rows}}/>
    {{else}}
      {{#if (eq this.visualisation.viewType "number")}}
        <NumberDisplay
          @value={{this.options.value}}
          @unit={{this.options.unit}}
          @color={{this.numberColor}}
          @description={{this.options.targetText}}/>
      {{else}}
        <Chart @options={{this.options}} @onClick={{if this.supportsClick this.onClick}}/>
      {{/if}}
    {{/if}}
  {{/if}}
{{else if (and this.loadedObjectTypeAttributes this.loadDependencies.isIdle)}}
  <View::ObjectView
    @view={{@view}}
    @searchString={{@searchString}}
    @setSearchString={{@setSearchString}}
    @showQueryContainer={{not (eq @view.visualisation.viewType "multiObjectList")}}
    @compactQueryContainer={{true}}
    @objectType={{@view.objectType}}
    @hideInclusionFilters={{true}}
    @columns={{this.columns}}
    @refreshInterfaceElements={{@refreshInterfaceElements}}
    @interface={{@view.interfaceElement.section.interface}}
  />
{{/if}}
