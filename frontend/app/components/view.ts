import Component from '@glimmer/component';
import { service } from '@ember/service';
import { cached, tracked } from '@glimmer/tracking';
import { action } from '@ember/object';
import Store from '@ember-data/store';
import RouterService from '@ember/routing/router-service';
import Owner from '@ember/owner';
import ViewModel from 'frontend/models/view';
import QueryResult from 'frontend/utils/query-result';
import { removeInvalidColumns, addAllColumns, filterHiddenColumns, LineChartOptions, RadarChartOptions, PieChartOptions, PyramidChartOptions, TableOptions, NumberOptions, GageChartOptions } from 'frontend/utils/visualisation';
import IntlService from 'ember-intl/services/intl';
import { loadSelfAndRelatedObjectTypeAttributes } from 'frontend/utils/object-type-attributes-loader';
import { VIEW_TYPE } from 'frontend/models/visualisation';
import Viewpoint from 'frontend/services/viewpoint';
import { variantFor, findObjectTypeVariantFor } from 'frontend/utils/variants';
import loadViewDependencies from 'frontend/utils/view/load-view-dependencies';
import Template from 'frontend/utils/template-definition';
import { loadFormTabs } from 'frontend/utils/form-tab-loader';
import { task } from 'ember-concurrency';
import ModalService from 'frontend/services/modal';
import ObjectViewModal from 'frontend/utils/modals/object-view-modal';
import { findAllViewWithFallback, findDefaultViewWithFallback } from 'frontend/utils/view/find-view';
import { all } from 'rsvp';
import { loadSelfAndRelatedDefaultThumbnails } from 'frontend/utils/default-thumbnails-loader';
import { RadarIndicator } from 'frontend/utils/visualisation/options/radar-chart-options';
import ObjectTypeModel from 'frontend/models/object-type';
import { markRuleAsDeleted } from 'frontend/utils/object-query-rules';
import { CalendarChartOptions } from 'frontend/utils/visualisation/options/calendar-chart-options';
import { colorForValue } from 'frontend/utils/visualisation/color-interval';

interface ViewSignature {
  Args: {
    view: ViewModel;
    searchString?: string;
    setSearchString?: (value: string) => void;
    refreshInterfaceElements?: () => void;
  };
}

export default class ViewComponent extends Component<ViewSignature> {
  @service declare store: Store;
  @service declare router: RouterService;
  @service declare viewpoint: Viewpoint;
  @service declare intl: IntlService;

  @service('modal')
  declare modalService: ModalService;

  @tracked declare viewData: QueryResult;
  @tracked loadedObjectTypeAttributes = false;

  constructor(owner: Owner, args: ViewSignature['Args']) {
    super(owner, args);
    if (this.args.view.template) {
      this.loadData();
    } else {
      this.loadObjectTypeAttributes().then(() => {
        removeInvalidColumns(this.args.view);
      });
      try {
        if (this.args.view.objectType) loadFormTabs(this.store, this.args.view.objectType.id);
        this.loadDependencies.perform();
      } catch {
        // Since we're just loading default thumbnails, no need to show an error message
      }
    }
  }

  get visualisation() {
    return this.args.view.visualisation;
  }

  get name() {
    if (this.args.view.template) return this.args.view.name;

    return `${this.args.view.objectType?.name}: ${this.args.view.name}`;
  }

  async loadObjectTypeAttributes() {
    try {
      if (this.args.view.objectType) {
        await loadSelfAndRelatedObjectTypeAttributes(this.args.view.objectType, true);
      }
      this.loadedObjectTypeAttributes = true;
    } catch {
      // The problematic view will just not render
    }
  }

  async loadData() {
    try {
      const rules = this.args.view.interfaceElement?.linkedFilter?.objectQueryView?.activeRules;
      this.viewData = await this.store.adapterFor('view').fetchData(this.args.view.id, rules);
    } catch {
      // The problematic view will just not render, so no need to show an error message
    }
  }

  @cached
  get options() {
    if (!this.viewData) return;

    const data = this.viewData.chartData;

    switch (this.visualisation.viewType) {
    case VIEW_TYPE.RADAR_CHART:
      return new RadarChartOptions(this.visualisation.layout, data, this.intl);
    case VIEW_TYPE.LINE_CHART:
      return new LineChartOptions(this.visualisation.layout, data);
    case VIEW_TYPE.PIE_CHART:
      return new PieChartOptions(this.visualisation.layout, data, this.intl);
    case VIEW_TYPE.PYRAMID_CHART:
      return new PyramidChartOptions(this.visualisation.layout, data);
    case VIEW_TYPE.GAGE_CHART:
      return new GageChartOptions(this.visualisation.layout, data, this.intl);
    case VIEW_TYPE.TABLE:
      return new TableOptions(this.visualisation.layout, data);
    case VIEW_TYPE.NUMBER:
      return new NumberOptions(this.visualisation.layout, data, this.intl);
    case VIEW_TYPE.CALENDAR_CHART:
      return new CalendarChartOptions(this.visualisation.layout, data, this.intl);
    }
    return undefined;
  }

  get supportsClick() {
    if (
      !this.args.view.template ||
      this.args.view.template.pivot ||
      !(this.args.view.template.group ||
        (this.args.view.template.time_range && this.args.view.template.time_range.group_in_buckets))
    ) return false;

    return true;
  }

  get numberColor() {
    const options = this.options as NumberOptions;
    return colorForValue(options.value, options.layout.colorIntervals) ?? options.color;
  }

  @action
  onClick(eventParams: Record<string, unknown>, indicators?: unknown) {
    const objectTypeId = getObjectTypeIdFromTemplate(this.args.view.template);
    if (!objectTypeId) return;

    let data: Record<string, unknown>;

    if (this.visualisation.viewType == VIEW_TYPE.CALENDAR_CHART) {
      if (!Array.isArray(eventParams.value)) return;
      data = { group_0: eventParams.value[0] };
    } else if (eventParams.targetType === 'axisName') {
      const axisName = eventParams.name as string;
      const clickedIndicator = (indicators as Array<RadarIndicator>).find((indicator) => indicator.name === axisName);

      data = clickedIndicator?.data ?? {};
    } else {
      data = eventParams.data as Record<string, unknown>;
    }

    // extract selected group from clicked value
    const groups: Array<unknown> = [];
    const valueDimensions = Object.keys(data);
    this.viewData.groupDimensions.forEach((dimension) => {
      if (valueDimensions.includes(dimension.key)) {
        groups.push(data[dimension.key]);
      }
    });
    if (groups.length == 0) return;

    this.openModalForViewZoom(
      objectTypeId,
      groups
    );
  }

  async openModalForViewZoom(objectTypeId: number, dataPoint: Array<unknown>) {
    const objectType = this.store.peekRecord('object-type', objectTypeId);
    if (!objectType) return;

    const view = await this.createModalView(objectType);

    await all ([
      loadSelfAndRelatedObjectTypeAttributes(objectType, true),
      loadFormTabs(this.store, objectType.id),
      loadSelfAndRelatedDefaultThumbnails(objectType),
      loadViewDependencies(view)
    ]);
    if (view.allView) addAllColumns(view);

    const team = this.args.view.interfaceElement?.section?.interface?.dataScopeTeams[0];
    const objectTypeVariant = findObjectTypeVariantFor(team, objectType);
    const variantedObjectType = variantFor(objectType, objectTypeVariant);
    const columns = filterHiddenColumns(variantedObjectType, view.visualisation.layout.columns);

    const modal = new ObjectViewModal({
      context: {
        view: view,
        variantedObjectType: variantedObjectType,
        viewZoom: { view: this.args.view, dataPoint: dataPoint },
        interface: this.args.view.interfaceElement?.section?.interface,
        columns: columns
      }
    });
    this.modalService.open(modal);
  }

  async createModalView(objectType: ObjectTypeModel) {
    let view = await findDefaultViewWithFallback(objectType, this.store);
    if ([VIEW_TYPE.OBJECT_KANBAN, VIEW_TYPE.OBJECT_CALENDAR].includes(view.visualisation.viewType)) {
      view = await findAllViewWithFallback(objectType, this.store);
    }
    view.objectQueryView?.objectQueryRules.forEach((rule) => markRuleAsDeleted(rule));
    this.duplicateLinkedFilters(view);

    return view;
  }

  @action
  duplicateLinkedFilters(view: ViewModel) {
    if (!view.objectQueryView) return;

    this.args.view.interfaceElement?.linkedFilter?.objectQueryView?.activeRules.forEach((rule) => {
      const objectQueryRule = {
        ruleType: rule.ruleType,
        objectTypeAttributeId: rule.objectTypeAttributeId,
        columnName: rule.columnName,
        relationshipAttributeId: rule.relationshipAttributeId,
        relationshipAttribute2Id: rule.relationshipAttribute2Id,
        operator: rule.operator,
        sortOrder: rule.sortOrder,
        value: rule.value,
        value2: rule.value2,
        value3: rule.value3
      };
      this.store.createRecord('object-query-rule', {
        objectQueryView: view.objectQueryView,
        ...objectQueryRule
      });
    });
  }

  get variantedObjectType() {
    if (!this.args.view.objectType) return;

    const team = this.viewpoint.selectedTeam;
    const objectTypeVariant = findObjectTypeVariantFor(team, this.args.view.objectType);
    return variantFor(this.args.view.objectType, objectTypeVariant);
  }

  get columns() {
    if (this.args.view.template || !this.loadedObjectTypeAttributes || !this.variantedObjectType) return;

    return filterHiddenColumns(this.variantedObjectType, this.args.view.visualisation.layout.columns);
  }

  loadDependencies = task(async() => {
    await loadViewDependencies(this.args.view);
  });
}

function getObjectTypeIdFromTemplate(template: Template) {
  if (template && template.from &&
      typeof template.from === 'object' &&
      template.from.type === 'object_type' &&
      template.from.object_type_id) {
    return template.from.object_type_id;
  }

  return null;
}
