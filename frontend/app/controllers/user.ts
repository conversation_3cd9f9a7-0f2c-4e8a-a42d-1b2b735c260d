import Store from '@ember-data/store';
import Controller from '@ember/controller';
import { action } from '@ember/object';
import RouterService from '@ember/routing/router-service';
import { service } from '@ember/service';
import { all } from 'ember-concurrency';
import IntlService from 'ember-intl/services/intl';
import User from 'frontend/models/user';
import { handleError } from 'frontend/utils/handle-error';
import { updatePropertyTranslations } from 'frontend/utils/object-type-property';
import CurrentUserService from 'frontend/services/current-user';
import Viewpoint, { SYSTEM_VIEWPOINT } from 'frontend/services/viewpoint';
import { resetLoadedState } from 'frontend/utils/object-type-attributes-loader';
import Team from 'frontend/models/team';
import ToastService from 'frontend/services/toast';
import { TOAST_TYPES } from 'frontend/utils/toast';

export default class UserController extends Controller {
  @service
  declare intl: IntlService;

  @service('store')
  declare store: Store;

  @service
  declare router: RouterService;

  @service
  declare currentUser: CurrentUserService;

  @service
  declare viewpoint: Viewpoint;

  @service('toast')
  declare toastService: ToastService;

  availableLanguages: Record<string, string> = { "English": "en", "French": "fr", "Dutch": "nl", "Spanish": "es" };

  declare model: User;

  @action
  async saveSettings() {
    try {
      const modelChanges = this.model.changedAttributes();
      const newJoinRequests = this.model.userTeamJoinRequests.filter((request) => request.get('isNew'));
      const deletedJoinRequests = this.model.userTeamJoinRequests.filter((request) => request.get('isDeleted'));

      await all([
        ...newJoinRequests.map((r) => r.save()),
        ...deletedJoinRequests.map((r) => r.save()),
        ...this.deleteTeamRelationships(),
        this.model.save()
      ]);
      // refresh the object type names after language has changed
      if (modelChanges.language) {
        await all([
          this.store.findAll('object-type'),
          this.store.findAll('form-tab')
        ]);
        // clear cached OTA's since some of their properties are localized and are thus no longer valid after locale change
        resetLoadedState();
      }

      if (newJoinRequests.length) {
        this.toastService.create({
          title: this.intl.t('user_join_requests.toast.title'),
          type: TOAST_TYPES.INFO,
          text: this.intl.t('user_join_requests.toast.body')
        });
      }

      if (this.model.settings?.theme) document.querySelector('html')?.setAttribute('data-bs-theme', this.model.settings.theme);
      this.intl.setLocale(this.model.language || 'en');
      updatePropertyTranslations(this.intl);
      // If the viewpoint is not in the updated teams then set it to the first one
      if (this.viewpoint?.selectedTeam && !this.model.teams.includes(this.viewpoint?.selectedTeam)) {
        const defaultViewpoint = this.currentUser.user.admin ? SYSTEM_VIEWPOINT.ALL : SYSTEM_VIEWPOINT.OWNED;
        this.viewpoint.setViewpoint(this.model.teams[0] || defaultViewpoint, true);
      }
      // Reload current user so the teams update is reflected
      await this.currentUser.loadUser();
      this.router.transitionTo("index");
    } catch (error) {
      handleError(this, error);
    }
  }

  @action
  setLanguage(event: GenericEvent<HTMLInputElement>) {
    this.model.language = this.availableLanguages[event.target.value];
  }

  @action
  setTheme(event: GenericEvent<HTMLInputElement>) {
    const value = event.target.value === "dark" ? "dark" : "light";
    if (this.model?.settings) this.model.settings.theme = value;
  }

  @action
  removeTeam(team: Team) {
    this.model.teams.removeObject(team);
  }

  deleteTeamRelationships() {
    const user = this.currentUser.user;
    if (!user) return [];

    const removedTeams: Array<Team> = [];
    this.currentUser.user.teams.forEach((team) => {
      if (!this.model.teams.includes(team)) removedTeams.push(team);
    });

    return removedTeams.map((team) => {
      this.store.adapterFor('team-users').deleteTeamUser(team.id, user.id);
    });
  }
}
