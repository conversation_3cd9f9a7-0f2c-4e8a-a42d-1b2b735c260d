import Model, { attr, belongsTo } from '@ember-data/model';
import JobModel from './job';
import AssistantChatModel from './assistant-chat';

export enum MESSAGE_ORIGIN {
  USER = 'user',
  ASSISTANT = 'assistant'
}

export default class AssistantMessageModel extends Model {
  @belongsTo('assistant-chat', { async: false, inverse: 'messages' })
  declare chat: AssistantChatModel;

  @belongsTo('job', { async: false, inverse: null })
  declare job: JobModel;

  @attr
  declare origin: MESSAGE_ORIGIN;

  @attr
  declare content: Array<{ type: 'text' | 'data', data: unknown }>;

  @attr
  declare createdAt: Date;
}

// DO NOT DELETE: this is how TypeScript knows how to look up your models.
declare module 'ember-data/types/registries/model' {
  export default interface ModelRegistry {
    'assistant-message': AssistantMessageModel;
  }
}
