import Model, { attr, belongsTo } from "@ember-data/model";
import AutomationModel from "./automation";
import Team from "./team";
import AutomationMailActionModel from "./automation-mail-action";
import AutomationStaticDataActionModel from "./automation-static-data-action";
import AutomationAiActionModel from "./automation-ai-action";
import AutomationUpdateObjectActionModel from "./automation-update-object-action";
import AutomationWebhookActionModel from "./automation-webhook-action";

export default class AutomationActionModel extends Model {
  @attr
  declare actionType: ACTION_TYPE;

  @attr
  declare objectTypeVariantId?: string;

  @belongsTo('team', { async: false, inverse: 'automationActions' })
  declare team?: Team;

  @belongsTo('automation', { async: false, inverse: 'automationActions' })
  declare automation: AutomationModel;

  @belongsTo('automation-mail-action', { async: false, inverse: 'automationAction' })
  declare automationMailAction?: AutomationMailActionModel;

  @belongsTo('automation-ai-action', { async: false, inverse: 'automationAction' })
  declare automationAiAction?: AutomationAiActionModel;

  @belongsTo('automation-update-object-action', { async: false, inverse: 'automationAction' })
  declare automationUpdateObjectAction?: AutomationUpdateObjectActionModel;

  @belongsTo('automation-static-data-action', { async: false, inverse: 'automationAction' })
  declare automationStaticDataAction?: AutomationStaticDataActionModel;

  @belongsTo('automation-webhook-action', { async: false, inverse: 'automationAction' })
  declare automationWebhookAction?: AutomationWebhookActionModel;

  declare isDirty?: boolean;

  async rollback() {
    if (this.id) {
      await this.reload();
    }
    this.automationMailAction?.rollback();
    this.automationAiAction?.rollback();
    this.automationUpdateObjectAction?.rollback();
    this.automationWebhookAction?.rollback();
    this.rollbackAttributes();
    this.isDirty = false;
  }
}

// DO NOT DELETE: this is how TypeScript knows how to look up your models.
declare module 'ember-data/types/registries/model' {
  export default interface ModelRegistry {
    'automation-action': AutomationActionModel;
  }
}

export enum ACTION_TYPE {
  MAIL = 'mail',
  STATIC_DATA = 'static_data',
  INTEGRATION = 'integration',
  AI = 'ai',
  UPDATE_OBJECT = 'update_object',
  WEBHOOK = 'webhook'
}
