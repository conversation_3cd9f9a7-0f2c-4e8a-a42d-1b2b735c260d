import Model, { attr, belongsTo } from "@ember-data/model";
import ObjectTypeAttributeModel from "./object-type-attribute";
import AutomationWebhookActionModel from "./automation-webhook-action";

export default class AutomationWebhookActionAttributeModel extends Model {
  @belongsTo('automation-webhook-action', { async: false, inverse: 'automationWebhookActionAttributes' })
  declare automationWebhookAction: AutomationWebhookActionModel;

  @belongsTo('object-type-attribute', { async: false, inverse: null })
  declare objectTypeAttribute: ObjectTypeAttributeModel;

  @attr
  declare columnName?: string;
}

// DO NOT DELETE: this is how TypeScript knows how to look up your models.
declare module 'ember-data/types/registries/model' {
  export default interface ModelRegistry {
    'automation-webhook-action-attribute': AutomationWebhookActionAttributeModel;
  }
}
