import Model, { SyncHas<PERSON>any, attr, belongsTo, hasMany } from "@ember-data/model";
import AutomationActionModel from "./automation-action";
import AutomationWebhookActionAttributeModel from "./automation-webhook-action-attribute";

export default class AutomationWebhookActionModel extends Model {
  @attr
  declare url: string;

  @belongsTo('automation-action', { async: false, inverse: 'automationWebhookAction' })
  declare automationAction: AutomationActionModel;

  @hasMany('automation-webhook-action-attributes', { async: false, inverse: 'automationWebhookAction' })
  declare automationWebhookActionAttributes: SyncHasMany<AutomationWebhookActionAttributeModel>;

  rollback() {
    this.rollbackAttributes();
  }
}

// DO NOT DELETE: this is how TypeScript knows how to look up your models.
declare module 'ember-data/types/registries/model' {
  export default interface ModelRegistry {
    'automation-webhook-action': AutomationWebhookActionModel;
  }
}
