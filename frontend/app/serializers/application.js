import JSONAPISerializer from '@ember-data/serializer/json-api';
import { underscore } from '@ember/string';

export default class ApplicationSerializer extends JSONAPISerializer {
  extractErrors = false;

  keyForAttribute(attr) {
    return underscore(attr);
  }

  keyForRelationship(attr) {
    return underscore(attr);
  }

  payloadKeyFromModelName(attr) {
    return underscore(attr);
  }

  normalizeSaveResponse(store, primaryModelClass, payload, id, requestType) {
    this.updatePrivateIds(store, payload);
    return super.normalizeSaveResponse(...arguments);
  }

  updatePrivateIds(store, payload) {
    payload.included?.forEach((model) => {
      if (model.attributes?.__guid__) {
        const record = store.peekRecord({ lid: model.attributes.__guid__ });

        // let ember data know the model with guid has received an id
        if (record) {
          record.id = model.id;
        }
      }
    });
  }

  serialize() {
    let serialized = super.serialize(...arguments);
    // Move included to its own section
    if (serialized.data.included) {
      serialized.included = serialized.data.included;
      delete serialized.data.included;
    }

    return serialized;
  }

  serializeHasMany(snapshot, json, relationship) {
    if (this.attrsHasMany?.includes(relationship.key)) {
      this.serializeHasManyModified(...arguments);
      this.includeRelationship(json, snapshot.hasMany(relationship.key));
    } else {
      super.serializeHasMany(snapshot, json, relationship);
    }
  }

  serializeBelongsTo(snapshot, json, relationship) {
    if (this.attrsBelongsTo?.includes(relationship.key)) {
      this.serializeBelongsToModified(...arguments);
      const belongsTo = snapshot.belongsTo(relationship.key);

      if (belongsTo) this.includeRelationship(json, [belongsTo]);
    } else {
      super.serializeBelongsTo(snapshot, json, relationship);
    }
  }

  serializeHasManyModified(snapshot, json, relationship) {
    let key = relationship.key;

    if (this.shouldSerializeHasMany(snapshot, key, relationship)) {
      let hasMany = snapshot.hasMany(key);
      if (hasMany !== undefined) {
        json.relationships = json.relationships || {};

        let schema = this.store.modelFor(snapshot.modelName);
        let payloadKey = this._getMappedKey(key, schema);
        if (payloadKey === key && this.keyForRelationship) {
          payloadKey = this.keyForRelationship(key, 'hasMany', 'serialize');
        }

        // MODIFIED: Do not filter out new relationships
        let nonNewHasMany = hasMany.filter((item) => item.record);
        let data = new Array(nonNewHasMany.length);

        for (let i = 0; i < nonNewHasMany.length; i++) {
          let item = hasMany[i];
          let payloadType = this.payloadKeyFromModelName(item.modelName);

          // MODIFIED: Also support the frontend generated temporary ID
          data[i] = { type: payloadType };
          if (item.id) {
            data[i].id = item.id;
          } else {
            data[i].__guid__ = item.identifier.lid;
          }
        }

        json.relationships[payloadKey] = { data };
      }
    }
  }

  serializeBelongsToModified(snapshot, json, relationship) {
    let key = relationship.key;

    if (this._canSerialize(key)) {
      let belongsTo = snapshot.belongsTo(key);

      if (belongsTo === null || belongsTo) {
        json.relationships = json.relationships || {};

        let schema = this.store.modelFor(snapshot.modelName);
        let payloadKey = this._getMappedKey(key, schema);
        if (payloadKey === key) {
          payloadKey = this.keyForRelationship(key, 'belongsTo', 'serialize');
        }

        let data = null;

        // MODIFIED: Also support the frontend generated temporary ID
        if (belongsTo) {
          let payloadType = this.payloadKeyFromModelName(belongsTo.modelName);
          data = { type: payloadType };
          if (belongsTo.id) {
            data.id = belongsTo.id;
          } else {
            data.__guid__ = belongsTo.identifier.lid;
          }
        }

        json.relationships[payloadKey] = { data: data };
      }
    }
  }

  includeRelationship(json, other) {
    if (other && other.length > 0) {
      json.included = json.included || [];
      other.forEach((snapshot) => {
        const serializedSnapshot = snapshot.serialize();
        let data = serializedSnapshot.data;
        if (snapshot.id) {
          data.id = snapshot.id;
        } else {
          data.__guid__ = snapshot.identifier.lid;
        }
        json.included.push(data);

        // add the included relationships of the snapshot also in the json included
        if (serializedSnapshot.included) {
          json.included.push(...serializedSnapshot.included);
        }
      });
    }
  }
}
