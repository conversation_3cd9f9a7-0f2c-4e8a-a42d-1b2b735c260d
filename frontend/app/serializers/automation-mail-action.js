import AutomationMailActionMailAddressAttributeModel from 'frontend/models/automation-mail-action-address-attribute';
import ApplicationSerializer from './application';

export default class AutomationMailActionSerializer extends ApplicationSerializer {
  attrs = {
    automationMailActionAttributes: { serialize: true },
    automationMailActionAddressAttributes: { serialize: true }
  };

  attrsHasMany = ["automationMailActionAttributes", "automationMailActionAddressAttributes"];
}
