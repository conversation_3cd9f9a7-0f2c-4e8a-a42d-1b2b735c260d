import Service, { service } from '@ember/service';
import Store from '@ember-data/store';
import { tracked } from '@glimmer/tracking';
import { apiRequest } from 'frontend/utils/api-request';
import IntlService from 'ember-intl/services/intl';

export default class CurrentInstanceService extends Service {
  @service declare store: Store;
  @tracked declare companyName?: string;
  @tracked declare hideRecentObjectsSection?: boolean;
  @tracked declare hideTeamFieldOnForms?: boolean;
  @tracked declare displayAvatarWithCompanyLogo?: boolean;
  @tracked declare logo?: string;
  @service declare intl: IntlService;

  async loadSettings() {
    try {
      const response = await ((await apiRequest('/api/current_instance')).json());
      this.companyName = response.data.attributes.company_name;
      this.hideRecentObjectsSection = response.data.attributes.hide_recent_objects_section;
      this.hideTeamFieldOnForms = response.data.attributes.hide_team_field_on_forms;
      this.displayAvatarWithCompanyLogo = response.data.attributes.display_avatar_with_company_logo;
      this.logo = response.data.attributes.logo;
    } catch {
      this.companyName = this.intl.t('root_level');
    }
  }
}

// DO NOT DELETE: this is how TypeScript knows how to look up your services.
declare module '@ember/service' {
  interface Registry {
    'current-instance': CurrentInstanceService;
  }
}
