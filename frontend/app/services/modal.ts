import Service, { service } from '@ember/service';
import RouterService from '@ember/routing/router-service';
import { next } from '@ember/runloop';
import Modal from 'frontend/utils/modals/modal';
import ObjectModal from 'frontend/utils/modals/object-modal';
import ObjectModel from 'frontend/models/object';
import Transition from '@ember/routing/transition';
import { tracked } from '@glimmer/tracking';

type MODAL_EVENT = 'willTransition';

export default class ModalService extends Service {
  @service declare router: RouterService;

  modals: Array<Modal> = [];
  callbacks: Record<string, Array<(...args: Array<unknown>) => unknown>> = {};

  @tracked
  declare _rerender: boolean;

  constructor(properties?: object) {
    super(properties);
    this.router.on('routeWillChange', (transition: Transition) => {
      if (transition.queryParamsOnly || transition.isAborted) return;

      const bottomModal = this.modals[0];
      if (bottomModal) this.close(bottomModal);
    });
  }

  get topModal() {
    return this.modals.lastObject;
  }

  isTopModal(modal: Modal) {
    return modal === this.topModal;
  }

  peekTop(number: number) {
    if (number < 1) return [];

    return this.modals.slice(-1 * number);
  }

  open(modal: Modal, transition?: ModalTransition) {
    if (!transition) transition = new ModalTransition(this, 'open', modal);
    this.trigger('willTransition', transition);
    if (transition.aborted) return;

    this.modals.pushObject(modal);
    this.toggleRerender();
  }

  transitionToModal(modal: Modal, transition?: ModalTransition) {
    const index = this.modals.indexOf(modal);
    if (index < 0) return;

    // close the modal immediately on top of the one where we want to navigate to
    const modalToClose = this.modals[index + 1];
    if (modalToClose) {
      if (!transition) transition = new ModalTransition(this, 'transition', modal);
      this.close(modalToClose, transition);
    }
  }

  // close a modal
  // automatically closes modals opened after the closed one
  close(modal: Modal, transition?: ModalTransition) {
    const index = this.modals.indexOf(modal);
    if (index < 0) return;

    if (!transition) transition = new ModalTransition(this, 'close', modal);
    this.trigger('willTransition', transition);
    if (transition.aborted) return;

    // close all modals from top until and including the one supplied as argument
    let topModalIndex = this.modals.length - 1;
    for (topModalIndex; topModalIndex >= index; topModalIndex--) {
      if (this.modals[topModalIndex].canBeClosed) {
        this.modals[topModalIndex].trigger('close');
        this.modals.removeAt(topModalIndex);
      } else {
        this.toggleRerender();
        this._scheduleTransition(transition);
        return;
      }
    }
    this.toggleRerender();
  }

  _scheduleTransition(transition: ModalTransition) {
    // schedule the transition for the next runloop
    next(() => {
      transition.retry();
    });
  }

  closeObjectRelatedModals(object: ObjectModel) {
    this.modals.some((modal) => {
      if (modal.type !== ObjectModal.type) return false;
      if ((modal as ObjectModal).object !== object) return false;

      this.close(modal);
      return true;
    });
  }

  on(event: MODAL_EVENT, callback: (...args: Array<unknown>) => unknown) {
    let callbacks = this._getCallbacksForEvent(event);
    if (!callbacks) {
      callbacks = [];
      this.callbacks[event] = callbacks;
    }
    callbacks.push(callback);
  }

  off(event: MODAL_EVENT, callback: (...args: Array<unknown>) => unknown) {
    this._getCallbacksForEvent(event)?.removeObject(callback);
  }

  trigger(event: MODAL_EVENT, ...args: Array<unknown>) {
    const callbacks = this._getCallbacksForEvent(event);
    if (!callbacks) return;

    callbacks.forEach((callback) => {
      callback(...args);
    });
  }

  _getCallbacksForEvent(event: MODAL_EVENT) {
    return this.callbacks[event];
  }

  toggleRerender() {
    this._rerender = !this._rerender;
  }
}

export class ModalTransition {
  declare targetModal: Modal;
  declare modalService: ModalService;
  declare action: 'open' | 'transition' | 'close';
  aborted = false;

  constructor(modalService: ModalService, action: typeof this.action, targetModal: Modal) {
    this.targetModal = targetModal;
    this.modalService = modalService;
    this.action = action;
  }

  abort() {
    this.aborted = true;
  }

  retry() {
    this.aborted = false;
    switch (this.action) {
    case 'open':
      this.modalService.open(this.targetModal, this);
      break;
    case 'transition':
      this.modalService.transitionToModal(this.targetModal, this);
      break;
    case 'close':
      this.modalService.close(this.targetModal, this);
    }
  }
}

// DO NOT DELETE: this is how TypeScript knows how to look up your services.
declare module '@ember/service' {
  interface Registry {
    'modal': ModalService;
  }
}
