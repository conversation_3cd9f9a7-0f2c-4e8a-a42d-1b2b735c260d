// abstract: contains scss definitions that ideally don't generate any css rule by themselves,
// e.g variables, functions, mixins etc.
@import
  "abstracts/mixins";

@import
  "vendors/bootstrap",
  "vendors/gridstack";

// base/base: rules that apply to universal building blocks, e.g body, h1, a etc.
// base/helpers: utility rules that don't apply to a particular UI block, e.g .big-ass-text
@import
  "base/base",
  "base/helpers",
  "base/forms",
  "base/print";

// Layout: contains structural elements which subdivide the page and in general are always visible
@import
  "layout/main",
  "layout/navbar",
  "layout/sidebar";

// Components: reusable UI blocks that are composited together in parent components or layout elements
@import
  "components/assistant_response",
  "components/attachment",
  "components/brand",
  "components/card",
  "components/carousel",
  "components/icon_card",
  "components/icon_list",
  "components/icon_picker",
  "components/dropdown",
  "components/formula_parameter_label",
  "components/quill_editor",
  "components/table",
  "components/toast",
  "components/toast_container",
  "components/user_avatar",
  "components/modals/modal",
  "components/modals/object_modal",
  "components/modals/edit_calculation_modal",
  "components/edit_app_right_panel",
  "components/edit_object_value_dialog",
  "components/tabs",
  "components/slider",
  "components/floating_action_button",
  "components/interface_element",
  "components/target_configuration",
  "components/template_filter_rule",
  "components/thumbnail",
  "components/thumbnail_list",
  "components/view",
  "components/viewpoint_selector",
  "components/views_window",
  "components/form_builder",
  "components/color_grid",
  "components/automation_settings",
  "components/thumbnail_editor",
  "components/discussion",
  "components/tooltip",
  "components/tom_select",
  "components/object_timeline",
  "components/kanban",
  "components/form_tab_panel",
  "components/filter_window",
  "components/signature_pad",
  "components/color_intervals";

// Pages
@import
  "pages/interface-edit/editor";
