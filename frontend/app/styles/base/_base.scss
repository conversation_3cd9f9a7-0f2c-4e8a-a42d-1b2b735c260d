html {
  height: 100%;
}

body {
  height: 100%;
  font-family: <PERSON><PERSON><PERSON>, sans-serif;
  font-weight: $font-weight-normal;
  background-color: var(--bs-body-bg);
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: min-content 1fr;
  grid-template-areas:
    "sidebar navbar"
    "sidebar main";
  @include media-breakpoint-down(lg) {
    grid-template-columns: 1fr;
    grid-template-areas:
      "header"
      "main"
      "navbar";
  }

  overflow: hidden;
}

// To prevent ugly field on iOS
input[type="date"],
input[type="datetime-local"] {
  -webkit-appearance: textfield;

  // https://simplernerd.com/js-align-text-left-ios-date-input/
  &::-webkit-date-and-time-value {
    text-align: left;
  }
}
