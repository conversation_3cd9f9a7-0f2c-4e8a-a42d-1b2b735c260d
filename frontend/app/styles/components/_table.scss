$check-col-width: 36px; // width of checkbox column

.table {
  .first-column-width {
    width: $check-col-width;
  }

  tbody {
    > tr {
      // height works as min-height for this
      height: 2.5em;

      > td {
        max-width: 50ch;
      }
    }
  }
}

@include color-mode(dark) {
  thead.table-secondary {
    --bs-table-bg: var(--bs-tertiary-bg);
  }
}

.table-header-fixed {
  @extend %mini-scrollbar;

  .table {
    overflow: auto;
    position: relative;
    border-collapse: separate;
    border-spacing: 0;
  }

  thead {
    position: sticky;
    top: 0;
    z-index: 2;
  }
}

.object-table {
  thead {
    > tr > th {
      font-weight: $font-weight-bold;
      max-width: 50ch;
    }

    @media (hover: hover) {
      th:not(:hover) .column-options:not([aria-expanded="true"]) {
        opacity: 0;
      }
    }
  }

  tbody {
    > tr > td {
      position: relative;
      background-color: var(--bs-body-bg);

      .edit-icon {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(-50%, -50%);
        opacity: 0;
        color: $primary;
        transition-property: opacity;
        transition-duration: 0.1s;

        @include media-breakpoint-down(lg) {
          display: none;
        }
      }

      @media (hover: hover) {
        &:has([aria-expanded="true"]) .edit-icon,
        &:hover.inline-edit-active .edit-icon {
          transition-delay: 0.2s;
          opacity: 1;
          border: none;
        }
      }

      .inline-edit-dropdown {
        cursor: default;
        max-width: 50ch;
        z-index: 2;

        .inline-form-element-label {
          font-size: 0.75rem;
          font-weight: $font-weight-bold;
          line-height: 1;
          width: 100%;
          text-align: left !important;
        }
      }
    }
  }

  .sticky-column {
    max-width: min(33vw, 50ch) !important;
    left: $check-col-width;
  }

  th.sticky-column button {
    overflow: visible;
  }

  .sticky-left {
    min-width: $check-col-width;
  }

  .sticky-left,
  .sticky-column {
    position: sticky;
    z-index: 1;
  }

  .subheader {
    font-size: 12px;
    font-weight: 300;
  }
}
