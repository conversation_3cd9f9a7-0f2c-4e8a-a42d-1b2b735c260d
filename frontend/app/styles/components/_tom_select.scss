.ts-dropdown {
  z-index: 1055;
}

.ts-hidden-accessible {
  height: 1px !important;
}

.ts-control input {
  color: var(--bs-body);
}

.ts-control .item {
  min-width: 0;
}

.ts-control .rounded-pill {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.ts-dropdown .rounded-pill {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
