{{page-title (t "navigation.home")}}
<nav class="p-2 d-lg-none">
  <span class="flex-grow-1 me-0">
    <LinkTo @route="index" class="text-decoration-none text-center">
      <div class="text-center">
        <img src="/logos/logo_dark.png" alt="" role="none" class="brand-logo me-2">
      </div>
    </LinkTo>
  </span>
</nav>

{{#if this.showEmptyMessage}}
  <div class="text-secondary text-center mt-4">{{t "no_items_to_display"}}</div>
{{else}}
  {{#if this.hasAvailableObjectTypes}}
    <div class="d-none d-lg-block text-end mt-2">
      {{#if (has-video)}}
        <button type="button" class="btn btn-accent" {{on "click" this.openQrScannerModal}} > <i class="fa fa-camera" /> {{t "scan"}}</button>
      {{/if}}
      <button type="button" class="btn btn-accent me-2" data-bs-toggle="dropdown"> <i class="fa fa-plus" /> {{t "new"}}</button>
      <ObjectTypeDropdown @lastVisitedObjectTypes={{this.lastVisitedObjectTypes}} @selectObjectType={{this.createNewObject}} />
    </div>
  {{/if}}

  <section class="mt-3 bold-text">
    {{#if this.lastVisitedObjectTypes.length}}
      <ObjectTypeGroup @objectTypes={{this.lastVisitedObjectTypes}} @name={{t "recently_used"}} @disableNewObjectTypeButton={{true}} />
    {{/if}}
    {{#each @model as |objectTypeGroup|}}
      {{#if (objectTypeGroup.filteredObjectTypes this.viewpoint.selectedViewpoint)}}
        <ObjectTypeGroup @objectTypeGroup={{objectTypeGroup}}/>
      {{/if}}
    {{/each}}
    {{#if this.showOthers}}
      <ObjectTypeGroup @objectTypes={{this.otherObjectTypes}} @name={{t "other"}} />
    {{/if}}
  </section>

  <div {{did-insert this.loadOffcanvas}} {{on "hide.bs.offcanvas" this.resetScannedQrCode}} class="offcanvas offcanvas-bottom rounded-top pt-2 p-0">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title fw-bold">{{t "new"}}</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
    </div>
    <ObjectTypeOffcanvas @lastVisitedObjectTypes={{this.lastVisitedObjectTypes}} @selectObjectType={{this.createNewObject}} />
  </div>

  {{#if this.hasAvailableObjectTypes}}
    <div class="d-lg-none fab-container">
      <FloatingActionButton>
        {{#if (has-video)}}
          <FloatingActionButtonOption @action={{this.openQrScannerModal}} @icon="fa-camera" @name={{t "scan"}} />
        {{/if}}
        <FloatingActionButtonOption @action={{this.showObjectTypes}} @icon="fa-plus" @name={{t "new"}} />
      </FloatingActionButton>
    </div>
  {{/if}}
{{/if}}
