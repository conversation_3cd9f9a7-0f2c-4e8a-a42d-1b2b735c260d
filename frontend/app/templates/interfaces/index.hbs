{{page-title (t "navigation.interfaces")}}

<nav class="p-2 d-lg-none">
  <span class="flex-grow-1 me-0">
    <LinkTo @route="index" class="text-decoration-none text-center">
      <div class="text-center">
        <img src="/logos/logo_dark.png" alt="" role="none" class="brand-logo me-2">
      </div>
    </LinkTo>
  </span>
</nav>

{{#if this.interfaceFetcher.isRunning}}
  <Utility::Loader @task={{this.interfaceFetcher}} />
{{else}}
  {{#unless (or this.data.length this.allowInterfaceCreate)}}
    <div class="text-secondary text-center mt-4">{{t "no_items_to_display"}}</div>
  {{/unless}}

  <div class="icon-list m-3">
    {{#each this.data as |interface|}}
      {{#if (and interface.team (eq interface.team.id this.viewpoint.selectedTeam.id))}}
        <LinkTo @route="home">
          <IconCard
            @name={{interface.name}}
            @icon={{interface.icon}}
            @color={{interface.color}}
            @backgroundIcon="fas fa-square"
          />
        </LinkTo>
      {{else}}
        <LinkTo @route="interface.show" @model={{interface.id}}>
          <IconCard
            @name={{interface.name}}
            @icon={{interface.icon}}
            @color={{interface.color}}
            @backgroundIcon="fas fa-square"
          />
        </LinkTo>
      {{/if}}
    {{/each}}
    {{#if this.allowInterfaceCreate}}
      <a {{on "click" this.openCreateInterfaceModal}} role="button">
        <IconCard
          @name={{t "new_interface"}}
          @icon="fa fa-plus"
          @color="#F3F3F3"
          @backgroundIcon="fas fa-square"
          @greyIcon={{true}}
        />
      </a>
    {{/if}}
  </div>
{{/if}}

