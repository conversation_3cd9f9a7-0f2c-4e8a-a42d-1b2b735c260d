import { VariantedObjectTypeAttribute, findObjectTypeVariantFor, variantFor } from 'frontend/utils/variants';
import { loadSelfAndRelatedObjectTypeAttributes } from 'frontend/utils/object-type-attributes-loader';
import { createObjectWithDefaults } from 'frontend/utils/create-object-with-defaults';
import ObjectModal from 'frontend/utils/modals/object-modal';
import ObjectModel from 'frontend/models/object';
import Team from 'frontend/models/team';
import { getOwner } from '@ember/owner';
import { loadFormTabs } from './form-tab-loader';
import CalculationModel from 'frontend/models/calculation';
import { generateCalculationPayload, previewCalculate } from './calculations';

interface Options {
  calculations?: Array<CalculationModel>;
}

export async function addNewRelationshipObject(self: object, relationshipAttribute: VariantedObjectTypeAttribute, object: ObjectModel, team?: Team, options?: Options) {
  const objectType = relationshipAttribute.targetObjectType;
  if (!objectType) return;

  const owner = getOwner(self);
  if (!owner) return;

  const store = owner.lookup("service:store");
  const modalService = owner.lookup("service:modal");

  const variant = findObjectTypeVariantFor(team, objectType);
  const variantedObjectType = variantFor(objectType, variant);
  await loadSelfAndRelatedObjectTypeAttributes(objectType, true);
  await loadFormTabs(store, variantedObjectType.id);

  let linkedAttribute;
  if (relationshipAttribute.inverse && relationshipAttribute.linkedObjectTypeAttributes.length === 1) {
    linkedAttribute = relationshipAttribute.linkedObjectTypeAttributes[0];
  }
  const skipList = linkedAttribute ? [linkedAttribute.id] : [];

  const newObject = createObjectWithDefaults(self, variantedObjectType, team, undefined, undefined, skipList);

  if (linkedAttribute) {
    store.createRecord('object-relationship', {
      objectTypeAttribute: linkedAttribute,
      model: object,
      object: newObject
    });
  }

  const modal = new ObjectModal({
    context: {
      object: newObject
    },
    callbacks: {
      onObjectSave: (createdObject: ObjectModel) => {
        if (!relationshipAttribute.inverse) {
          if (relationshipAttribute.relationshipKind == "single") {
            object.updateSingleRelationshipForAttribute(relationshipAttribute.original, createdObject);
          } else {
            object.addRelationshipForAttribute(relationshipAttribute.original, createdObject);
          }
          if (options?.calculations?.length) {
            const payload = generateCalculationPayload(options.calculations, object);
            previewCalculate(object, payload);
          }
          object.setDirtyState(true);
        }
      },
      close: () => {
        if (relationshipAttribute.inverse && newObject.id === undefined) { newObject.objectRelationships.slice().forEach((rel) => rel.unloadRecord()); }
      }
    }
  });
  modalService.open(modal);
}
