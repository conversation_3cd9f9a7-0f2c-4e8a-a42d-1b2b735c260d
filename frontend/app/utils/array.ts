/**
 * Get a random element from the array
 */
export function sample<T extends Array<unknown>>(array: T): T[number] {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Sorts the array in place based on a key
 * @param items - The array of items that needs sorting
 * @param key - Field on which the array should be sorted
 */
export function sortByKey<T extends Array<object>>(items: T, key: keyof T[number]) {
  return items.sort((a: T[number], b: T[number]) => {
    const valueA = a[key];
    const valueB = b[key];

    if (typeof valueA === "string" && typeof valueB === "string") {
      return valueA.localeCompare(valueB, undefined, { sensitivity: 'base' });
    }

    if (valueA === null || valueA === undefined || valueB === null || valueB === undefined) {
      return valueA == null ? (valueB == null ? 0 : 1) : -1;
    }

    return valueA === valueB ? 0 : (valueA > valueB) ? 1 : -1;
  });
}
