import AssistantChatModel from 'frontend/models/assistant-chat';
import { tracked } from '@glimmer/tracking';
import { getOwner } from '@ember/owner';
import Store from '@ember-data/store';
import JobService from 'frontend/services/job';
import IntlService from 'ember-intl/services/intl';
import JobModel from 'frontend/models/job';
import AssistantMessageModel, { MESSAGE_ORIGIN } from 'frontend/models/assistant-message';
import { AssistantResult } from 'frontend/utils/job/assistant-result';

export class AssistantChatSession {
  declare chat: AssistantChatModel;
  declare job?: JobModel;
  declare store: Store;
  declare jobService: JobService;
  declare intl: IntlService;

  @tracked
  declare entries: Array<AssistantChatSessionEntry>;

  constructor(chat: AssistantChatModel) {
    this.chat = chat;
    const owner = getOwner(chat);
    this.store = owner?.lookup('service:store') as Store;
    this.jobService = owner?.lookup('service:job') as JobService;
    this.intl = owner?.lookup('service:intl') as IntlService;
    this.entries = chat.messages.slice().map((message) => {
      return buildChatSessionEntriesFromMessage(message);
    }).flat();
  }

  get isProcessing() {
    console.log({
      job: this.job,
    });

    const isProcessing = Boolean(this.job?.isInProgress || this.job?.isPending);
    console.log({ isProcessing });
    return isProcessing;
  }

  addEntries(entries: AssistantChatSessionEntry | Array<AssistantChatSessionEntry>) {
    if (Array.isArray(entries)) {
      this.entries = this.entries.concat(entries);
    } else {
      this.entries = [...this.entries, entries];
    }
  }

  async addUserMessage(content: string) {
    console.log('Adding message');
    const message = this.store.createRecord(
      'assistant-message',
      {
        origin: MESSAGE_ORIGIN.USER,
        content: [{
          type: 'text',
          data: content
        }],
        chat: this.chat
      }
    );
    console.log('Building entries');
    const entries = buildChatSessionEntriesFromMessage(message);
    this.addEntries(entries);
    console.log('Saving the message');
    await message.save();

    this.job = message.job;
    console.log('job set to', this.job);
    if (this.job) {
      console.log('Adding the response callback');
      this.addResponseCallback(this.job);
    }

    console.log('Returning the message');
    return message;
  }

  addResponseCallback(job: JobModel) {
    this.jobService.subscribeJobActivity(job, {
      completed: async() => {
        const result = job.result as AssistantResult;
        const messageId = result?.data?.message_id;
        if (messageId) {
          try {
            const response = await this.store.findRecord('assistant-message', messageId);
            const entries = buildChatSessionEntriesFromMessage(response);
            this.addEntries(entries);
          } catch {
            this.addEntries(buildErrorEntry(this.intl));
          }
        }
      },
      failed: () => {
        this.addEntries(buildErrorEntry(this.intl));
      }
    });
  }
}

export enum ENTRY_TYPE {
  TEXT = 'text',
  DATA = 'data',
  TOOL = 'tool'
}
enum ENTRY_ORIGIN {
  USER = 'user',
  ASSISTANT = 'assistant'
}
type TextContent = string;
type DataContent = { data: unknown };
export type ToolContent = {
  tool_call_id: string;
  tool_name: string;
  arguments: unknown;
  output: { content: string, data: unknown };
};
type EntryContent = TextContent | DataContent | ToolContent;

class AssistantChatSessionEntry {
  declare type: ENTRY_TYPE;
  declare origin: ENTRY_ORIGIN;
  declare content: EntryContent;
  declare timestamp: Date;

  constructor(type: ENTRY_TYPE, origin: ENTRY_ORIGIN, content: EntryContent, timestamp: Date) {
    this.type = type;
    this.origin = origin;
    this.content = content;
    this.timestamp = timestamp;
  }
}

function buildChatSessionEntriesFromMessage(message: AssistantMessageModel) {
  return message.content.map((part) => {
    return new AssistantChatSessionEntry(
      part.type as ENTRY_TYPE,
      (message.origin as unknown) as ENTRY_ORIGIN,
      (part.data as unknown) as EntryContent,
      message.createdAt || new Date()
    );
  });
}

function buildErrorEntry(intl: IntlService) {
  return new AssistantChatSessionEntry(
    ENTRY_TYPE.TEXT,
    ENTRY_ORIGIN.ASSISTANT,
    intl.t('assistant.message_processing_error'),
    new Date()
  );
}
