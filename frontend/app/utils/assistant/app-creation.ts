interface ObjectTypeAttributePayload {
  allowed_values?: any;
  data_type: string;
  description?: string;
  guid: string;
  name_en?: string;
  name_es?: string;
  name_fr?: string;
  name_nl?: string;
  relationship_kind?: string;
  searchable_for_recommendations?: boolean;
  target_class?: string;
  target_column_name?: string;
  target_object_type_attribute_guid?: string;
  target_object_type_guid?: string;
}

interface ObjectTypePayload {
  attributes: Array<ObjectTypeAttributePayload>;
  color?: string;
  guid: string;
  icon?: string;
  name_en?: string;
  name_es?: string;
  name_fr?: string;
  name_nl?: string;
  recommended_object_type_attribute_guid?: string;
}

export class AppCreation {
  locale: string;
  data: Array<ObjectTypePayload>;

  constructor(locale: string, data: Array<ObjectTypePayload>) {
    this.locale = locale;
    this.data = data;
  }

  get objectTypes() {
    const localeKey = `name_${this.locale}` as 'name_en' | 'name_es' | 'name_fr' | 'name_nl';

    const objectTypesWithLocalizeNames = this.data
      .map((payload) => ({
        ...payload,
        name: payload[localeKey] ?? ''
      }));

    return objectTypesWithLocalizeNames.map((ot) => ({
      ...ot,
      attributes: ot.attributes
        .map((attribute) => ({
          ...attribute,
          dataType: attribute.data_type,
          name: attribute[localeKey] ?? '',
          relationshipTarget: attribute.data_type === "Relationship" ? objectTypesWithLocalizeNames.find((ot) => ot.guid === attribute.target_object_type_guid) : undefined
        }))
    }));
  }
}
