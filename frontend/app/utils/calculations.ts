import CalculationModel, { CALCULATION_TYPE } from "frontend/models/calculation";
import ObjectTypeAttributeModel from "frontend/models/object-type-attribute";
import { VariantedObjectTypeAttribute } from "./variants";
import ObjectModel from "frontend/models/object";

/**
 * Get a list of all calculations that use the given attribute as input variable including chained calculations.
 * This list is limited to the calculations for the objectType of the attribute.
 * @param objectTypeAttribute Attribute
 * @returns List of chained calculations that use the attribute as input variable
 */
export function getCalculationsDependingOnObjectTypeAttribute(objectTypeAttribute: ObjectTypeAttributeModel | VariantedObjectTypeAttribute) {
  // Determine a list of all calculations for the given object type or variant thereof
  const calculationAttributes = objectTypeAttribute.objectType.objectTypeAttributes.filter((ota) => {
    return ota.calculated && ota.calculation?.calculationType === CALCULATION_TYPE.FORMULA;
  });

  // Determine the calculations that use this attribute as input variable
  const calculationSet = calculationAttributes.reduce((set, ota) => {
    if (ota.calculation && !set.has(ota.calculation) && ota.calculation.objectTypeAttributePaths.some((path) => {
      // If there are relationships, look at the first relationship instead of the data attribute
      if (path.relationshipReferences?.length) {
        return path.relationshipReferences[0].attribute_id === objectTypeAttribute.id;
      }
      return path.belongsTo('dataAttribute').id() === objectTypeAttribute.id;
    })) {
      set.add(ota.calculation);
    }
    return set;
  }, new Set<CalculationModel>());

  // Add all calculations that use the already found calculations as input variable
  const recursiveAddDependingCalculations = (calculation: CalculationModel) => {
    calculationAttributes.forEach((calcOta) => {
      if (calcOta.calculation && !calculationSet.has(calcOta.calculation)) {
        const isDependent = calcOta.calculation.objectTypeAttributePaths.some((path) => {
          return path.belongsTo('dataAttribute').id() === calculation.objectTypeAttribute.id;
        });

        if (isDependent) {
          calculationSet.add(calcOta.calculation);
          recursiveAddDependingCalculations(calcOta.calculation);
        }
      }
    });
  };

  [...calculationSet].forEach(recursiveAddDependingCalculations);
  return [...calculationSet];
}

export function generateCalculationPayload(calculations: Array<CalculationModel>, object: ObjectModel) {
  const dataHash: Dict = {};
  calculations.forEach((calculation) => {
    calculation.objectTypeAttributePaths.forEach((path) => {
      if (path.relationshipReferences?.length) {
        const ref = path.relationshipReferences[0];
        if (!dataHash[ref.attribute_id]) {
          const attribute = object.objectType.objectTypeAttributes.find((ota) => ota.id === ref.attribute_id);
          if (attribute) {
            dataHash[ref.attribute_id] = object.getRelationshipObjects(attribute).map((object) => object.id).join(',');
          }
        }
      } else if (path.belongsTo('dataAttribute').value() && path.dataAttribute?.key) {
        dataHash[path.dataAttribute.id] = object.values[path.dataAttribute.key];
      }
    });
  });

  return {
    calculation_id: calculations.map((calc) => calc.id).join(','),
    team_id: object.team?.id,
    values: dataHash,
    object_id: object.id
  };
}

export async function previewCalculate(object: ObjectModel, payload: Partial<ReturnType<typeof generateCalculationPayload>>) {
  try {
    const response = await object.store.adapterFor('object-type').previewCalculate(object.objectType, payload);
    if (response.attributes && object.changedAttributes().values) {
      Object.keys(response.attributes).forEach((key) => {
        object.updateAttribute(key, response.attributes[key]);
      });
    }
  } catch (error) {
    // do something here???
    // eslint-disable-next-line no-console
    console.error(error);
  }
}
