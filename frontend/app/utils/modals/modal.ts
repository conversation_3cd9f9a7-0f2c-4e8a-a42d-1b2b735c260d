export interface ModalArgs {
  context?: Dict;
  callbacks?: Record<string, (...args: Array<unknown>) => unknown>;
}

export default abstract class Modal<Args extends ModalArgs = ModalArgs> {
  declare static type: string;
  declare context: Args['context'];
  declare callbacks: Record<string, Array<(...args: Array<unknown>) => unknown>>;

  constructor(args: Args) {
    this.context = args.context;
    this.callbacks = {};
    const callbacks = args.callbacks;
    if (callbacks) {
      Object.keys(callbacks).forEach((event) => {
        this.callbacks[event] = [callbacks[event]];
      });
    }
  }

  get type() {
    return (this.constructor as typeof Modal).type;
  }

  get canBeClosed() {
    return true;
  }

  on(event: string, callback: (...args: Array<unknown>) => unknown) {
    let callbacks = this._getCallbacksForEvent(event);
    if (!callbacks) {
      callbacks = [];
      this.callbacks[event] = callbacks;
    }
    callbacks.push(callback);
  }

  trigger(event: string, ...args: Array<unknown>) {
    const callbacks = this._getCallbacksForEvent(event);
    if (!callbacks) return;

    callbacks.forEach((callback) => {
      if (typeof callback === "function") {
        callback(...args);
      }
    });
  }

  _getCallbacksForEvent(event: string) {
    return this.callbacks[event];
  }
}
