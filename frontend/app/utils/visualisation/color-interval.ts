import { tracked } from "@glimmer/tracking";

export interface ColorIntervalHash {
  min?: number;
  max?: number;
  color: string;
}

export class ColorInterval {
  @tracked
  color: string;

  declare min?: number;
  declare max?: number;

  constructor(interval: ColorIntervalHash) {
    this.color = interval.color;
    this.min = interval.min;
    this.max = interval.max;
  }

  serialize(): ColorIntervalHash {
    return {
      color: this.color,
      min: this.min,
      max: this.max
    };
  }
}

export function colorForValue(value: unknown, colorIntervals: Array<ColorInterval>) {
  if (typeof value !== 'number') return;

  return colorIntervals.find((interval) => {
    const min = interval.min ?? Number.NEGATIVE_INFINITY;
    const max = interval.max ?? Number.POSITIVE_INFINITY;
    return value >= min && value <= max;
  })?.color;
}
