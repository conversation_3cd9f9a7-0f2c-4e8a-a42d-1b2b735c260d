import { tracked } from "@glimmer/tracking";
import { Column } from "./column";
import { SeriesOptions, TrackedSeriesOptions } from "./series";
import { ColorInterval } from "./color-interval";

interface SingleTarget {
  type: 'single';
  label: string;
  deviation: 'below' | 'above';
}

interface RangeTarget {
  type: 'range';
  lowerLabel: string;
  upperLabel: string;
  deviation: 'between' | 'outside';
}

interface Layout {
  columns?: Array<{ column_name?: string, object_type_attribute_id?: string }>;
  label?: string;
  series?: Record<string, SeriesOptions>;
  xAxis?: { type?: 'category' | 'time' | 'value', axisLabel?: { rotate: number }, boundaryGap?: boolean };
  yAxis?: Array<Dict>;
  legend?: { type?: 'scroll' | 'plain', show?: boolean, orient?: 'horizontal' | 'vertical' };
  chartOptions?: { min?: number, max?: number, openingAngle?: number, color?: string, hideOther?: boolean, donut?: boolean, label?: boolean, value?: boolean };
  target?: SingleTarget | RangeTarget;
  showBreadcrumb?: boolean;
  timeline?: { color?: string, view_mode?: 'Day' | 'Week' | 'Month', start_attribute_id?: string, end_attribute_id?: string, title_attribute_id?: string };
  colorIntervals?: Array<{ color: string, min?: number, max?: number }>;
}

export class TimelineVisualisationOptions {
  @tracked
  color?: string;

  @tracked
  viewMode?: 'Day' | 'Week' | 'Month';

  @tracked
  startAttributeId?: string;

  @tracked
  endAttributeId?: string;

  @tracked
  titleAttributeId?: string;

  constructor(layout: Layout['timeline']) {
    this.color = layout?.color;
    this.viewMode = layout?.view_mode;
    this.startAttributeId = layout?.start_attribute_id;
    this.endAttributeId = layout?.end_attribute_id;
    this.titleAttributeId = layout?.title_attribute_id;
  }
}

export class VisualisationLayout {
  // private member for storing initial values, used when rolling back
  declare _layout: Layout;

  // --- ObjectTable layout ---
  @tracked
  declare columns: Array<Column>;

  @tracked
  declare showBreadcrumb?: boolean;

  // --- Chart / Table layout ---
  declare label?: Layout['label'];

  @tracked
  declare series?: Record<string, TrackedSeriesOptions>;

  declare xAxis?: Layout['xAxis'];

  declare yAxis?: Layout['yAxis'];

  @tracked
  declare legend?: Layout['legend'];

  @tracked
  declare chartOptions?: Layout['chartOptions'];

  @tracked
  declare target?: Layout['target'];

  declare timeline?: { color?: string, viewMode?: 'Day' | 'Week' | 'Month', startAttributeId?: string, endAttributeId?: string, titleAttributeId?: string };

  @tracked
  declare colorIntervals: Array<ColorInterval>;

  constructor(layout: Layout) {
    this._layout = layout;
    this._deserializeLayout(layout);
  }

  _deserializeLayout(layout: Layout) {
    this.columns = layout.columns?.map((col) => new Column(col)) || [];
    this.label = layout.label;
    this.series = this._trackSeriesOptions(layout.series);
    this.xAxis = layout.xAxis;
    this.yAxis = layout.yAxis;
    this.legend = layout.legend;
    this.chartOptions = layout.chartOptions;
    this.target = layout.target;
    this.showBreadcrumb = layout.showBreadcrumb ?? false;
    this.timeline = new TimelineVisualisationOptions(layout.timeline);
    this.colorIntervals = layout.colorIntervals?.map((color) => new ColorInterval(color)) ?? [];
  }

  _trackSeriesOptions(series?: Record<string, SeriesOptions>): Record<string, TrackedSeriesOptions> {
    if (!series) return {};
    const trackedSeries: Record<string, TrackedSeriesOptions> = {};
    Object.keys(series).forEach((key) => {
      trackedSeries[key] = new TrackedSeriesOptions(series[key]);
    });
    return trackedSeries;
  }

  serializeLayout(): Layout {
    return {
      columns: this.columns.map((col) => {
        if (col.columnName) {
          return { column_name: col.columnName };
        }
        return { object_type_attribute_id: col.objectTypeAttributeId };
      }),
      label: this.label,
      series: this._serializeSeriesOptions(this.series),
      xAxis: this.xAxis,
      yAxis: this.yAxis,
      legend: this.legend,
      chartOptions: this.chartOptions,
      target: this.target,
      showBreadcrumb: this.showBreadcrumb,
      timeline: this.serializedTimeline,
      colorIntervals: this.colorIntervals.length ? this.colorIntervals.map((color) => color.serialize()) : undefined
    };
  }

  _serializeSeriesOptions(series?: Record<string, TrackedSeriesOptions>): Record<string, SeriesOptions> {
    if (!series) return {};
    const SeriesHash: Record<string, SeriesOptions> = {};
    Object.keys(series).forEach((key) => {
      SeriesHash[key] = series[key].toPlainObject();
    });
    return SeriesHash;
  }

  get serializedTimeline() {
    return {
      start_attribute_id: this.timeline?.startAttributeId,
      end_attribute_id: this.timeline?.endAttributeId,
      title_attribute_id: this.timeline?.titleAttributeId,
      color: this.timeline?.color,
      view_mode: this.timeline?.viewMode
    };
  }

  rollback() {
    this._deserializeLayout(this._layout);
  }

  clone() {
    return new VisualisationLayout(this.serializeLayout());
  }
}
