import { get } from "@ember/object";
import { cached } from "@glimmer/tracking";
import { GaugeSeriesOption } from "echarts/types/dist/echarts";
import { ChartOptions } from "./chart-options";
import { TrackedSeriesOptions } from "../series";
import { formatValue, formatTarget } from "frontend/utils/chart/format-value";
import { colorForValue } from "../color-interval";

export const DEFAULT_GAGE_VALUES = {
  MIN: 0,
  MAX: 100,
  OPEN_ANGLE: 90,
  COLOR: '#03455A'
};

export class GageChartOptions extends ChartOptions {
  @cached
  get valueSeries() {
    const series = this.options.series as Record<string, TrackedSeriesOptions>;
    const dimensionKey = Object.keys(series)[0];

    return { key: dimensionKey, ...series[dimensionKey].toPlainObject() };
  }

  get roundedValue() {
    let value = get(this.data.values[0], this.valueSeries.key) as number;
    if (typeof value === 'string') value = parseFloat(value);
    if (typeof value !== 'number') return undefined;

    return formatValue(value);
  }

  get min() {
    return this.options.chartOptions?.min ?? DEFAULT_GAGE_VALUES.MIN;
  }

  get max() {
    return this.options.chartOptions?.max ?? DEFAULT_GAGE_VALUES.MAX;
  }

  get unit() {
    return this.valueSeries.unit ?? "";
  }

  get startAngle() {
    if (this.options.chartOptions?.openingAngle) {
      return 270 - this.options.chartOptions.openingAngle / 2;
    }
    return 225;
  }

  get endAngle() {
    if (this.options.chartOptions?.openingAngle) {
      return 270 + this.options.chartOptions.openingAngle / 2;
    }
    return 315;
  }

  get color() {
    return (colorForValue(this.roundedValue, this.options.colorIntervals) ?? this.options.chartOptions?.color) || DEFAULT_GAGE_VALUES.COLOR; // Must define color to avoid pointer flickering on hover
  }

  get seriesOptions(): Array<GaugeSeriesOption> {
    return [
      {
        type: 'gauge',
        min: this.min,
        max: this.max,
        splitNumber: 1,
        startAngle: this.startAngle,
        endAngle: this.endAngle,
        progress: {
          show: true
        },
        itemStyle: {
          color: this.color
        },
        tooltip: {
          formatter: this.roundedValue + this.unit
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}' + this.unit,
          fontSize: 30,
          color: '#212529',
          fontFamily: 'Poppins, sans-serif',
          fontWeight: 500,
          offsetCenter: [0, 0]
        },
        axisTick: {
          show: false
        },
        title: {
          fontFamily: 'Poppins, sans-serif',
          fontWeight: 300,
          fontSize: '1rem'
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: this.color
          }
        },
        axisLabel: {
          show: true
        },
        splitLine: {
          lineStyle: {
            width: 1
          }
        },
        data: [
          {
            value: this.roundedValue,
            name: this.targetText
          }
        ]
      }
    ];
  }

  get targetText() {
    return formatTarget(this.options.target, this.data.values[0], this.intl);
  }
}
