import { ChartOptions } from './chart-options';
import { XAXisComponentOption, YAXisComponentOption, DatasetComponentOption, LineSeriesOption, BarSeriesOption } from 'echarts';
import { formatValue } from 'frontend/utils/chart/format-value';

const DEFAULT_SERIES_OPTIONS: Record<string, LineSeriesOption | BarSeriesOption> = {
  line: {
    type: 'line'
  },
  bar: {
    type: 'bar'
  },
  area: {
    type: 'line',
    stack: 'Total',
    areaStyle: {},
    emphasis: { focus: 'series' }
  }
};

export class LineChartOptions extends ChartOptions {
  buildOptions() {
    const options = super.buildOptions();
    options.xAxis = this.xAxisOptions;
    options.yAxis = this.yAxisOptions;
    options.dataset = this.mappedData;
    return options;
  }

  get xAxisOptions(): XAXisComponentOption {
    const labelType = this._echartsDimensionType(this.labelColumn.data_type);
    let xOptions: XAXisComponentOption;
    if (labelType === 'ordinal') {
      xOptions = { type: 'category', boundaryGap: true };
    } else if (labelType === 'time') {
      xOptions = { type: 'time' };
    } else {
      xOptions = { type: 'value' };
    }

    return { ...xOptions, ...this.options.xAxis };
  }

  get yAxisOptions(): Array<YAXisComponentOption> {
    if (!this.options.yAxis) return [{ type: 'value' }];

    // alignTicks true to make multiple yAxes more readable
    return this.options.yAxis.map((ax) => { return { ...ax, alignTicks: true }; });
  }

  get tooltipOptions() {
    const options = super.tooltipOptions;
    options.trigger = 'axis';
    return options;
  }

  get mappedData(): DatasetComponentOption {
    return {
      dimensions: this.data.dimensions.map((d) => { return { name: d.key, type: this._echartsDimensionType(d.data_type) }; }),
      source: this.data.values
    };
  }

  get targetLabels() {
    const labels: Array<string> = [];
    const series = this.options.series ?? {};
    Object.keys(series).forEach((serieKey) => {
      const serie = series[serieKey];
      if (serie.target?.type === 'single') {
        labels.push(serie.target.label);
      } else if (serie.target?.type === 'range') {
        labels.push(serie.target.lowerLabel);
        labels.push(serie.target.upperLabel);
      }
    });

    return labels;
  }

  get seriesOptions(): Array<LineSeriesOption | BarSeriesOption> {
    const targetLabels = this.targetLabels;
    return this.dataColumns.filter((col) => {
      return !targetLabels.includes(col.key);
    }).map((dimension, i) => {
      const seriesOptions = this.options.series?.[dimension.key];
      const typeOptions = DEFAULT_SERIES_OPTIONS[seriesOptions?.type || 'line'];

      const options = {
        ...typeOptions,
        name: seriesOptions?.name || dimension.name,
        yAxisIndex: i % this.yAxisOptions.length,
        encode: { x: this.labelColumn.key, y: dimension.key }
      };
      if (seriesOptions?.stack) options.stack = seriesOptions.stack;
      if (seriesOptions?.color) options.color = seriesOptions.color;

      if (seriesOptions?.label) {
        options.label = {
          show: seriesOptions?.label.show,
          position: seriesOptions?.label?.position ?? 'top' as any,
          // This will unconditionally hide labels for the value 0. This might not be desired in every scenario and is a temporary solution
          formatter: (param) => {
            const dimensionNamesIndex = param.encode?.y[0] as keyof typeof param.dimensionNames;
            const dimensionName = param.dimensionNames?.[dimensionNamesIndex] as keyof typeof param.value;
            if (!param.value) return '';

            let value = (param.value as any)[dimensionName];

            value = formatValue(value);

            return value === 0 ? '' : value;
          }
        };
      }

      if (seriesOptions?.target?.type === 'single') {
        const key = seriesOptions.target.label;
        const value = (this.data.values[0] as any)?.[key];
        if (value != undefined) {
          options.markLine = {
            symbol: 'none',
            data: [{
              yAxis: value,
              label: { position: 'insideEndTop', show: false },
              emphasis: { label: { show: true } }
            }]
          };
        }
      } else if (seriesOptions?.target?.type === 'range') {
        const key1 = seriesOptions.target.lowerLabel;
        const key2 = seriesOptions.target.upperLabel;
        const value1 = (this.data.values[0] as any)?.[key1];
        const value2 = (this.data.values[0] as any)?.[key2];
        if (value1 != undefined && value2 != undefined) {
          options.markArea = {
            data: [[{ yAxis: value1 }, { yAxis: value2 }]]
          };
          options.markLine = {
            symbol: 'none',
            data: [{
              yAxis: value1,
              label: { position: 'insideEndTop', show: false },
              emphasis: { label: { show: true } }
            }, {
              yAxis: value2,
              label: { position: 'insideEndTop', show: false },
              emphasis: { label: { show: true } }
            }]
          };
        }
      }

      return options;
    });
  }
}
