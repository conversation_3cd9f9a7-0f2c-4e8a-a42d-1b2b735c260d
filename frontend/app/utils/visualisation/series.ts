import { tracked } from "@glimmer/tracking";
import { type VisualisationLayout } from "./layout";

export enum SERIES_DISPLAY_TYPE {
  BAR = 'bar',
  LINE = 'line',
  AREA = 'area'
}

type SeriesLabel = {
  position?: string;
  show?: boolean;
};

export interface SeriesOptions {
  name?: string;
  type?: SERIES_DISPLAY_TYPE;
  unit?: string;
  stack?: string;
  position?: number;
  label?: SeriesLabel;
  color?: string;
  target?: VisualisationLayout['target'];
}

export class TrackedSeriesOptions {
  @tracked
  name?: string;

  @tracked
  type?: SERIES_DISPLAY_TYPE;

  @tracked
  unit?: string;

  @tracked
  stack?: string;

  @tracked
  position?: number;

  @tracked
  label?: SeriesLabel;

  @tracked
  color?: string;

  @tracked
  target?: VisualisationLayout['target'];

  constructor(options: SeriesOptions) {
    this.name = options.name;
    this.type = options.type;
    this.unit = options.unit;
    this.stack = options.stack;
    this.position = options.position;
    this.label = options.label;
    this.color = options.color;
    this.target = options.target;
  }

  toPlainObject(): SeriesOptions {
    return {
      name: this.name,
      type: this.type,
      unit: this.unit,
      stack: this.stack,
      position: this.position,
      label: this.label,
      color: this.color,
      target: this.target
    };
  }
}
