{"name": "frontend", "version": "0.0.0", "private": true, "description": "Houston frontend app", "repository": "", "author": "", "directories": {"doc": "doc", "test": "tests"}, "scripts": {"build": "ember build --environment=production", "lint": "concurrently \"npm:lint:*(!fix)\" --names \"lint:\"", "lint:fix": "concurrently \"npm:lint:*:fix\" --names \"fix:\"", "lint:hbs": "ember-template-lint .", "lint:hbs:fix": "ember-template-lint . --fix", "lint:js": "eslint . --cache", "lint:js:fix": "eslint . --fix", "lint:scss": "npx stylelint \"app/styles/**/*.scss\"", "lint:scss:fix": "npx stylelint --fix \"app/styles/**/*.scss\"", "start": "ember serve", "test": "concurrently \"npm:lint\" \"npm:test:*\" --names \"lint,test:\"", "test:ember": "ember test", "postinstall": "patch-package"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@ember/optional-features": "^2.2.0", "@ember/render-modifiers": "^2.1.0", "@ember/string": "^3.1.1", "@ember/test-helpers": "^5.2.2", "@embroider/macros": "^1.16.10", "@glimmer/component": "^1.1.2", "@glimmer/tracking": "^1.1.2", "@popperjs/core": "^2.11.8", "@rails/actioncable": "^7.2.201", "@rails/activestorage": "^7.2.201", "@simonwep/pickr": "^1.9.1", "@stylistic/eslint-plugin": "^4.4.0", "@stylistic/stylelint-config": "^2.0.0", "@stylistic/stylelint-plugin": "^3.1.2", "@types/autosize": "^4.0.3", "@types/bootstrap": "^5.2.10", "@types/ember-data": "^4.4.16", "@types/ember-data__adapter": "^4.0.6", "@types/ember-data__model": "^4.0.5", "@types/ember-data__serializer": "^4.0.6", "@types/ember-data__store": "^4.0.7", "@types/ember__application": "^4.0.11", "@types/ember__array": "^4.0.10", "@types/ember__component": "^4.0.22", "@types/ember__controller": "^4.0.12", "@types/ember__debug": "^4.0.8", "@types/ember__engine": "^4.0.11", "@types/ember__error": "^4.0.6", "@types/ember__object": "^4.0.12", "@types/ember__owner": "^4.0.9", "@types/ember__polyfills": "^4.0.6", "@types/ember__routing": "^4.0.22", "@types/ember__runloop": "^4.0.10", "@types/ember__service": "^4.0.9", "@types/ember__string": "^3.0.15", "@types/ember__template": "^4.0.7", "@types/ember__test": "^4.0.6", "@types/ember__utils": "^4.0.7", "@types/frappe-gantt": "^0.9.0", "@types/qunit": "^2.19.12", "@types/rails__activestorage": "^7.0.5", "@types/rsvp": "^4.0.9", "@types/sortablejs": "^1.15.8", "@types/strftime": "^0.9.8", "autosize": "^6.0.1", "bootstrap": "5.3.3", "broccoli-asset-rev": "^3.0.0", "broccoli-funnel": "^3.0.8", "broccoli-merge-trees": "^4.2.0", "concurrently": "^9.1.2", "dompurify": "^3.2.6", "echarts": "^5.6.0", "ember-auto-import": "^2.10.0", "ember-cli": "~5.12.0", "ember-cli-app-version": "^7.0.0", "ember-cli-babel": "^8.2.0", "ember-cli-code-coverage": "^3.1.0", "ember-cli-dependency-checker": "^3.3.3", "ember-cli-htmlbars": "^6.3.0", "ember-cli-inject-live-reload": "^2.0.2", "ember-cli-sass": "^11.0.1", "ember-cli-sri": "^2.1.1", "ember-cli-terser": "^4.0.2", "ember-cli-typescript": "^5.3.0", "ember-click-outside": "^6.1.1", "ember-concurrency": "^4.0.4", "ember-data": "~4.12.8", "ember-fetch": "^8.1.2", "ember-flatpickr": "^4.0.0", "ember-intl": "7.1.8", "ember-load-initializers": "^3.0.1", "ember-maybe-import-regenerator": "^1.0.0", "ember-modifier": "^4.2.2", "ember-page-title": "^9.0.2", "ember-qunit": "^9.0.3", "ember-resize-modifier": "^0.7.1", "ember-resolver": "^13.1.1", "ember-service-worker": "^9.0.1", "ember-service-worker-cache-fallback": "^0.6.2", "ember-source": "~5.12.0", "ember-template-lint": "^7.7.0", "ember-test-selectors": "^7.1.0", "ember-web-app": "^5.0.1", "eslint": "^9.28.0", "eslint-formatter-compact": "^8.40.0", "eslint-plugin-ember": "^12.5.0", "eslint-plugin-n": "^17.19.0", "eslint-plugin-qunit": "^8.1.2", "frappe-gantt": "^1.0.3", "globals": "^16.2.0", "gridstack": "^12.1.2", "html5-qrcode": "^2.2.1", "loader.js": "^4.7.0", "marked": "^15.0.12", "miragejs": "^0.1.48", "nouislider": "^15.8.1", "patch-package": "^8.0.0", "postcss": "^8.5.4", "quill": "^2.0.3", "quill-magic-url": "^4.2.0", "qunit": "^2.24.1", "qunit-dom": "^3.4.0", "reactiveweb": "^1.5.0", "sass": "^1.78.0", "signature_pad": "^5.0.7", "sortablejs": "^1.15.6", "strftime": "^0.10.3", "stylelint": "^16.20.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-scss": "^6.12.0", "timeago.js": "^4.0.2", "tom-select": "^2.4.3", "tracked-built-ins": "^4.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "webpack": "^5.99.9"}, "engines": {"node": ">= 18"}, "ember": {"edition": "octane"}, "resolutions": {"@ember/test-waiters": "^3.1.0", "ember-cli-htmlbars": "^6.3.0"}, "ember-addon": {"paths": ["lib/new-relic-browser-agent"]}}