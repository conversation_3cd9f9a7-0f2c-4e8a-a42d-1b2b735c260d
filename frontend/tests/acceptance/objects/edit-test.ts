import { setupApplicationTest } from 'frontend/tests/helpers';
import { module, test } from 'qunit';
import { Server, testServer } from 'frontend/tests/server/test-server';
import { setupAdminSession } from 'frontend/tests/helpers/session-helper';
import { click, fillIn, findAll, settled, Target, visit } from '@ember/test-helpers';
import { setupIntl, t } from 'ember-intl/test-support';
import { getSelectedOptions, openSelect, removeSelection, selectOption } from 'frontend/tests/helpers/select-helpers';
import { clickable } from 'frontend/tests/helpers/element-helpers';
import { INPUT_TYPE } from 'frontend/models/form-element';
import { VALIDATION_TYPE } from 'frontend/models/object-type-attribute-validation';
import { DATA_TYPE } from 'frontend/models/object-type-attribute';
import { changeViewPointToTeam } from 'frontend/tests/helpers/viewpoint-helpers';
import { resetLoadedState } from 'frontend/utils/default-thumbnails-loader';
import { THUMBNAIL_TYPE } from 'frontend/utils/thumbnail';

module('Acceptance | objects | edit', function(hooks) {
  setupApplicationTest(hooks);
  setupIntl(hooks, 'en');

  let server: Server;

  hooks.beforeEach(() => {
    server = testServer();
    setupAdminSession(server);
  });

  hooks.afterEach(() => {
    server.shutdown();
  });

  module('General', function() {
    test('it shows all content in the master', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij', color: "#c3291c", icon: "fa fa-house" });
      await visit(`/objects/${objectType.id}/edit/general`);

      assert.dom('.tab-pane .icon-card-icon').doesNotExist();
      assert.dom('.tab-pane input.form-control').exists().isEnabled();
      assert.dom('.tab-pane select').exists().isEnabled();
      assert.dom('.tab-pane .color-grid').exists();
      assert.dom('.tab-pane button[aria-label~="color"]').exists();
      assert.dom('.tab-pane .icon-grid').exists();
    });

    test('it shows only relevant content in a variant', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij', color: "#c3291c", icon: "fa fa-house" });
      const team = server.currentUser.teams.models[0];
      server.create('object-type-variant', { objectType: objectType, team: team, id: '1' });
      server.create('object-type-team-relationship', { objectType: objectType, team: team, published: true });
      await visit(`/objects/${objectType.id}/edit/general`);
      await changeViewPointToTeam(team);

      assert.dom('.tab-pane .icon-card-icon').exists();
      assert.dom('.tab-pane input.form-control').doesNotExist();
      assert.dom('.tab-pane .color-grid').doesNotExist();
      assert.dom('.tab-pane button[aria-label~="color"]').doesNotExist();
      assert.dom('.tab-pane .icon-grid').doesNotExist();
    });

    test('it shows an error when you want to save an object type without owners', async function(assert) {
      const toasts = this.owner.lookup('service:toast');

      const objectType = server.create('object-type', { name: 'Boerderij', color: "#c3291c", icon: "fa fa-house" });
      const user1 = server.create('user', { id: '5', name: 'owner 1', ownedObjectTypes: [objectType.id] });
      const user2 = server.create('user', { id: '6', name: 'owner 2', ownedObjectTypes: [objectType.id] });

      await visit(`/objects/${objectType.id}/edit/general`);
      assert.deepEqual(getSelectedOptions(), [user1.id, user2.id]);
      await openSelect();
      removeSelection(user1.id);
      removeSelection(user2.id);
      assert.deepEqual(getSelectedOptions(), []);

      await click('.save-button');

      assert.strictEqual(toasts.toasts.length, 1);
      assert.strictEqual(toasts.toasts[0].text, t('error.empty_object_type_owners_list'));
    });

    test('it shows a warning message when you remove yourself as owner', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij', color: "#c3291c", icon: "fa fa-house" });
      // make currentUser not an admin + make owner of object type
      server.currentUser.update({ admin: false, ownedObjectTypes: [objectType.id] });
      const user1 = server.create('user', { id: '5', name: 'owner 1', ownedObjectTypes: [objectType.id] });

      await visit(`/objects/${objectType.id}/edit/general`);
      assert.deepEqual(getSelectedOptions(), [server.currentUser.id, user1.id]);
      await openSelect();
      // remove yourself as owner
      removeSelection(server.currentUser.id);
      assert.deepEqual(getSelectedOptions(), [user1.id]);

      await click('.save-button');
      assert.dom('.modal-dialog').includesText(t("dialog.confirm_removal_current_user_as_object_type_owner"));

      // cancel removing yourself put you back in the owners list
      await click(clickable(t('cancel')));
      assert.deepEqual(getSelectedOptions(), [user1.id, server.currentUser.id]);

      removeSelection(server.currentUser.id);
      await click('.save-button');
      await click(clickable(t('ok')));

      // transitions aways after save
      assert.dom('.tab-pane').doesNotExist();
    });

    test('it renders the description inputs on master', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      await visit(`/objects/${objectType.id}/edit/general`);
      assert.dom(".quill-editor").exists();
    });

    test('it disables the description inputs on variant', async function(assert) {
      const team1 = server.create('team', { name: "team1" });
      const team2 = server.create('team', { parent: team1, name: "team2", ancestorIds: [team1.id] });
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const user = server.create('user', { admin: true, teams: [team1, team2], ownedObjectTypes: [objectType.id] });
      server.signInUser(user);

      server.create('object-type-variant', { objectType: objectType, team: team1, id: '1' });
      server.create('object-type-team-relationship', { objectType: objectType, team: team1, published: true });
      server.create('object-type-team-relationship', { objectType: objectType, team: team2, published: true });

      await visit(`/objects/${objectType.id}/edit/general`);
      await changeViewPointToTeam(team2);
      assert.dom(".quill-editor").hasClass("ql-disabled");
    });
  });

  module('FormBuilder', function() {
    test('it renders the formbuilder', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });

      await visit(`/objects/${objectType.id}/edit/form`);
      assert.dom('.form-builder').exists();
      assert.dom('.form-builder .header').containsText(objectType.name);
    });

    test('it can create a formTab', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click(clickable(`${t('tab')}`, { ancestor: '.form-builder .left .tabs ul' }));

      const newTabSelector = '.tabs ul > div > li';
      assert.dom(newTabSelector).exists({ count: 1 });
      assert.dom(newTabSelector).containsText(t("new_tab"));
      assert.dom('.form-tab').exists();
    });

    test('it can create a formElement and can change the type before saving', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      server.create('form-tab', { name: 'kippetjes', objectType: objectType });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click('.btn-outline-primary');
      const type = findAll('.new-form-element-menu button').find((type) => type.textContent?.includes(t('input_type.number')));
      assert.dom(type).exists();

      await click(type as Target);
      assert.dom('.form-element').exists({ count: 1 });

      const inputTypeField = findAll(".right .col-form-label").find((input) => input.textContent?.includes(t('input_type.type')));
      assert.dom(inputTypeField).exists();

      await openSelect(`.${inputTypeField?.className} + div > select`);

      selectOption("short_text");
      assert.dom(`.${inputTypeField?.className} + div .item`).hasText(t('input_type.short_text'));
    });

    test('it can create a formElement and cannot change its type after saving', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const formTab = server.create('form-tab', { name: 'kippetjes', objectType: objectType });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click('.btn-outline-primary');
      const number = findAll('.new-form-element-menu button').find((type) => type.textContent?.includes(t('input_type.number')));
      assert.dom(number).exists();

      await click(number as Target);
      assert.dom('.form-element').exists({ count: 1 });

      let inputTypeField = findAll(".right .col-form-label").find((input) => input.textContent?.includes(t('input_type.type')));
      assert.dom(inputTypeField).exists();

      await fillIn('#name', 'name');
      await click(clickable(`${t("create")}`, { ancestor: '.right .footer', tag: 'span' }));

      assert.strictEqual(server.db.formElements.length, 1);
      assert.strictEqual(server.db.formElements[0].formTabId, formTab.id);
      assert.strictEqual(server.db.formElements[0].inputType, 'TextField');
      assert.strictEqual(server.db.objectTypeAttributes.length, 1);
      assert.strictEqual(server.db.objectTypeAttributes[0].dataType, 'Number');

      inputTypeField = findAll(".right .col-form-label").find((input) => input.textContent?.includes(t('input_type.type')));
      assert.strictEqual(inputTypeField, undefined); // inputType field is not shown when it is not new.
    });

    test('it can create a formElement and assign a calculation to it', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const formTab = server.create('form-tab', { name: 'kippetjes', objectType: objectType });
      const objectTypeAttribute = server.create('object-type-attribute', { name: 'Nummertje', key: 'nummer', dataType: 'Number', objectType: objectType });
      server.create('form-element', { objectTypeAttributeId: objectTypeAttribute.id, formTab: formTab });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click(clickable("Nummertje", { tag: "span" }));
      await click(".list-group-item .fa-plus");

      const type = findAll('.new-form-element-menu button').find((type) => type.textContent?.includes(t('input_type.number')));
      assert.dom(type).exists();

      await click(type as Target);
      await click(clickable(t("calculation.calculated"), { tag: 'label' }));
      await click(clickable(t("add_calculation")));
      await fillIn(".modal-body input", "A+1");
      await click(clickable(t("calculation.variable")));

      await openSelect(".modal-body select");

      selectOption(objectTypeAttribute.id, ".modal-body select");

      await click(clickable(t("confirm")));

      assert.dom(".calculation-thumbnail").exists();
      assert.dom(".calculation-thumbnail").containsText("= A+1");
      assert.dom(".calculation-thumbnail").containsText("Where: A Nummertje");
    });

    test('it can set the mapping of a path on a calculation', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const formTab = server.create('form-tab', { name: 'kippetjes', objectType: objectType });
      const allowedValues = { type: "Select", values: [{ value: 'key1' }, { value: 'key2' }] };
      const objectTypeAttribute = server.create('object-type-attribute', { name: 'Nummertje', key: 'nummer', dataType: 'Number', objectType: objectType, allowedValues: allowedValues });
      server.create('form-element', { objectTypeAttributeId: objectTypeAttribute.id, formTab: formTab, inputType: 'Select' });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click(clickable("Nummertje", { tag: "span" }));
      await click(".list-group-item .fa-plus");

      const type = findAll('.new-form-element-menu button').find((type) => type.textContent?.includes(t('input_type.number')));
      assert.dom(type).exists();

      await click(type as Target);
      await click(clickable(t("calculation.calculated"), { tag: 'label' }));
      await click(clickable(t("add_calculation")));
      await fillIn(".modal-body input", "A+1");
      await click(clickable(t("calculation.variable")));

      await openSelect(".modal-body select");

      selectOption(objectTypeAttribute.id, ".modal-body select");

      await fillIn(".modal-body input[type=number]", "10");
      await click(clickable(t("confirm")));

      assert.dom(".calculation-thumbnail").exists();
      assert.dom(".calculation-thumbnail").containsText("= A+1");
      assert.dom(".calculation-thumbnail").containsText("Where: A Nummertje");
      const store = this.owner.lookup('service:store');

      const calculation = store.peekAll('calculation')[0];
      assert.strictEqual(calculation.objectTypeAttributePaths.length, 1);
      assert.deepEqual(calculation.objectTypeAttributePaths[0].mapping, { key1: 10, key2: 1 });
    });

    test('when calulation rules are changed, it will ask the user if he wants to update existing values', async function(assert: Assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const formTab = server.create('form-tab', { name: 'kippetjes', objectType });
      const objectTypeAttribute = server.create('object-type-attribute', { objectType, name: 'Nummertje', key: 'nummer', dataType: 'Number', calculated: true });
      server.create('form-element', { formTab, objectTypeAttributeId: objectTypeAttribute.id });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click(clickable('Nummertje', { tag: 'span' }));
      await click(clickable(t("add_calculation")));
      await fillIn('.modal-body input', '1');
      await click(clickable(t("confirm")));
      await click(clickable(t('save'), { tag: 'span', ancestor: '.save-button' }));

      assert.dom('.modal-content').containsHtml(t('dialog.confirm_recalculation'));
    });

    test('it can show the properties of a formelement by clicking it on the left panel', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab = server.create('form-tab', { objectType: objectType, name: 'formTab', position: 0 });
      const ota = server.create('object-type-attribute', { objectType: objectType, name: 'kieken' });
      server.create('form-element', { formTab: tab, objectTypeAttribute: ota });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click('.form-element');

      const nameField = findAll(".right .col-form-label").find((input) => input.textContent?.includes(t('field_name')));
      assert.dom(nameField).exists();
      const nameValue = document.querySelector(".input-group input");
      assert.dom(nameValue).hasValue(ota.name);
    });

    test('it can show the properties of a formTab by clicking the cog on the left panel', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab = server.create('form-tab', { objectType: objectType, name: 'formTab', position: 0 });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click('.form-builder .left .tabs ul .nav-item .fa-gear');

      const nameField = findAll(".right .col-form-label").find((input) => input.textContent?.includes(t('tab_name')));
      assert.dom(nameField).exists();
      const nameValue = document.querySelector(".input-group input");
      assert.dom(nameValue).hasValue(tab.name);
    });

    test('it can show the properties of a section by clicking the section name in the left panel preview', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab = server.create('form-tab', { objectType: objectType, name: 'formTab', position: 0 });
      const section = server.create('form-tab', { objectType: objectType, name: 'section', parent: tab, position: 0 });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click(clickable(`${section.name}`, { ancestor: '.form-tab .form-tab div', tag: 'label' }));

      const nameField = findAll(".right .col-form-label").find((input) => input.textContent?.includes(t('section_name')));
      assert.dom(nameField).exists();
      const nameValue = document.querySelector(".input-group input");
      assert.dom(nameValue).hasValue(section.name);
    });

    test('it disables the save button if the formElement has an objectTypeAttribute without a name', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab = server.create('form-tab', { objectType: objectType, name: 'formTab', position: 0 });
      const ota = server.create('object-type-attribute', { objectType: objectType });
      server.create('form-element', { formTab: tab, objectTypeAttribute: ota, inputType: INPUT_TYPE.TEXTFIELD });

      await visit(`/objects/${objectType.id}/edit/form`);
      await click('.form-element');

      assert.dom('.right .footer button').exists({ count: 2 });
      const buttons = document.querySelectorAll('.right .footer button');
      assert.dom(buttons[0]).hasAttribute('disabled');
      assert.dom(buttons[1]).hasAttribute('disabled');
      await fillIn(".right .form-control", "Naampje");
      assert.dom(buttons[0]).doesNotHaveAttribute('disabled');
      assert.dom(buttons[1]).doesNotHaveAttribute('disabled');
    });

    test('it hides hidden form elements in a variant when toggling the eye icon', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const team = server.currentUser.teams.models[0];
      server.create('object-type-variant', { objectType: objectType, team: team, id: '1' });
      const tab = server.create('form-tab', { objectType: objectType, name: 'formTab', position: 0 });
      const ota = server.create('object-type-attribute', { objectType: objectType, hidden: { '1': true } });
      server.create('form-element', { formTab: tab, objectTypeAttribute: ota, inputType: INPUT_TYPE.TEXTFIELD });
      server.create('object-type-team-relationship', { objectType: objectType, team: team });

      await visit(`/objects/${objectType.id}/edit/form`);
      await changeViewPointToTeam(team);

      assert.dom('.fa-lg.fa-eye').exists();
      assert.dom('.form-element').exists({ count: 1 });

      await click('.fa-lg.fa-eye');
      assert.dom('.form-element').doesNotExist();
    });

    test('it can move a formelement to another tab and changes positions correctly', async function(this, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab1 = server.create('form-tab', { objectType: objectType, name: 'tab1', position: 0, id: '0' });
      const tab2 = server.create('form-tab', { objectType: objectType, name: 'tab2', position: 1, id: '1' });

      const ota1 = server.create('object-type-attribute', { objectType: objectType });
      const formElement1 = server.create('form-element', { formTab: tab1, objectTypeAttribute: ota1, inputType: INPUT_TYPE.TEXTFIELD, position: 0 });

      const ota2 = server.create('object-type-attribute', { objectType: objectType });
      const formElement2 = server.create('form-element', { formTab: tab1, objectTypeAttribute: ota2, inputType: INPUT_TYPE.TEXTFIELD, position: 1 });

      await visit(`/objects/${objectType.id}/edit/form`);
      // make a new formelement between the 2 existing ones
      await click('.form-item');
      await click('.btn-group .fa-plus');
      const number = findAll('.new-form-element-menu button').find((type) => type.textContent?.includes(t('input_type.number')));
      assert.dom(number).exists();
      await click(number as Target);
      assert.dom('.form-element').exists({ count: 3 });
      assert.strictEqual(store.peekRecord('form-element', formElement2.id)?.position, 2);

      await click('.form-item .fa-ellipsis');
      await click('.form-item .fa-up-down-left-right');

      await openSelect('.modal-body select');
      selectOption(tab2.id, '.modal-body select');

      await click('.modal .save-button');
      const newFormElement = store.peekAll('form-element')[2];
      assert.strictEqual(newFormElement.formTab.id, tab2.id);
      assert.strictEqual(newFormElement.position, 0);

      assert.strictEqual(store.peekRecord('form-element', formElement1.id)?.position, 0);
      assert.strictEqual(store.peekRecord('form-element', formElement1.id)?.formTab.id, tab1.id);

      // check if formelement after the moved formelement has decremented its position
      assert.strictEqual(store.peekRecord('form-element', formElement2.id)?.formTab.id, tab1.id);
      assert.strictEqual(store.peekRecord('form-element', formElement2.id)?.position, 1);
    });

    test('it can move a section to another tab and changes positions correctly', async function(this, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab1 = server.create('form-tab', { objectType: objectType, name: 'tab1', position: 0, id: '0' });
      const tab2 = server.create('form-tab', { objectType: objectType, name: 'tab2', position: 1, id: '1' });

      const ota1 = server.create('object-type-attribute', { objectType: objectType });
      const formElement1 = server.create('form-element', { formTab: tab1, objectTypeAttribute: ota1, inputType: INPUT_TYPE.TEXTFIELD, position: 0 });

      const ota2 = server.create('object-type-attribute', { objectType: objectType });
      const formElement2 = server.create('form-element', { formTab: tab1, objectTypeAttribute: ota2, inputType: INPUT_TYPE.TEXTFIELD, position: 1 });

      const ota3 = server.create('object-type-attribute', { objectType: objectType });
      const formElement3 = server.create('form-element', { formTab: tab2, objectTypeAttribute: ota3, inputType: INPUT_TYPE.TEXTFIELD, position: 0 });

      await visit(`/objects/${objectType.id}/edit/form`);
      // make a new section between the 2 existing items
      await click('.form-item');
      await click('.btn-group .fa-plus');
      const section = findAll('.new-form-element-menu button').find((type) => type.textContent?.includes(t('section')));
      assert.dom(section).exists();
      await click(section as Target);
      assert.dom('.form-item').exists({ count: 3 });
      assert.strictEqual(store.peekRecord('form-element', formElement2.id)?.position, 2);

      await click('.form-item .fa-ellipsis');
      await click('.form-item .fa-up-down-left-right');

      await openSelect('.modal-body select');
      selectOption(tab2.id, '.modal-body select');

      await click('.modal .save-button');
      const newSection = store.peekAll('form-tab').find((tab) => tab.parent)!;
      assert.strictEqual(newSection.parent?.id, tab2.id);
      assert.strictEqual(newSection.position, formElement3.position + 1);

      assert.strictEqual(store.peekRecord('form-element', formElement1.id)?.position, 0);
      assert.strictEqual(store.peekRecord('form-element', formElement1.id)?.formTab.id, tab1.id);

      // check if formelement after the moved formitem has decremented its position
      assert.strictEqual(store.peekRecord('form-element', formElement2.id)?.formTab.id, tab1.id);
      assert.strictEqual(store.peekRecord('form-element', formElement2.id)?.position, 1);
    });
  });

  module('Validation', function() {
    test('it shows only the formTabs that have validations', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const tab = server.create('form-tab', { objectType: objectType, name: 'formTab', position: 0 });
      const otherTab = server.create('form-tab', { objectType: objectType, name: 'otherTab', position: 1 });
      const ota = server.create('object-type-attribute', { objectType: objectType, name: 'test' });
      server.create('form-element', { formTab: tab, objectTypeAttribute: ota, inputType: INPUT_TYPE.TEXTFIELD });
      server.create('object-type-attribute-validation', { objectTypeAttribute: ota, validationType: VALIDATION_TYPE.EQUALS, value: 'Yay' });

      await visit(`/objects/${objectType.id}/edit/validations`);
      assert.dom('.tab-pane').doesNotContainText(otherTab.name);
      assert.dom('.tab-pane').containsText(tab.name);
    });
  });

  module('Automation', function() {
    test('it can create and select an automation and track its dirtyness', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      await visit(`/objects/${objectType.id}/edit/automation`);
      await click(clickable(t("automation.automation")));

      assert.dom('.automation-settings').exists('Automation is created and selected');
      await click('.automation-settings .fa-arrow-left');
      assert.dom('.modal-dialog').exists('Dialog modal shows when leaving the newly, unsaved automation');
    });

    test('the active toggle sends a request to the backend', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const automation = server.create('automation', { objectType: objectType, active: false });
      await visit(`/objects/${objectType.id}/edit/automation`);

      assert.dom('.card .user-select-none').containsText(t('not_active'));
      await click('.card i[role="checkbox"]');

      assert.dom('.card .user-select-none').containsText(t('active'));
      assert.true(automation.active);
    });

    test('it can save the selected action or automation', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const automation = server.create('automation', { name: 'yeet', objectType: objectType });

      await visit(`/objects/${objectType.id}/edit/automation`);
      await click(clickable(automation.name, { tag: 'label' }));
      await click(clickable(t('automation.action')));
      await click('.fa-envelope');

      await click('.automation-settings .fa-arrow-left');
      assert.dom('.modal-dialog').exists('Dialog modal shows when automation is not saved');

      await click(clickable(t('cancel')));
      await click('.save-button');

      await click('.automation-settings .fa-arrow-left');
      assert.dom('.modal-dialog').doesNotExist('Dialog is not shown which means the automation is saved and closed');
    });

    test('it shows only automations of the current variant', async function(assert) {
      const team1 = server.create('team', { name: 'A team' });
      const team2 = server.create('team', { name: 'Inter Miami' });

      const objectType = server.create('object-type', { name: 'Boerderij', teams: [team1, team2] });
      server.create('object-type-team-relationship', { objectType: objectType, team: team1 });
      const objectTypeVariant1 = server.create('object-type-variant', { objectType: objectType, team: team1 });
      const objectTypeVariant2 = server.create('object-type-variant', { objectType: objectType, team: team2 });

      const automation = server.create('automation', { name: 'Foo', objectType: objectType, objectTypeVariantId: objectTypeVariant1.id });
      server.create('automation', { name: 'Bar', objectType: objectType, objectTypeVariantId: objectTypeVariant2.id });
      server.create('automation', { name: 'FooBar', objectType: objectType });

      await visit(`/objects/${objectType.id}/edit/automation`);

      await changeViewPointToTeam(team1);
      assert.dom('.card').exists({ count: 2 });
      assert.dom('.card:first-of-type .automation-thumbnail-togglebox').exists();
      assert.dom('.card:first-of-type').containsText(automation.name);
      assert.dom('.card .automation-thumbnail-togglebox').exists({ count: 1 });
    });

    test('it creates an automation with the correct objectTypeVariantId', async function(assert) {
      const team1 = server.create('team', { name: 'A team' });
      const team2 = server.create('team', { name: 'Inter Miami' });

      const objectType = server.create('object-type', { name: 'Boerderij', teams: [team1, team2] });
      server.create('object-type-team-relationship', { objectType: objectType, team: team1 });
      const variant = server.create('object-type-variant', { objectType: objectType, team: team1, id: "73" });
      server.create('object-type-variant', { objectType: objectType, team: team2 });

      await visit(`/objects/${objectType.id}/edit/automation`);

      await changeViewPointToTeam(team1);
      assert.dom('.automation-settings').doesNotExist();
      await click(clickable(t('automation.automation')));

      const store = this.owner.lookup('service:store');

      const automations = store.peekAll("automation");
      assert.deepEqual(automations.length, 1);
      assert.deepEqual(automations[0].objectTypeVariantId, variant.id);
      assert.dom('.automation-settings').exists();
    });

    test('it allows the user to configure users as recipients', async function(assert) {
      const objectType = server.create('object-type');
      const user = server.create('user', { name: 'Lionel Messi' });

      await visit(`/objects/${objectType.id}/edit/automation`);

      await click(clickable(t('automation.automation')));
      await click('.fa-envelope');
      await click(clickable(t('automation.recipient')));

      await openSelect('.modal-body select:first-of-type');
      selectOption('user', '.modal-body select:first-of-type');

      await openSelect('[data-select="recipient-user-select"] select');
      selectOption(user.id, '[data-select="recipient-user-select"] select');

      await click(clickable(t("confirm")));

      const store = this.owner.lookup('service:store');
      const automationMailAction = store.peekAll("automation-mail-action")[0];
      const frontendUser = store.peekRecord('user', user.id);
      assert.deepEqual(automationMailAction.selectedUsers, [frontendUser]);
    });

    test('it allows the user to configure roles as recipients', async function(assert) {
      const objectType = server.create('object-type');
      const role = server.create('object-type-role', { name: 'Rollebolle', objectType: objectType });

      await visit(`/objects/${objectType.id}/edit/automation`);

      await click(clickable(t('automation.automation')));
      await click('.fa-envelope');
      await click(clickable(t('automation.recipient')));

      await openSelect('.modal-body select:first-of-type');
      selectOption('role', '.modal-body select:first-of-type');
      await settled();

      await openSelect('[data-select="recipient-role-select"] select');
      selectOption(role.id, '[data-select="recipient-role-select"] select');

      await click(clickable(t("confirm")));

      const store = this.owner.lookup('service:store');
      const automationMailAction = store.peekAll("automation-mail-action")[0];
      const frontendRole = store.peekRecord('object-type-role', role.id);
      assert.deepEqual(automationMailAction.selectedRoles, [frontendRole]);
    });

    test('it allows the user to configure recipients through fields', async function(assert) {
      const objectType = server.create('object-type');
      const ota = server.create('object-type-attribute', { objectType: objectType, dataType: 'String' });
      const tab = server.create('form-tab', { objectType: objectType });
      server.create('form-element', { objectTypeAttribute: ota, formTab: tab, inputType: 'TextField' });
      await visit(`/objects/${objectType.id}/edit/automation`);

      await click(clickable(t('automation.automation')));
      await click('.fa-envelope');
      await click(clickable(t('automation.recipient')));

      await openSelect('.modal-body select:first-of-type');
      selectOption('field', '.modal-body select:first-of-type');
      await settled();

      await openSelect('[data-select="recipient-attribute-select"] select');
      selectOption(ota.id, '[data-select="recipient-attribute-select"] select');
      await settled();

      await click(clickable(t("confirm")));

      const store = this.owner.lookup('service:store');
      const automationMailAction = store.peekAll("automation-mail-action")[0];
      const mailActionAddressAttribute = automationMailAction.automationMailActionAddressAttributes[0];
      assert.deepEqual(mailActionAddressAttribute.objectTypeAttribute?.id, ota.id);
      assert.strictEqual(mailActionAddressAttribute.relationshipObjectTypeAttribute, null);
    });

    test('it allows the user to configure recipients through nested user associations', async function(assert) {
      const relatedObjectType = server.create('object-type');
      const ota = server.create('object-type-attribute', {
        dataType: DATA_TYPE.RELATIONSHIP,
        key: 'support',
        name: 'Contact Person',
        objectType: relatedObjectType,
        relationshipKind: 'single',
        targetClass: 'User'
      });

      const objectType = server.create('object-type');
      const relatedOta = server.create('object-type-attribute', {
        dataType: DATA_TYPE.RELATIONSHIP,
        key: 'supplier',
        name: 'Supplier',
        objectType,
        relationshipKind: 'single',
        targetClass: 'Object',
        targetObjectTypeId: relatedObjectType.id
      });

      const relatedTab = server.create('form-tab', { objectType: relatedObjectType });
      server.create('form-element', { objectTypeAttribute: ota, formTab: relatedTab, inputType: 'TextField' });

      const formTab = server.create('form-tab', { objectType });
      server.create('form-element', { objectTypeAttribute: relatedOta, formTab, inputType: 'Select' });

      await visit(`/objects/${objectType.id}/edit/automation`);

      await click(clickable(t('automation.automation')));
      await click('.fa-envelope');
      await click(clickable(t('automation.recipient')));

      await openSelect('.modal-body select:first-of-type');

      selectOption('selectedRelatedUserField', '.modal-body select:first-of-type');
      await settled();

      await openSelect('[data-select="selected-related-user-field-recipient-attribute-select"] select');
      selectOption(relatedOta.id, '[data-select="selected-related-user-field-recipient-attribute-select"] select');

      await settled();
      await openSelect('[data-select="selected-related-user-field-recipient-related-attribute-select"] select');
      selectOption(ota.id, '[data-select="selected-related-user-field-recipient-related-attribute-select"] select');

      await settled();

      await click(clickable(t("confirm")));

      const store = this.owner.lookup('service:store');
      const automationMailAction = store.peekAll("automation-mail-action")[0];
      const mailActionAddressAttribute = automationMailAction.automationMailActionAddressAttributes[0];
      assert.deepEqual(mailActionAddressAttribute.objectTypeAttribute?.id, ota.id);
      assert.strictEqual(mailActionAddressAttribute.relationshipObjectTypeAttribute?.id, relatedOta.id);
    });

    test('it allows the user to configure recipients through fields from a related object', async function(assert) {
      const relatedObjectType = server.create('object-type');
      const relatedOta = server.create('object-type-attribute', { key: 'ota', objectType: relatedObjectType, dataType: 'String', name: 'Name' });
      const relatedTab = server.create('form-tab', { objectType: relatedObjectType });
      server.create('form-element', { objectTypeAttribute: relatedOta, formTab: relatedTab, inputType: 'TextField' });

      const objectType = server.create('object-type');
      const ota = server.create('object-type-attribute', { key: 'relationship', objectType: objectType, dataType: 'Relationship', targetClass: 'Object', targetObjectTypeId: relatedObjectType.id, name: 'Relationship field', relationshipKind: 'single', inverseRelationshipKind: 'multiple' });
      const tab = server.create('form-tab', { objectType: objectType });
      server.create('form-element', { objectTypeAttribute: ota, formTab: tab, inputType: 'Select' });

      await visit(`/objects/${objectType.id}/edit/automation`);

      await click(clickable(t('automation.automation')));
      await click('.fa-envelope');
      await click(clickable(t('automation.recipient')));

      await openSelect('.modal-body select:first-of-type');

      selectOption('field', '.modal-body select:first-of-type');
      await settled();

      await openSelect('[data-select="recipient-attribute-select"] select');

      selectOption(ota.id, '[data-select="recipient-attribute-select"] select');

      await settled();
      await openSelect('[data-select="recipient-related-attribute-select"] select');
      selectOption(relatedOta.id, '[data-select="recipient-related-attribute-select"] select');

      await settled();

      await click(clickable(t("confirm")));

      const store = this.owner.lookup('service:store');
      const automationMailAction = store.peekAll("automation-mail-action")[0];
      const mailActionAddressAttribute = automationMailAction.automationMailActionAddressAttributes[0];
      assert.deepEqual(mailActionAddressAttribute.objectTypeAttribute?.id, relatedOta.id);
      assert.strictEqual(mailActionAddressAttribute.relationshipObjectTypeAttribute?.id, ota.id);
    });
  });

  module('Thumbnails', function() {
    test('it renders the thumbnails as enabled on master', async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });
      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      assert.dom("#thumbnails .thumbnail-container").hasAttribute("role", "button");
    });

    test('it renders the thumbnails as disabled on variant', async function(assert) {
      const team1 = server.create('team', { name: "team1" });
      const team2 = server.create('team', { parent: team1, name: "team2", ancestorIds: [team1.id] });
      const user = server.create('user', { admin: true, teams: [team1, team2] });
      server.signInUser(user);
      const objectType = server.create('object-type', { name: 'Boerderij' });

      server.create('object-type-variant', { objectType: objectType, team: team1, id: '1' });
      server.create('object-type-team-relationship', { objectType: objectType, team: team1, published: true });
      server.create('object-type-team-relationship', { objectType: objectType, team: team2, published: true });

      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      await changeViewPointToTeam(team2);
      assert.dom("#thumbnails .thumbnail-container").hasClass('bg-secondary');
    });

    test('it renders the thumbnails tab with a mock object', async function(assert) {
      server.currentUser.name = 'ThumbnailTest';
      const objectType = server.create('object-type', { name: 'Boerderij' });
      const name = server.create('object-type-attribute', {
        objectType: objectType,
        key: 'name',
        name: 'Name',
        dataType: DATA_TYPE.STRING
      });
      const att = server.create('object-type-attribute', {
        objectType: objectType,
        key: 'attachment',
        name: 'Attachment',
        dataType: DATA_TYPE.ATTACHMENT
      });
      const user = server.create('object-type-attribute', {
        objectType: objectType,
        key: 'user',
        name: 'User',
        dataType: DATA_TYPE.RELATIONSHIP,
        targetClass: 'User',
        relationshipKind: 'single',
        inverseRelationshipKind: 'multiple',
        targetColumnName: 'name'
      });
      const thumbnail = server.create('thumbnail', { objectType: objectType, thumbnailType: THUMBNAIL_TYPE.GRID, default: true, thumbnailAttributes: [] });
      thumbnail.thumbnailAttributes = [
        { object_type_attribute_id: name.id, thumbnail_id: thumbnail.id, row: 0, column: 0 },
        { object_type_attribute_id: att.id, thumbnail_id: thumbnail.id, row: 1, column: 0 },
        { object_type_attribute_id: user.id, thumbnail_id: thumbnail.id, row: 2, column: 0 }
      ];

      const store = this.owner.lookup('service:store');
      assert.strictEqual(store.peekAll("object").length, 0);
      assert.strictEqual(store.peekAll("object-relationship").length, 0);
      assert.strictEqual(store.peekAll("user").length, 0);
      assert.strictEqual(store.peekAll("object-attachment").length, 0);

      resetLoadedState();
      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      const thumbnailSelector = Array.from(findAll('.thumbnail-container')).find((element) => {
        return element.querySelector('div')?.textContent?.includes(t('thumbnail.default_multiple_rows'));
      });
      assert.dom(thumbnailSelector).containsText('Lorem ipsum');
      assert.dom('.img-thumbnail', thumbnailSelector).exists();
      assert.dom(thumbnailSelector).containsText('ThumbnailTest');

      // temporary mock-object data
      assert.strictEqual(store.peekAll("object").length, 1);
      assert.strictEqual(store.peekAll("object-attachment").length, 1);

      await visit(`/objects/${objectType.id}/edit/automation`);
      // temporary mock-object data should have been removed
      assert.strictEqual(store.peekAll("object").length, 0);
      assert.strictEqual(store.peekAll("object-relationship").length, 0);
      assert.strictEqual(store.peekAll("user").length, 0);
      assert.strictEqual(store.peekAll("object-attachment").length, 0);
    });

    test("Toggling 'show attribute names' toggles the display of attribute names", async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij' });

      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      await click(clickable(t("thumbnail.horizontal"), { tag: 'div' }));
      await click(".thumbnail-color-section");
      assert.dom(".thumbnail-card .text-secondary").doesNotExist();
      await click('.show-attribute-names');
      assert.dom(".thumbnail-card .text-secondary").exists();
    });

    test("Toggling 'show object icon' toggles the display of the object icon", async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij', icon: 'fa-cow' });

      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      await click(clickable(t("thumbnail.horizontal"), { tag: 'div' }));
      await click(".thumbnail-color-section");
      assert.dom(".thumbnail-color-section .fa-cow").exists();
      await click('.show-thumbnail-icon');
      assert.dom(".thumbnail-color-section .fa-cow").doesNotExist();
    });

    test("Toggling 'show background image' toggles the display of the background image", async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij', icon: 'fa-cow' });

      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      await click(clickable(t("thumbnail.card"), { tag: 'div' }));
      await click(".thumbnail-color-section");
      assert.dom(".fa-image").exists();
      await click('.show-background-image');
      assert.dom(".fa-image").doesNotExist();
    });

    test("'Show header' toggle is only visible for grid thumbnail", async function(assert) {
      const objectType = server.create('object-type', { name: 'Boerderij', icon: 'fa-cow' });

      await visit(`/objects/${objectType.id}/edit/thumbnails`);
      // Grid
      await click(clickable(t("thumbnail.grid"), { tag: 'div' }));
      await click(".thumbnail-color-section");
      assert.dom(".bg-body").containsText(t("thumbnail.show_title_row"));

      await click('.thumbnail-editor .fa-arrow-left');

      // Horizontal
      await click(clickable(t("thumbnail.horizontal"), { tag: 'div' }));
      await click(".thumbnail-color-section");
      assert.dom(".bg-body").doesNotContainText(t("thumbnail.show_title_row"));

      await click('.thumbnail-editor .fa-arrow-left');

      // Vertical
      await click(clickable(t("thumbnail.vertical"), { tag: 'div' }));
      await click(".thumbnail-color-section");
      assert.dom(".bg-body").doesNotContainText(t("thumbnail.show_title_row"));
    });
  });

  module('ObjectType Access', function() {
    test('user can access the edit page of the object type or variant if he has edit rights', async function(assert) {
      const team = server.create('team', { name: 'Team 42', id: '42', ancestorIds: [] });
      const ot = server.create('object-type', { name: 'Objective' });
      const user = server.create('user', { admin: false, teams: [team], administratedTeams: [team.id], ownedObjectTypes: [ot.id], settings: { last_viewpoint: team.id } });
      server.signInUser(user);
      server.create('object-type-team-relationship', { objectType: ot, team: team });

      await visit(`/objects/${ot.id}/edit/form`);

      assert.dom('.form-builder').exists();
      assert.dom('.form-builder .header').containsText(ot.name);
    });

    test('user will get redirected from the edit page if he switches to a viewpoint where he has no rights', async function(assert) {
      const team = server.create('team', { name: 'Team 42', id: '42', ancestorIds: [] });
      const team2 = server.create('team', { name: 'Team 43', id: '43', ancestorIds: [] });
      const ot = server.create('object-type', { name: 'Objective', teams: [team, team2] });
      const user = server.create('user', { admin: false, teams: [team, team2], administratedTeams: [team.id], ownedObjectTypes: [], settings: { last_viewpoint: team.id } });
      server.signInUser(user);
      server.create('object-type-team-relationship', { objectType: ot, team: team });
      server.create('object-type-team-relationship', { objectType: ot, team: team2 });

      await visit(`/objects/${ot.id}/edit/form`);
      await changeViewPointToTeam(team2);

      const router = this.owner.lookup('service:router');
      assert.dom('.form-builder').doesNotExist();
      assert.strictEqual(router.currentURL, `/objects/${ot.id}`);
    });
  });
});
