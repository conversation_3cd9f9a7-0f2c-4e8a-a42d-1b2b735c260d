import { module, test } from 'qunit';
import { setupRenderingTest } from 'frontend/tests/helpers';
import { click, render, settled, TestContext } from '@ember/test-helpers';
import { hbs } from 'ember-cli-htmlbars';
import ObjectModel from 'frontend/models/object';
import { setupIntl, t } from 'ember-intl/test-support';
import AllowedValues from 'frontend/utils/allowed-values';
import { DATA_TYPE } from 'frontend/models/object-type-attribute';
import { VariantedFormElement, variantFor } from 'frontend/utils/variants';
import { clearSelection, openSelect, selectOption } from 'frontend/tests/helpers/select-helpers';
import { testServer } from 'frontend/tests/server/test-server';
import { setupSession, setCurrentUser } from 'frontend/tests/helpers/session-helper';
import { setViewpoint } from 'frontend/tests/helpers/viewpoint-helpers';
import { DISPLAY_TYPE } from 'frontend/models/conditional-display';
import { OPERATOR, RULE_TYPE } from 'frontend/models/condition-rule';
import { VALIDATION_TYPE } from 'frontend/models/object-type-attribute-validation';
import FormElementOptions from 'frontend/utils/form-element-options';
import { Column } from 'frontend/utils/visualisation';
import { setPropertyTranslations } from 'frontend/tests/helpers/property-translations-helpers';
import { assertPresence } from 'frontend/tests/helpers/custom-asserts';

interface Context extends TestContext {
  formElement: VariantedFormElement;
  object: ObjectModel;
}

module('Integration | Component | form-element', function(hooks) {
  setupRenderingTest(hooks);
  setupIntl(hooks, 'en');

  const stringValue = 'si';
  const numberValue = 12;

  module('Input types', function() {
    module('Description', function() {
      test('it renders the given text', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Description', text: stringValue, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element').hasText(this.formElement.text as string);
      });
    });

    module('TextField', function() {
      test('it renders the number input type and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'yay', dataType: 'Number' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);
        assert.dom('.form-element input[type="number"]').exists();
        assert.dom('.form-element input[type="text"]').doesNotExist();
        assert.dom(`.form-element input[placeholder="${t('enter_number')}"]`).exists();

        this.set('object.values', { 'yay': numberValue });
        assert.dom('.form-element input').hasValue(numberValue.toString());
      });

      test('it renders the text input type and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'String' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'TextField', objectType: objectType, objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);
        assert.dom('.form-element input[type="text"]').exists();
        assert.dom('.form-element input[type="number"]').doesNotExist();
        assert.dom(`.form-element input[placeholder="${t('enter_text')}"]`).exists();

        this.set('object.values', { 'key': stringValue });
        assert.dom('.form-element input').hasValue(stringValue);
      });
    });

    module('TextArea', function() {
      test('it renders a textArea and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'String' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'TextArea', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);
        assert.dom('.form-element textarea').exists();
        assert.dom(`.form-element textarea[placeholder="${t('enter_text')}"]`).exists();

        this.set('object.values', { 'key': stringValue });
        assert.dom('.form-element textarea').hasValue(stringValue);
      });
    });

    module('Date', function() {
      test('it renders a Datepicker and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'Date' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Date', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        setCurrentUser();
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element input.ember-flatpickr-input').exists();
        assert.dom(`.form-element input.ember-flatpickr-input[placeholder="${t('select_date')}"]`).exists();

        this.set('object.values', { 'key': '2023-04-25' });
        assert.dom('.form-element input').hasValue('25-04-2023');
      });
    });

    module('Radio', function() {
      test('it renders Radiobuttons and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const allowedValues = new AllowedValues({ type: 'Select', values: [{ text: 'uno', value: 'uno', archived: false, locked: false }, { text: 'dos', value: 'dos', archived: false, locked: true }, { text: 'tres', value: 'tres', archived: false, locked: true }] });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'nombre', allowedValues: allowedValues });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Radio', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element input[type="radio"]').exists({ count: 3 });

        this.set('object.values', { 'nombre': 'dos' });
        assert.dom('.form-element input[value="dos"]').isChecked();
      });
    });

    module('Checkbox', function() {
      test('it renders Checkboxes and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'Boolean' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Checkbox', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element input[type="checkbox"]').exists();

        this.set('object.values', { 'key': true });
        assert.dom('.form-element input').isChecked();
      });
    });

    module('Checkmark', function() {
      test('it renders a Checkmark and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'Boolean' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Checkmark', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element button i.fa-circle-check').exists();

        this.set('object.values', { 'key': true });
        assert.dom('.form-element button i').hasClass('text-success');
      });
    });

    module('Toggle', function() {
      test('it renders a Switch and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'Boolean' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Toggle', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element .form-switch').exists();

        this.set('object.values', { 'key': true });
        assert.dom('.form-element input').isChecked();
      });
    });

    module('Select', function() {
      test('it renders a select and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        // a values needs to be assigned for tomSelectedField
        this.object = store.createRecord('object', { objectType: objectType, values: { 'nombre': 'uno' } });
        const allowedValues = new AllowedValues({ type: 'Select', values: [{ text: 'uno', value: 'uno', archived: false, locked: true }, { text: 'dos', value: 'dos', archived: false, locked: true }, { text: 'tres', value: 'tres', archived: false, locked: true }] });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, allowedValues: allowedValues, name: 'nombre', key: 'nombre' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Select', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);

        assert.dom('.form-element select').exists();

        this.set('object.values', { 'nombre': 'dos' });
        assert.dom('.form-element select').hasValue('dos');
      });
    });

    module('RichText', function() {
      test('it renders a Richtext and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'HTML' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'RichText', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);
        assert.dom('.form-element .ql-editor').exists();
        assert.dom(`.form-element .ql-editor[data-placeholder="${t('enter_text')}"]`).exists();

        this.set('object.values', { 'key': stringValue });
        assert.dom('.form-element .ql-editor').hasText(stringValue);
      });
    });

    module('Range', function() {
      test('it renders a Slider and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const allowedValues = new AllowedValues({ type: 'Range', min: 0, max: 100, step: 1 });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'Number', allowedValues: allowedValues });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Range', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);
        assert.dom('.form-element .noUi-horizontal').exists();

        this.set('object.values', { 'key': numberValue });
        assert.dom('.form-element .noUi-base').containsText(numberValue.toString(), 'the value is rendered');
      });
    });

    module('URL', function() {
      test('it renders a URL and rerenders on value change', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'key', dataType: 'String' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'URL', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />`);
        assert.dom('.form-element a[target="_blank"]').exists();

        this.set('object.values', { 'key': 'https://www.hoehel.be/' });
        assert.dom('.form-element a').hasText('https://www.hoehel.be/');
        assert.dom('.form-element a').hasProperty('href', 'https://www.hoehel.be/');
      });
    });

    module('Relationship', function() {
      test('it renders a Relationship and rerenders on value change', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const linkedObject1 = await store.createRecord('object', { objectType: linkedObjectType, id: '1' }).save();
        const linkedObject2 = await store.createRecord('object', { objectType: linkedObjectType, id: '2' }).save();

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: '1',
          targetColumnName: 'id',
          relationshipKind: 'single',
          inverseRelationshipKind: 'multiple'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3' });

        store.createRecord('object-relationship', { objectTypeAttribute: objectTypeAttribute, model: linkedObject1, object: this.object });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: objectTypeAttribute,
          formTab: formTab
        });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);
        assert.dom('.ts-control .item').hasText(linkedObject1.id);
        await openSelect();
        selectOption(linkedObject2.id);
        assert.dom('.ts-control .item').hasText(linkedObject2.id);

        server.shutdown();
      });

      test('it hides the tom-select when select option is disabled and when no value is already selected', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' });
        const linkedObject = await store.createRecord('object', { objectType: linkedObjectType, id: '1' });

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: '1',
          targetColumnName: 'id',
          relationshipKind: 'single'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3' });

        store.createRecord('object-relationship', { objectTypeAttribute: objectTypeAttribute, model: linkedObject, object: this.object });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: objectTypeAttribute,
          formTab: formTab,
          options: new FormElementOptions({ select: { disabled: true } })
        });
        this.formElement = variantFor(formElement);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);

        assert.dom('select').exists('tom-select is shown when a value is selected');
        clearSelection();
        await settled();
        assert.dom('select').doesNotExist('tom-select is not shown when no value is selected');
        server.shutdown();
      });

      test('it does not render the select when the relationship is shown as a thumbnail and when it is single and selected and when it is disabled', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1', color: 'red' }).save();
        const linkedObject = await store.createRecord('object', { objectType: linkedObjectType, id: '1' }).save();

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: '1',
          targetColumnName: 'id',
          relationshipKind: 'single'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3' });

        store.createRecord('object-relationship', { objectTypeAttribute: objectTypeAttribute, model: linkedObject, object: this.object });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: objectTypeAttribute,
          formTab: formTab,
          customDisplay: 'Thumbnail',
          options: new FormElementOptions({ select: { disabled: true } })
        });
        this.formElement = variantFor(formElement);
        setCurrentUser(store.createRecord('current-user', { id: '1', settings: { date_format: '%d-%m-%Y' } }));
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
            @openEditObjectModal={{(noop)}}
          />
        `);

        assert.dom('select').doesNotExist('tom-select is not shown when a value is selected with thumbnail display');
        assert.dom('.form-element-thumbnail').exists('thumbnail is rendered');
        await click('.btn-close');
        assert.dom('select').doesNotExist('tom-select is not shown when disabled is set true');
        assert.dom('.form-element-thumbnail').doesNotExist('thumbnail is not shown after removal');

        this.formElement.options!.select = { disabled: false };
        await settled();
        assert.dom('select').exists('tom-select is shown when no value is selected with thumbnail display');

        await openSelect();
        await selectOption(linkedObject.id);
        await settled();
        assert.dom('.form-element-thumbnail').exists('thumbnail is rendered after selecting option');
        assert.dom('select').doesNotExist('tom-select is not shown when a value is selected with thumbnail display');

        server.shutdown();
      });

      test('it render the select when the relationship is shown as a thumbnail and when multiple options can be selected', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '3', color: 'red' }).save();
        const linkedObject = await store.createRecord('object', { objectType: linkedObjectType, id: '5' }).save();

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          targetColumnName: 'id',
          relationshipKind: 'multiple'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3' });

        store.createRecord('object-relationship', { objectTypeAttribute: objectTypeAttribute, model: linkedObject, object: this.object });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: objectTypeAttribute,
          formTab: formTab,
          customDisplay: 'Thumbnail'
        });
        this.formElement = variantFor(formElement);
        setCurrentUser(store.createRecord('current-user', { id: '1', settings: { date_format: '%d-%m-%Y' } }));
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
            @openEditObjectModal={{(noop)}}
          />
        `);

        assert.dom('.form-element-thumbnail').exists('thumbnail is rendered');
        assert.dom('select').exists('tom-select is shown because multiple options are allowed and select is not disabled');
        await click('.btn-close');
        assert.dom('select').exists('tom-select is shown even when no option is selected');
        assert.dom('.form-element-thumbnail').doesNotExist('thumbnail is not shown after removal');

        server.shutdown();
      });

      test('it renders a table for table custom display', async function(this: Context, assert) {
        setPropertyTranslations(this);
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '3', color: 'red' }).save();
        const linkedObjectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.STRING,
          id: '42',
          name: 'LinkedObjectTypeAttribute'
        });
        const linkedObject = await store.createRecord('object', { objectType: linkedObjectType, id: '5', values: { 'ota1': "My Text" } }).save();

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          targetColumnName: 'id',
          relationshipKind: 'multiple'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3' });

        store.createRecord('object-relationship', { objectTypeAttribute: objectTypeAttribute, model: linkedObject, object: this.object });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: objectTypeAttribute,
          formTab: formTab,
          customDisplay: 'Table',
          options: {
            columns: [new Column({ column_name: "id" }), new Column({ object_type_attribute_id: linkedObjectTypeAttribute.id })]
          }
        });
        this.formElement = variantFor(formElement);
        setCurrentUser(store.createRecord('current-user', { id: '1', settings: { date_format: '%d-%m-%Y' } }));
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
            @openEditObjectModal={{(noop)}}
          />
        `);

        assert.dom('.object-table').exists();
        assert.dom('thead').containsText('Global ID');
        assert.dom('thead').containsText('LinkedObjectTypeAttribute');
        assert.dom('.object-row').containsText(linkedObject.id);
        assert.dom('.object-row').containsText("My Text");

        // Needed otherwise the cleanup of the view in formElementTable will fail
        store.unloadAll('view');
        server.shutdown();
      });

      test('it renders a user relationship and rerenders on value change', async function(this: Context, assert) {
        const server = testServer(true);
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const linkedObjectType = store.createRecord('object-type', { name: 'LOT', id: '1' });
        const team = store.createRecord('team', { id: '1', name: 'team1' });
        store.createRecord('user', { objectType: linkedObjectType, id: '1', name: 'user1', teams: [team] });
        store.createRecord('user', { objectType: linkedObjectType, id: '2', name: 'user2' });

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'User',
          targetColumnName: 'id',
          relationshipKind: 'single',
          inverseRelationshipKind: 'multiple'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3', TargetClass: 'User' });

        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectType: objectType,
          objectTypeAttribute: objectTypeAttribute,
          teamFilterable: true
        });
        this.formElement = variantFor(formElement);
        setViewpoint(team);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);
        await click('.ts-control');
        const dropdown = document.querySelector('.ts-dropdown');
        assert.dom(dropdown).includesText('user1');
        assert.dom(dropdown).doesNotIncludeText('user2');

        server.shutdown();
      });

      test('it renders a user relationship and filtered on targetTeamId', async function(this: Context, assert) {
        const server = testServer(true);
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const team1 = server.create('team', { id: '1', name: 'team1' });
        const team2 = server.create('team', { id: '2', name: 'team2' });
        server.create('user', { name: 'user1', teams: [team1] });
        server.create('user', { name: 'user2' });
        server.create('user', { name: 'user3', teams: [team2] });
        server.create('user', { name: 'user4', teams: [team1, team2] });
        server.create('user', { name: 'user5', teams: [team2] });
        const team1f = store.createRecord('team', { id: '1', name: 'team1' });

        const objectType = store.createRecord('object-type', { id: '2' });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'User',
          targetColumnName: 'id',
          relationshipKind: 'single',
          inverseRelationshipKind: 'multiple',
          targetTeamId: '2'
        });
        this.object = store.createRecord('object', { objectType: objectType, id: '3' });

        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectType: objectType,
          objectTypeAttribute: objectTypeAttribute,
          teamFilterable: true
        });
        this.formElement = variantFor(formElement);
        setViewpoint(team1f);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);
        await click('.ts-control');
        const dropdown = document.querySelector('.ts-dropdown');
        assert.dom(dropdown).doesNotIncludeText('user1');
        assert.dom(dropdown).doesNotIncludeText('user2');
        assert.dom(dropdown).includesText('user3');
        assert.dom(dropdown).includesText('user4');
        assert.dom(dropdown).includesText('user5');

        server.shutdown();
      });

      test('it can add a new linked record when clicking the new relationship button for inverse relationships', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const team = store.createRecord('team', { id: '1', name: 'team1' });
        setViewpoint(team);
        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const variant = store.createRecord('object-type-variant', { id: "1", objectType: linkedObjectType, team: team });
        const objectType = store.createRecord('object-type', { id: '2' });

        // Create the forward relationship OTA
        const forwardOta = store.createRecord('object-type-attribute', {
          key: 'forward',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: objectType.id,
          relationshipKind: 'multiple',
          canUpdate: true
        });

        // Create the inverse relationship OTA
        const inverseOta = store.createRecord('object-type-attribute', {
          key: 'inverse',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          relationshipKind: 'multiple',
          inverse: true,
          linkedObjectTypeAttributes: [forwardOta],
          canUpdate: true
        });

        this.object = await store.createRecord('object', { objectType: objectType, team: team }).save();
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: inverseOta,
          formTab: formTab
        });
        setCurrentUser();

        this.formElement = variantFor(formElement, variant);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);

        assert.strictEqual(this.object.getRelationshipObjects(inverseOta).length, 0);
        assert.dom('.fa-plus').exists('New relationship button exists');
        await click('.fa-plus');
        assert.strictEqual(this.object.getRelationshipObjects(inverseOta).length, 1);
        const lastCreatedObject = store.peekAll('object').lastObject;
        assertPresence(lastCreatedObject, 'Last created object exists');
        assert.strictEqual(lastCreatedObject.getRelationshipObjects(forwardOta).length, 1);
        assert.strictEqual(lastCreatedObject.getRelationshipObjects(forwardOta)[0], this.object);

        server.shutdown();
      });

      test('it does not render a new relationship button if the object is new', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const team = store.createRecord('team', { id: '1', name: 'team1' });
        setViewpoint(team);
        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const variant = store.createRecord('object-type-variant', { id: "1", objectType: linkedObjectType, team: team });
        const objectType = store.createRecord('object-type', { id: '2' });

        // Create the forward relationship OTA
        const forwardOta = store.createRecord('object-type-attribute', {
          key: 'forward',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: objectType.id,
          relationshipKind: 'multiple',
          canUpdate: true
        });

        // Create the inverse relationship OTA
        const inverseOta = store.createRecord('object-type-attribute', {
          key: 'inverse',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          relationshipKind: 'multiple',
          inverse: true,
          linkedObjectTypeAttributes: [forwardOta],
          canUpdate: true
        });

        this.object = store.createRecord('object', { objectType: objectType, team: team });
        assert.true(this.object.isNew, 'Object is new');
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: inverseOta,
          formTab: formTab
        });
        setCurrentUser();

        this.formElement = variantFor(formElement, variant);

        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);

        assert.dom('.fa-plus').doesNotExist('New relationship button does not exist');
        assert.dom('select').exists('Relationship select element is rendered');
        assert.dom('.ts-control').exists('TomSelect control is rendered');

        server.shutdown();
      });

      test('it does not render the new relationship button if it is disabled in the form element options', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');
        const team = store.createRecord('team', { id: '1', name: 'team1' });
        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const variant = store.createRecord('object-type-variant', { id: "1", objectType: linkedObjectType, team: team });
        const objectType = store.createRecord('object-type', { id: '2' });

        const relationshipOta = store.createRecord('object-type-attribute', {
          key: 'forward',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          relationshipKind: 'multiple',
          canUpdate: true
        });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: relationshipOta,
          formTab: formTab,
          options: new FormElementOptions({ new_relationship_button: { disabled: true } })
        });

        this.object = store.createRecord('object', { objectType: objectType, team: team });
        this.formElement = variantFor(formElement, variant);
        setViewpoint(team);
        setCurrentUser();

        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);

        assert.dom('.fa-plus').doesNotExist('New relationship button does not exist');
        assert.dom('select').exists('Relationship select element is rendered');
        assert.dom('.ts-control').exists('TomSelect control is rendered');

        server.shutdown();
      });

      test('it does not render the new relationship button if relationship is inverse and has multiple linked forward relationships', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');
        const team = store.createRecord('team', { id: '1', name: 'team1' });
        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const variant = store.createRecord('object-type-variant', { id: '1', objectType: linkedObjectType, team: team });
        const objectType = store.createRecord('object-type', { id: '2' });

        const forwardOta1 = store.createRecord('object-type-attribute', {
          key: 'forward1',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: objectType.id,
          relationshipKind: 'multiple',
          inverseRelationshipKind: 'multiple',
          canUpdate: true
        });
        const forwardOta2 = store.createRecord('object-type-attribute', {
          key: 'forward2',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: objectType.id,
          relationshipKind: 'multiple',
          inverseRelationshipKind: 'multiple',
          canUpdate: true
        });

        // Create the inverse relationship OTA
        const inverseOta = store.createRecord('object-type-attribute', {
          key: 'inverse',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          relationshipKind: 'multiple',
          inverse: true,
          linkedObjectTypeAttributes: [forwardOta1, forwardOta2],
          canUpdate: true
        });

        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: inverseOta,
          formTab: formTab
        });

        this.object = store.createRecord('object', { objectType: objectType, team: team });
        this.formElement = variantFor(formElement, variant);
        setViewpoint(team);
        setCurrentUser();

        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);

        assert.dom('.fa-plus').doesNotExist('New relationship button does not exist');
        assert.dom('select').exists('Relationship select element is rendered');
        assert.dom('.ts-control').exists('TomSelect control is rendered');

        server.shutdown();
      });

      test('it does not render the new relationship button if relationship is inverse and of type single', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');
        const team = store.createRecord('team', { id: '1', name: 'team1' });
        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const variant = store.createRecord('object-type-variant', { id: '1', objectType: linkedObjectType, team: team });
        const objectType = store.createRecord('object-type', { id: '2' });

        const forwardOta = store.createRecord('object-type-attribute', {
          key: 'forward',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: objectType.id,
          relationshipKind: 'multiple',
          inverseRelationshipKind: 'single',
          canUpdate: true
        });

        // Create the inverse relationship OTA
        const inverseOta = store.createRecord('object-type-attribute', {
          key: 'inverse',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          relationshipKind: 'multiple',
          inverse: true,
          linkedObjectTypeAttributes: [forwardOta],
          canUpdate: true
        });

        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: inverseOta,
          formTab: formTab
        });

        this.object = store.createRecord('object', { objectType: objectType, team: team });
        this.formElement = variantFor(formElement, variant);
        setViewpoint(team);
        setCurrentUser();

        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);

        assert.dom('.fa-plus').doesNotExist('New relationship button does not exist');
        assert.dom('select').exists('Relationship select element is rendered');
        assert.dom('.ts-control').exists('TomSelect control is rendered');

        server.shutdown();
      });

      test('hide the relationshipSelect for inverse relationships with relationships present', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);

        const store = this.owner.lookup('service:store');

        const team = store.createRecord('team', { id: '1', name: 'team1' });
        setViewpoint(team);
        const linkedObjectType = await store.createRecord('object-type', { name: 'LOT', id: '1' }).save();
        const objectType = store.createRecord('object-type', { id: '2' });
        const variant = store.createRecord('object-type-variant', { id: "1", objectType: objectType, team: team });

        // Create the forward relationship OTA
        const forwardOta = store.createRecord('object-type-attribute', {
          key: 'forward',
          objectType: linkedObjectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: objectType.id,
          relationshipKind: 'multiple',
          canUpdate: true
        });

        // Create the inverse relationship OTA
        const inverseOta = store.createRecord('object-type-attribute', {
          key: 'inverse',
          objectType: objectType,
          dataType: DATA_TYPE.RELATIONSHIP,
          targetClass: 'Object',
          targetObjectTypeId: linkedObjectType.id,
          relationshipKind: 'multiple',
          inverse: true,
          linkedObjectTypeAttributes: [forwardOta],
          canUpdate: true
        });

        this.object = store.createRecord('object', { objectType: objectType, team: team, id: '1' });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', {
          inputType: 'Relationship',
          objectTypeAttribute: inverseOta,
          formTab: formTab,
          customDisplay: 'Thumbnail',
          options: {
            columns: []
          }
        });
        const linkedObject = store.createRecord('object', { objectType: linkedObjectType, team: team, id: '2' });
        store.createRecord('object-relationship', { object: linkedObject, objectTypeAttribute: forwardOta, model: this.object });
        setCurrentUser();

        this.formElement = variantFor(formElement, variant);
        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
            @openEditObjectModal={{(noop)}}
          />
        `);

        assert.strictEqual(this.object.getRelationshipObjects(inverseOta).length, 1);
        assert.dom('select').doesNotExist();
        assert.dom('.form-element').exists();
        assert.dom('.form-element-value').exists();

        server.shutdown();
      });
    });

    module('Button', function() {
      test('it can render buttons and passes the automations trigger endpoint when clicking', async function(this: Context, assert) {
        const server = testServer();
        setupSession(server);
        let passedEndpoint = false;
        server.post('/automations/:id/trigger', function() {
          passedEndpoint = true;
          return new Response('200');
        });
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const formElement = store.createRecord('form-element', { inputType: 'Button', text: 'buttin', formTab: formTab });
        const automation = store.createRecord('automation', { id: 1, name: 'automation' });
        store.createRecord('frontend-event', { formElement: formElement, automation: automation, saveBeforeExecute: false });
        this.formElement = variantFor(formElement);

        await render(hbs`
            <FormElement
              @formElement={{this.formElement}}
              @object={{this.object}}
            />
          `);

        assert.dom('.btn.btn-accent').exists().hasText(formElement.text as string);
        await click('.btn.btn-accent');
        assert.true(passedEndpoint, 'Clicking the button reaches /automation/trigger');
        server.shutdown();
      });
    });

    module('Attachment', function() {
      test('it can render attachments', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.ATTACHMENT
        });
        const formElement = store.createRecord('form-element', { inputType: 'Attachment', formTab: formTab, objectTypeAttribute: objectTypeAttribute });
        store.createRecord('object-attachment', { object: this.object, objectTypeAttribute: objectTypeAttribute, contentType: 'image/png' });
        this.formElement = variantFor(formElement);

        await render(hbs`
            <FormElement
              @formElement={{this.formElement}}
              @object={{this.object}}
            />
          `);
        assert.dom('.drop-zone').exists();
      });

      test('it can render a signature pad', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.ATTACHMENT
        });
        const formElement = store.createRecord('form-element', { inputType: 'Signature', formTab: formTab, objectTypeAttribute: objectTypeAttribute });
        this.formElement = variantFor(formElement);

        await render(hbs`
            <FormElement
              @formElement={{this.formElement}}
              @object={{this.object}}
            />
          `);
        assert.dom('.signature-pad').exists();
      });

      test('it does not render the signature pad when a signature is already uploaded', async function(this: Context, assert) {
        const store = this.owner.lookup('service:store');
        const objectType = store.createRecord('object-type');
        this.object = store.createRecord('object', { objectType: objectType });
        const formTab = store.createRecord('form-tab', { objectType: objectType });
        const objectTypeAttribute = store.createRecord('object-type-attribute', {
          key: 'ota1',
          objectType: objectType,
          dataType: DATA_TYPE.ATTACHMENT
        });
        const formElement = store.createRecord('form-element', { inputType: 'Signature', formTab: formTab, objectTypeAttribute: objectTypeAttribute });
        store.createRecord('object-attachment', { id: '1', object: this.object, objectTypeAttribute: objectTypeAttribute, contentType: 'image/png' });
        this.formElement = variantFor(formElement);

        await render(hbs`
          <FormElement
            @formElement={{this.formElement}}
            @object={{this.object}}
          />
        `);
        assert.dom('.signature-pad').doesNotExist();
      });
    });
  });

  module('Disabled', function() {
    test('it disables the form element if a conditional display with object condition rule disables it', async function(this: Context, assert: Assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType, values: {} });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'yay', dataType: 'String' });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
      const conditionalDisplay = store.createRecord('conditional-display', { displayType: DISPLAY_TYPE.DISABLE, subject: formElement });
      store.createRecord('condition-rule', { conditionalDisplay: conditionalDisplay, objectTypeAttribute: objectTypeAttribute, operator: OPERATOR.EQUALS, value: 'test', ruleType: RULE_TYPE.OBJECT });
      this.formElement = variantFor(formElement);
      await render(hbs`
      <FormElement
        @formElement={{this.formElement}}
        @object={{this.object}}
      />
    `);

      assert.dom('.form-element input[type="text"]').exists();
      assert.dom('.form-element input[type="text"]').isEnabled();
      this.set('object.values', { 'yay': 'test' });
      assert.dom('.form-element input[type="text"]').isDisabled();
    });

    test('it disables the form element if a conditional display with user condition rule disables it', async function(this: Context, assert: Assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      const role = store.createRecord('object-type-role', {
        id: '7',
        objectType: objectType,
        throughTeam: true,
        roleType: 'custom'
      });
      // The serializer's structure for roles is the following:
      // roles: { 10: ['1', '2'.. ] }
      const objectScopeTeam = store.createRecord('team', { id: '1', ancestorIds: [], roles: { [role.id]: ['1'] } });
      store.createRecord('object-type-role-team-relationship', { objectTypeRole: role, team: objectScopeTeam });
      setCurrentUser(store.createRecord('current-user', { id: '1', teams: [objectScopeTeam] }));
      this.object = store.createRecord('object', { objectType: objectType, team: objectScopeTeam });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'yay', dataType: 'String' });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
      const conditionalDisplay = store.createRecord('conditional-display', { displayType: DISPLAY_TYPE.DISABLE, subject: formElement });
      store.createRecord('condition-rule', { id: '10', conditionalDisplay: conditionalDisplay, operator: OPERATOR.EQUALS, value: '7', ruleType: RULE_TYPE.USER });
      this.formElement = variantFor(formElement);
      await render(hbs`
      <FormElement
        @formElement={{this.formElement}}
        @objectType={{this.objectType}}
        @object={{this.object}}
      />
    `);

      assert.dom('.form-element input[type="text"]').exists();
      assert.dom('.form-element input[type="text"]').isDisabled();
    });

    test('it disables the form element when the associated object type attribute is calculated', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'text', dataType: DATA_TYPE.STRING, calculated: true });
      store.createRecord('calculation', { objectTypeAttribute: objectTypeAttribute }); // not strictly necessary
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });

      this.formElement = variantFor(formElement);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
        />`);
      assert.dom('.form-element input[type="text"]').exists();
      assert.dom('.form-element input[type="text"]').isDisabled();
    });
  });

  module('Validations', function() {
    test('shows an asterisk (*) next to the field name when it is required', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'name', dataType: 'String', name: 'Name' });
      objectTypeAttribute.objectTypeAttributeValidations.createRecord({ validationType: VALIDATION_TYPE.PRESENCE });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
      this.formElement = variantFor(formElement);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
        />`);

      assert.dom('.form-element').hasText('Name*');
    });

    test('does not show an asterisk (*) when it does not have presence validations', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'name', dataType: 'String', name: 'Name' });
      objectTypeAttribute.objectTypeAttributeValidations.createRecord({ validationType: VALIDATION_TYPE.MAX_LENGTH, value: 50 });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });

      this.formElement = variantFor(formElement);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
        />`);

      assert.dom('.form-element').hasText('Name');
      assert.dom('.form-element').doesNotHaveTextContaining('*');
    });
  });

  module('Hidden icon', function() {
    test('shows the hidden icon if form element is hidden in variant', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const parentTeam = store.createRecord('team');
      const objectTypeVariant = store.createRecord('object-type-variant', { id: '1', team: parentTeam, objectType: objectType });

      const objectTypeAttribute = store.createRecord('object-type-attribute', {
        objectType: objectType,
        key: 'name',
        dataType: 'String',
        name: 'Name',
        locked: false,
        hidden: { [objectTypeVariant.id]: true }
      });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });

      this.formElement = variantFor(formElement, objectTypeVariant);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
          @editMode={{true}}
        />`);

      assert.dom('.form-element i.fa-eye-slash').exists();
    });

    test('does not show the hidden icon if form element is not hidden in variant', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const parentTeam = store.createRecord('team');
      const objectTypeVariant = store.createRecord('object-type-variant', { id: '1', team: parentTeam, objectType: objectType });

      const objectTypeAttribute = store.createRecord('object-type-attribute', {
        objectType: objectType,
        key: 'name',
        dataType: 'String',
        name: 'Name',
        locked: false,
        hidden: { [objectTypeVariant.id]: false }
      });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });

      this.formElement = variantFor(formElement, objectTypeVariant);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
          @editMode={{true}}
        />`);

      assert.dom('.form-element i.fa-eye-slash').doesNotExist();
    });
  });

  module('hide label', function() {
    test('it hides the label when hideLabel arg is true', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'name', dataType: 'String', name: 'Name' });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab, hideLabel: true });
      this.formElement = variantFor(formElement);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
          @hideLabel={{true}}
        />`);

      assert.dom('.form-element label').doesNotExist();
    });

    test('it shows the label when the hideLabel arg is false', async function(this: Context, assert) {
      const store = this.owner.lookup('service:store');
      const objectType = store.createRecord('object-type');
      this.object = store.createRecord('object', { objectType: objectType });
      const objectTypeAttribute = store.createRecord('object-type-attribute', { objectType: objectType, key: 'name', dataType: 'String', name: 'Name' });
      const formTab = store.createRecord('form-tab', { objectType: objectType });
      const formElement = store.createRecord('form-element', { inputType: 'TextField', objectTypeAttribute: objectTypeAttribute, formTab: formTab });
      this.formElement = variantFor(formElement);
      await render(hbs`
        <FormElement
          @formElement={{this.formElement}}
          @object={{this.object}}
        />`);

      assert.dom('.form-element label').exists();
      assert.dom('.form-element label').hasText('Name');
    });
  });
});
