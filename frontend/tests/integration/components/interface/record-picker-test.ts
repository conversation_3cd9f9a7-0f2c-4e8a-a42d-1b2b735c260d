import { module, test } from 'qunit';
import { setupRenderingTest } from 'frontend/tests/helpers';
import { render, TestContext } from '@ember/test-helpers';
import { hbs } from 'ember-cli-htmlbars';
import { setupIntl } from 'ember-intl/test-support';
import { testServer } from 'frontend/tests/server/test-server';
import { setupSession } from 'frontend/tests/helpers/session-helper';
import InterfaceElementRecordPickerModel from 'frontend/models/interface-element-record-picker';
import { getRenderedOptions, openSelect } from 'frontend/tests/helpers/select-helpers';

interface Context extends TestContext {
  recordPicker: InterfaceElementRecordPickerModel;
}

module('Integration | Component | interface/record-picker', function(hooks) {
  setupRenderingTest(hooks);
  setupIntl(hooks, 'en');

  let server: any;

  hooks.beforeEach(() => {
    server = testServer();
    setupSession(server);
  });

  hooks.afterEach(() => {
    server.shutdown();
  });

  test('loadRecords function queries store with appropriate filters and context', async function(this: Context, assert) {
    const store = this.owner.lookup('service:store');

    // Create the necessary models
    const objectType = store.createRecord('object-type', { id: '3', name: 'Test Object Type' });
    const team = store.createRecord('team', { id: '2', name: 'Test Team' });
    const interfaceModel = store.createRecord('interface', { id: '1', name: 'Test Interface' });
    interfaceModel.dataScopeTeams.push(team);
    const section = store.createRecord('section', { id: '1', name: 'Test Section', interface: interfaceModel });
    const interfaceElement = store.createRecord('interface-element', { id: '1', name: 'Test Element', section: section });
    const objectQueryView = store.createRecord('object-query-view', { id: '1' });

    // Create the recordPicker with all necessary relationships
    this.recordPicker = store.createRecord('interface-element-record-picker', {
      id: '1',
      objectType: objectType,
      interfaceElement: interfaceElement,
      objectQueryView: objectQueryView
    });

    // Setup the server mock for objects endpoint
    server.get('/objects', (_schema: any, request: any) => {
      assert.strictEqual(request.queryParams['context[subteams]'], 'true', 'include objects of subteams');
      assert.strictEqual(request.queryParams['context[shared]'], 'false', 'exclude shared objects');
      assert.strictEqual(request.queryParams['filter[object_type_id]'], '3');
      assert.strictEqual(request.queryParams['filter[team_id]'], '2', 'filter by team');
      assert.strictEqual(request.queryParams['page[size]'], '10');
      assert.strictEqual(request.queryParams['page[number]'], '1');

      return {
        data: [
          {
            id: '123',
            type: 'object'
          }
        ],
        meta: {
          total_count: 1
        }
      };
    });

    // Render the component
    await render(hbs`<Interface::RecordPicker @recordPicker={{this.recordPicker}} />`);

    // Assert the component renders correctly
    assert.dom('[data-select="interface-element-record-picker-select"]').exists('Record picker select exists');

    // Open the select and verify it loads data
    await openSelect();
    const options = getRenderedOptions();
    assert.deepEqual(options, ['123']);
  });
});
