import InterfaceElementButtonModel from './models/interface-element-button';
import InterfaceElementRecordPickerModel from './models/interface-element-record-picker';
import ObjectModel from './models/object';
import ObjectQueryRuleModel from './models/object-query-rule';
import ObjectQueryViewModel from './models/object-query-view';
import ObjectTypeVariantModel from './models/object-type-variant';
import ObjectTypeModel from './models/object-type';
import ObjectTypeUserRelationshipModel from './models/object-type-user-relationship';
import ShortcutModel from './models/shortcut';
import UserModel from './models/user';
import TeamModel from './models/team';
import ThumbnailModel from './models/thumbnail';
import ViewModel from './models/view';
import VisualisationModel from './models/visualisation';
import InterfaceModel from './models/interface';
import SectionModel from './models/section';
import ObjectTypeGroupModel from './models/object-type-group';
import FormElementModel from './models/form-element';
import FrontendEventModel from './models/frontend-event';
import ObjectTypeAttributeModel from './models/object-type-attribute';
import FormTabModel from './models/form-tab';
import AutomationModel from './models/automation';
import ObjectTypeRoleModel from './models/object-type-role';
import JobModel from './models/job';
import ObjectRelationshipModel from './models/object-relationship';
import AutomationActionModel from './models/automation-action';
import AutomationMailActionModel from './models/automation-mail-action';
import UserTeamJoinRequestModel from './models/user-team-join-request';
import ObjectTypeDescriptionModel from './models/object-type-description';
import ObjectTypeAttributeValidationModel from './models/object-type-attribute-validation';
import InterfaceTeamRelationshipModel from './models/interface-team-relationship';
import ObjectTypeTeamRelationshipModel from './models/object-type-team-relationship';
import InvitationModel from './models/invitation';
import QrCodeModel from './models/qr-code';
import DataSourceModel from './models/data-source';
import InterfaceElementModel from './models/interface-element';
import UserInterfaceRelationshipModel from './models/user-interface-relationship';
import ExternalUrlNavigationActionModel from './models/external-url-navigation-action';
import InterfaceSectionNavigationActionModel from './models/interface-section-navigation-action';
import InterfaceElementFieldModel from './models/interface-element-field';
import WidgetModel from './models/widget';
import InterfaceElementFilterModel from './models/interface-element-filter';
import InterfaceElementLinkModel from './models/interface-element-link';
import InterfaceElementLinkTabModel from './models/interface-element-link-tab';
import CalculationModel from './models/calculation';

const models = {
  calculation: CalculationModel,
  interfaceElementButton: InterfaceElementButtonModel,
  interfaceElementLink: InterfaceElementLinkModel,
  interfaceElementLinkTab: InterfaceElementLinkTabModel,
  interfaceElementRecordPicker: InterfaceElementRecordPickerModel,
  externalUrlNavigationAction: ExternalUrlNavigationActionModel,
  interfaceSectionNavigationAction: InterfaceSectionNavigationActionModel,
  formElement: FormElementModel,
  formTab: FormTabModel,
  frontendEvent: FrontendEventModel,
  job: JobModel,
  object: ObjectModel,
  objectQueryRule: ObjectQueryRuleModel,
  objectQueryView: ObjectQueryViewModel,
  objectType: ObjectTypeModel,
  objectTypeAttribute: ObjectTypeAttributeModel,
  objectTypeAttributeValidation: ObjectTypeAttributeValidationModel,
  objectTypeUserRelationship: ObjectTypeUserRelationshipModel,
  shortcut: ShortcutModel,
  team: TeamModel,
  thumbnail: ThumbnailModel,
  user: UserModel,
  view: ViewModel,
  visualisation: VisualisationModel,
  interface: InterfaceModel,
  interfaceElement: InterfaceElementModel,
  dataSource: DataSourceModel,
  section: SectionModel,
  objectTypeGroup: ObjectTypeGroupModel,
  automation: AutomationModel,
  objectTypeRole: ObjectTypeRoleModel,
  objectTypeVariant: ObjectTypeVariantModel,
  objectRelationship: ObjectRelationshipModel,
  automationAction: AutomationActionModel,
  automationMailAction: AutomationMailActionModel,
  userTeamJoinRequest: UserTeamJoinRequestModel,
  objectTypeDescription: ObjectTypeDescriptionModel,
  interfaceTeamRelationship: InterfaceTeamRelationshipModel,
  objectTypeTeamRelationship: ObjectTypeTeamRelationshipModel,
  invitation: InvitationModel,
  qrCode: QrCodeModel,
  userInterfaceRelationship: UserInterfaceRelationshipModel,
  interfaceElementField: InterfaceElementFieldModel,
  widget: WidgetModel,
  interfaceElementFilter: InterfaceElementFilterModel
};

export { models };
