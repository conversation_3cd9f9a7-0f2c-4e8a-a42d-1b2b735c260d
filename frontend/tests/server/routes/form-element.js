import { camelize } from "@ember/string";

export function routesForFormElements(server) {
  server.patch('/form_elements/:id');

  server.post('/form_elements', function(schema, request) {
    const attrs = this.normalizedRequestAttrs('form-element');
    let ota = findOta(JSON.parse(request.requestBody));
    let dbOta;
    if (ota) {
      const formTab = schema.formTabs.find(attrs.formTabId);
      ota = Object.keys(ota.attributes).reduce((sum, key) => {
        sum[transformKey(key)] = ota.attributes[key];
        return sum;
      }, { objectTypeId: formTab?.objectTypeId });
      dbOta = schema.objectTypeAttributes.create(ota);
    }

    const formElement = schema.formElements.create({ ...attrs, objectTypeAttributeId: dbOta?.id });
    const data = this.serialize(formElement);
    if (dbOta) {
      data.included = data.included || [];
      const serializedOta = this.serialize(dbOta);
      data.included.push(serializedOta.data);
    }

    return data;
  });

  server.get('/form_elements');
}

function transformKey(key) {
  if (key == '__guid__') return key;
  return camelize(key);
}

function findOta(payload) {
  return payload?.included?.find((d) => {
    return d && d.type == 'object_type_attribute' && d.attributes;
  });
}
