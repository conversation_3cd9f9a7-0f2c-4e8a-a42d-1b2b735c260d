import { INTERFACE_ELEMENT_TYPE } from "frontend/models/interface-element";
import interfaceElementButton from "../factories/interface-element-button";
import { INTERFACE_ELEMENT_BUTTON_ACTION_TYPES } from "frontend/models/interface-element-button";

function updateButtonActions(interfaceElementButton, interfaceElementButtonPayload, schema) {
  if (!interfaceElementButton || !interfaceElementButtonPayload || !schema) return;

  updateExternalUrlNavigationAction(interfaceElementButton, interfaceElementButtonPayload, schema);
  updateInterfaceSectionNavigationAction(interfaceElementButton, interfaceElementButtonPayload, schema);
}

function updateExternalUrlNavigationAction(interfaceElementButton, interfaceElementButtonPayload, schema) {
  if (interfaceElementButton.action_type !== INTERFACE_ELEMENT_BUTTON_ACTION_TYPES.EXTERNAL_URL_NAVIGATION_ACTION) {
    interfaceElementButton.externalUrlNavigationAction?.destroy();
    return;
  }

  const externalUrlNavigationActionPayload = interfaceElementButtonPayload.included?.find(
    ({ type }) => type === 'external_url_navigation_action'
  );

  if (!externalUrlNavigationActionPayload) {
    interfaceElementButton.externalUrlNavigationAction?.destroy();
    interfaceElementButton.reload();

    return;
  }

  const { url, open_in_new_tab: openInNewTab } = externalUrlNavigationActionPayload.attributes;

  if (interfaceElementButton.externalUrlNavigationAction) {
    interfaceElementButton.externalUrlNavigationAction?.update({ openInNewTab, url });
  } else {
    const externalUrlNavigationAction = schema.externalUrlNavigationActions.create({
      interfaceElementButton,
      openInNewTab,
      url
    });

    interfaceElementButton.update({ externalUrlNavigationAction });
  }
}

function updateInterfaceSectionNavigationAction(interfaceElementButton, interfaceElementButtonPayload, schema) {
  if (interfaceElementButton.action_type !== INTERFACE_ELEMENT_BUTTON_ACTION_TYPES.INTERFACE_SECTION_NAVIGATION_ACTION) {
    interfaceElementButton.interfaceSectionNavigationAction?.destroy();
    return;
  }

  const interfaceSectionNavigationActionPayload = interfaceElementButtonPayload.included?.find(
    ({ type }) => type === 'interface_section_navigation_action'
  );

  if (!interfaceSectionNavigationActionPayload) {
    interfaceElementButton.interfaceSectionNavigationAction?.destroy();
    interfaceElementButton.reload();

    return;
  }

  const section = schema.sections.find(interfaceSectionNavigationActionPayload.relationships.section.data.id);

  if (interfaceElementButton.interfaceSectionNavigationAction) {
    interfaceElementButton.interfaceSectionNavigationAction.update({ section });
  } else {
    const interfaceSectionNavigationAction = schema.interfaceSectionNavigationActions.create({
      interfaceElementButton,
      section
    });

    interfaceElementButton.update({ interfaceSectionNavigationAction });
  }
}

export function routesForInterfaceElements(server) {
  server.patch('/interface_elements/:id', (schema, request) => {
    const interfaceElement = schema.interfaceElements.find(request.params.id);
    const { data, included } = JSON.parse(request.requestBody);

    if (data.relationships.section) {
      interfaceElement.section = schema.sections.find(data.relationships.section.data.id);
    }

    const interfaceElementButtonPayload = included?.find(({ type }) => type === 'interface_element_button');
    const interfaceElementButton = schema.interfaceElementButtons.find(interfaceElementButtonPayload?.id ?? '');

    interfaceElementButton?.update({
      ...interfaceElementButtonPayload.attributes,
      actionType: interfaceElementButtonPayload.attributes.action_type
    });

    updateButtonActions(interfaceElementButton, { ...interfaceElementButtonPayload, included: included }, schema);

    return interfaceElement.update(data.attributes);
  });

  server.post('/interface_elements', function(schema, request) {
    const { data: { attributes, relationships }, included } = JSON.parse(request.requestBody);

    const interfaceElement = schema.interfaceElements.create({
      elementType: attributes.element_type,
      gridPosition: attributes.grid_position,
      section: schema.sections.find(relationships.section.data.id)
    });

    if (interfaceElement.elementType === INTERFACE_ELEMENT_TYPE.BUTTON) {
      const interfaceElementButtonPayload = included[0];

      const interfaceElementButton = schema.interfaceElementButtons.create({
        label: interfaceElementButtonPayload.attributes.label,
        interfaceElement
      });
      interfaceElement.interfaceElementButton = interfaceElementButton;
    } else if (interfaceElement.elementType === INTERFACE_ELEMENT_TYPE.FIELD) {
      const interfaceElementFieldPayload = included[0];

      const interfaceElementField = schema.interfaceElementFields.create({
        interfaceElement
      });
      interfaceElement.interfaceElementField = interfaceElementField;
    }
    return this.serialize(interfaceElement);
  });
}
