import { module, test } from 'qunit';
import { setupTest } from 'frontend/tests/helpers';
import { RELATIONSHIP_KIND } from 'frontend/models/object-type-attribute';

module('Unit | Model | object-type-attribute', function(hooks) {
  setupTest(hooks);

  module('#effectiveInverseRelationshipKind', function() {
    test('it returns inverseRelationshipKind when inverse is false', function(assert) {
      const store = this.owner.lookup('service:store');
      const objectTypeAttribute = store.createRecord('object-type-attribute', {
        inverse: false,
        inverseRelationshipKind: RELATIONSHIP_KIND.SINGLE
      });

      assert.strictEqual(objectTypeAttribute.effectiveInverseRelationshipKind, RELATIONSHIP_KIND.SINGLE);
    });

    test('it returns the relationshipKind of the linked attribute when inverse is true and there is one linked attribute', function(assert) {
      const store = this.owner.lookup('service:store');
      const linkedAttribute = store.createRecord('object-type-attribute', {
        relationshipKind: RELATIONSHIP_KIND.SINGLE
      });

      const objectTypeAttribute = store.createRecord('object-type-attribute', {
        inverse: true,
        inverseRelationshipKind: RELATIONSHIP_KIND.MULTIPLE
      });

      objectTypeAttribute.linkedObjectTypeAttributes.push(linkedAttribute);

      assert.strictEqual(objectTypeAttribute.effectiveInverseRelationshipKind, RELATIONSHIP_KIND.SINGLE);
    });

    test('it returns MULTIPLE when inverse is true and there are multiple linked attributes', function(assert) {
      const store = this.owner.lookup('service:store');
      const linkedAttribute1 = store.createRecord('object-type-attribute', {
        relationshipKind: RELATIONSHIP_KIND.SINGLE
      });

      const linkedAttribute2 = store.createRecord('object-type-attribute', {
        relationshipKind: RELATIONSHIP_KIND.MULTIPLE
      });

      const objectTypeAttribute = store.createRecord('object-type-attribute', {
        inverse: true,
        inverseRelationshipKind: RELATIONSHIP_KIND.SINGLE
      });

      objectTypeAttribute.linkedObjectTypeAttributes.push(linkedAttribute1, linkedAttribute2);

      assert.strictEqual(objectTypeAttribute.effectiveInverseRelationshipKind, RELATIONSHIP_KIND.MULTIPLE);
    });

    test('it returns MULTIPLE when inverse is true and there are no linked attributes', function(assert) {
      const store = this.owner.lookup('service:store');
      const objectTypeAttribute = store.createRecord('object-type-attribute', {
        inverse: true,
        inverseRelationshipKind: RELATIONSHIP_KIND.SINGLE
      });

      assert.strictEqual(objectTypeAttribute.effectiveInverseRelationshipKind, RELATIONSHIP_KIND.MULTIPLE);
    });
  });
});
