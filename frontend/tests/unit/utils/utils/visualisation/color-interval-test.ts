import { colorForValue, ColorInterval } from 'frontend/utils/visualisation/color-interval';
import { module, test } from 'qunit';

module('Unit | Utils | visualisation/color-interval', function() {
  module('#colorForValue', function() {
    test('it returns the color for an interval with no min and max', function(assert) {
      const intervals = [
        new ColorInterval({ color: '#123456' })
      ];

      assert.strictEqual(colorForValue(20, intervals), '#123456');
    });

    test('it returns the color for the first matching interval', function(assert) {
      const intervals = [
        new ColorInterval({ color: '#111111', min: 5, max: 30 }),
        new ColorInterval({ color: '#222222', min: 25, max: 50 }),
        new ColorInterval({ color: '#333333', min: 0, max: 100 })
      ];

      assert.strictEqual(colorForValue(31, intervals), '#222222');
    });

    test('it returns color of the first interval when the value is on the boundary between them', function(assert) {
      const intervals = [
        new ColorInterval({ color: '#123456', min: 0, max: 10 }),
        new ColorInterval({ color: '#FFFFFF', min: 10, max: 20 })
      ];

      assert.strictEqual(colorForValue(10, intervals), '#123456');
    });

    test('it returns undefined when the value is not a number', function(assert) {
      const intervals = [
        new ColorInterval({ color: '#123456' })
      ];

      assert.strictEqual(colorForValue('20', intervals), undefined);
    });

    test('it returns undefined when the value is not within the interval', function(assert) {
      const intervals = [
        new ColorInterval({ color: '#123456', min: 0, max: 10 }),
        new ColorInterval({ color: '#FFFFFF', min: 20, max: 30 })
      ];

      assert.strictEqual(colorForValue(15, intervals), undefined);
    });
  });
});
