---
access: Access
active: Active
add_calculation: Add a calculation
add_custom_qr: Add custom QR/bar code
add_quick_access: Add a quick access
add_subteam: Add a subteam
add_to_team: Add to team
add_user: Add a user
admin_panel:
  invitations_page:
    delete_selected_invitations: Delete selected invitations
  page_title: Administration
  users_page:
    cannot_delete_yourself: You cannot delete yourself
    cannot_disable_yourself: You cannot deactivate yourself
    delete_selected_users: Delete selected users
    delete_user: Delete user
    disable_selected_users: Deactivate selected users
administrators: Administrators
aggregation: Calculation
aggregations:
  average: Average
  count: Number of records
  maximum: Maximum
  minimum: Minimum
  pluck: List
  sum: Sum
all: All
allow_creation_new_records: Allow creation of new records
allow_select_existing_records: Allow to select existing records
and: And
app_creation:
  action: Action
  actor: Actor
  create_app: Create app
  describe_app: Describe the app you want to build
  description: Description
  description_placeholder: Describe the app you want Houston to build. For instance "Create an app to manage internal audit non-conformities"
  field_or_relationship: Field / Relationship
  generate_with_ai: Generate with AI
  generating_your_app: Generating your app...
  key_roles: Key Roles
  kpis: KPIs
  new_app: New app
  parsing_mission_parameters: Parsing mission parameters
  relationship_to: Relationship to
  short_description: Short description
  step: Step
  step_by_step_process_description: Step-by-step process description
  summary: Summary
appearance: Appearance
apply: Apply
assistant:
  analysis_in_progress: Analyzing...
  ask_houston: Ask Houston
  chat_adjustments_question: Are there any adjustments you want me to do?
  chat_analysis_in_progress: Analysis in progress...
  chat_houston_ai: Houston AI
  chat_prompt_may_update_tabs: Your prompt may update other tabs
  chat_prompt_placeholder: Ask to add, remove, or update something in the current tab...
  data_limit_exceeded: Data limit exceeded. Consider using less input fields or less records.
  experimental: Experimental
  houston: Houston
  message_processing_error: Sorry, there was an error processing your message.
  prompt_may_update_tabs: Your prompt may update other tabs
  question: Question
  question_placeholder: What can I help you with? I can summarize, analyze, and much more.
  toggle_input_fields: Toggle input fields
attributes:
  background: Background
  created_at: Created at
  id: Global ID
  label: Label
  last_updated_by_id: Last updated by
  local_id: ID within object
  name: Name
  text: Text
  updated_at: Last updated at
  user_id: Created by
attributes_list: Fields to display as list
authentication_header_explanation:
  authentication: This authentication token should be supplied as the value of an "Authorization" header
  power_bi: ' <ol class="text-start"> <li> In Power BI Desktop, click "Get data", choose "Web" and select "Advanced" </li> <li> Paste the URL above in "URL parts" </li> <li> Add a header called "Authorization" in "HTTP request header parameters (optional)" and paste the Authentication token above as its value </li> <li> Confirm by clicking "OK" </li> </ol> '
automate: Automate
automation:
  action: Action
  add_all: Add all
  add_attribute: Add field
  add_input_attribute: Add input field
  add_output_attribute: Add output field
  add_recipient: Add recipient
  advanced_settings: Advanced settings
  automation: Automation
  automations: Automations
  create: a new record is created
  description_field: Describe the field to the AI
  destroy: a record is deleted
  email_address_from_field: Email address entered in a field
  enter_email: Enter email
  event: Event
  fill_fields_ai: Generate with AI
  frontend: a button is clicked
  get_static_data: Get static data
  input_attributes: Input fields
  integration: an integration is activated
  mentioned_users: Users mentioned in the record
  only_present_fields: Ignore empty fields
  only_updated_fields: Show modified fields only
  output_attributes: Output fields
  recipient: Recipient
  recipient_type: Recipient type
  recipients: Recipients
  remove_all: Remove all
  run_integration: Run integration
  save_before_execute: Save record before automation execution
  schedule_options:
    after_n_minutes: After {delay} minutes
    immediately: Immediately
    with_delay: With a delay
  scheduling: Scheduling
  select_buttons: Select buttons
  selected_users: Selected users
  send_based_on_field: Send based on a field value
  send_email: Send email
  send_immediately: Send immediately
  send_later: Send later
  send_webhook: Send webhook
  show_changes: Highlight changes in field values
  static_mail_address: Email address
  system_message: System message
  system_message_description: Instruct the AI how to behave
  team_of_record: Team of the record
  type_of_action: Type of action
  type_of_event: Type of event
  update: a record is updated
  update_attributes: Fields to update
  update_record: Update record
  user_selected_in_a_field: User selected in a field
  webhook_payload_fields: Fields to include in request payload
background: Background
bars: Bars
body: Body
calculation:
  calculated: Calculated
  create_calculation: Create calculation
  edit_calculation: Edit calculation
  enter_formula: Enter formula
  formula: Formula
  formula_explanation: |-
    <p>Formulas are powerful tools for performing calculations and analyzing data in Houston.</p><p>Formulas follow an Excel-like syntax:</p> <ul> <li>A formula is a (mathematical) expression that consists of numbers/text, mathematical operators, <strong>variables</strong> and <strong>functions</strong> that can be nested. For example:</li> <ul> <li>A + 10</li> <li>A / B * 2</li> <li>IF(A > B, TRUE, FALSE)</li> <li>CONCAT(A + B, ' kg in total')</li></ul>
    <li>A <strong>variable</strong> is a reference to a field in an object. The variable is an alphabetic character (A, B, C, and so on) that can be used in the formula. When the formula is evaluated, the variable is replaced by the value of the field in the record.</li>
    <li>A <strong>function</strong> is a built-in shortcut for performing a calculation or other operation on data. There are many functions available, and the list continues to grow. This assistant aims to help you find exactly the Houston function that suits your needs. Please reach <NAME_EMAIL> in case you see a need for an additional function.</li></ul>
  how_to_write_formula: How to write a formula in houston? »
  selectable_values_number_map: Value for calculations
  variable: Variable
cancel: Cancel
categories: Categories
charts:
  area: Area
  axis: Axis
  bar: Bar
  bar_stack_id: ID of the bar group
  category: Category
  data_serie_options: Data series
  date_field: Date field
  display_value: Display value
  legend: Legend
  line: Line
  opening_angle: Opening angle
  rotation: Rotation
  segment: Segment
  show_as_donut: Show as donut?
  target: Target
  targets:
    above: Above
    below: Below
    between: Between
    lower: Minimum
    outside: Outside
    range: Range
    single_value: Value
    upper: Maximum
  time_frame: Time period
  time_unit: Time unit
  x_axis: X-axis
  y_axis: Y-axis
choose_or_drop_files: Choose files or drop files here
clear: Clear
clone: Clone
close: Close
color: Color
columns: Columns
comment:
  comment: Comment
  deleted_user: "<b>Deleted user</b> commented"
  edited: edited
  loading: Loading comments ...
  placeholder: Leave a comment
  user: "<b>{user}</b> commented"
  you: "<b>You</b> commented"
condition:
  always: Always
  and_join: " and "
  blank_value: "(empty)"
  condition: Condition
  conditions: Conditions
  display_types:
    disable: disable
    filter_selectable: filter options
    hide: hide
    show: show
  field_with_name: field "{name}"
  if: If
  operators:
    blank: is blank
    changes: Changes
    changes_into: Changes into
    contains: contains
    equals: equals
    gt: is greater than
    in: in
    lt: is less than
    not_equals: does not equal
    presence: is filled in
  or_join: " or "
  result_selectable_explanation: show {values} in {subject}
  result_visibility_explanation: "{action} {subject}"
  rule: Rule
  rule_clause_explanation: '"{field}" {operator}'
  rule_clause_explanation_with_value: '"{field}" {operator} "{value}"'
  section_with_name: section "{name}"
  tab_with_name: Tab "{name}"
  then: then
  when: When
conditional_colors: Conditional formatting
conditional_displays: Conditional displays
confirm: Confirm
connected_elements: Connected elements
contact_support: Contact support
copied: Copied!
copy: Copy
create: Create
create_data_source: Create data source
create_interface: Create interface
create_lookup_field: Create a lookup field from a linked object
create_new_interface: Create new interface
create_new_object_type: Create new object
create_object_type: Create object
create_subteam: Create subteam
create_view: Create view
custom: Custom
custom_color: Custom color
data: Data
data_source:
  auth_token: Authentication token
datafeed_field_identifier: ID for data export
date_period: Date period
deactivated: Deactivated
default: Default
default_value: Default value
delete: Delete
delete_interface: Delete interface
delete_invitations_from_team: Remove selected invitations from this team
delete_object: Delete object
delete_record: Delete record
delete_selected: Delete selected
delete_selected_users_from_team: Remove selected users from this team
delete_user_from_team: Remove user from this team
delivery: Delivery
description: Description
dialog:
  are_you_sure: Are you sure?
  confirm_accept_requests: "{itemCount, plural, one {This join request} other {The selected join requests ({itemCount})} } will be accepted. Are you sure?"
  confirm_delete_attachment: This attachment ({attachmentName}) will be permanently deleted. Are you sure?
  confirm_delete_automation: This automation ({automationName}) will be permanently deleted. Are you sure?
  confirm_delete_comment: This comment will be permanently deleted. Are you sure?
  confirm_delete_condition: This condition will be permanently deleted. Are you sure?
  confirm_delete_empty_form_tab: This tab ({name}) will be permanently deleted. Are you sure?
  confirm_delete_empty_section: This section ({name}) will be permanently deleted. Are you sure?
  confirm_delete_form_element: This form element ({name}) will be permanently deleted. Are you sure?
  confirm_delete_form_tab: This tab ({name}) will be permanently deleted. Are you sure? Please type <b>{confirmText}</b> to permanently delete this tab.
  confirm_delete_interface: This interface ({name}) will be permanently deleted and cannot be recovered. Are you sure? This will result in irreparable DATA LOSS. Please type <b>{confirmText}</b> to permanently delete this interface.
  confirm_delete_interface_element: This element will be permanently deleted. Are you sure?
  confirm_delete_invites: "{itemCount, plural, one {This invitation} other {The selected invitations ({itemCount})} } will be deleted. Are you sure?"
  confirm_delete_link_tab: This tab and its content will be permanently deleted. Are you sure?
  confirm_delete_nested_grid: This element group and its content will be permanently deleted. Are you sure?
  confirm_delete_object_type: This object ({name}) and all its records will be permanently deleted and cannot be recovered. Are you sure? This will result in irreparable DATA LOSS. Please type <b>{confirmText}</b> to permanently delete this object.
  confirm_delete_object_type_role: This role ({objectTypeRoleName}) will be permanently deleted. Are you sure?
  confirm_delete_objects: "{itemCount, plural, one {This record} other {The selected records ({itemCount})} } will be permanently deleted and cannot be recovered. Are you sure?"
  confirm_delete_option: This option will be permanently deleted. Are you sure?
  confirm_delete_quick_access: This quick access ({quickAccessName}) will be permanently deleted. Are you sure?
  confirm_delete_section: This section ({name}) will be permanently deleted. Are you sure? Please type <b>{confirmText}</b> to permanently delete this section.
  confirm_delete_signature: This signature will be permanently deleted. Are you sure?
  confirm_delete_users: "{itemCount, plural, one {This user} other {The selected users ({itemCount})} } will be permanently deleted. Are you sure?"
  confirm_delete_users_from_team: "{itemCount, plural, one {This user} other {The selected users ({itemCount})} } will be permanently removed from this team. Are you sure?"
  confirm_delete_validation: This validation will be permanently deleted. Are you sure?
  confirm_delete_view: This view ({viewName}) will be permanently deleted. Are you sure?
  confirm_discard_custom_thumbnail: This will discard the current custom thumbnail. Are you sure?
  confirm_lose_rights: The changes that you made may affect users assigned to this role. Are you sure?
  confirm_overwrite_custom_qr: A custom QR/bar code has already been paired with this record. Do you want to overwrite it?
  confirm_publish_object_type: This object still contains missing information. This may impact its behaviour and/or usability. Are you sure?
  confirm_recalculation:
    message: The formula attached to this field is changed. Do you want to recalculate the values of this field for all existing records?<br>This recalculation will not trigger any automations.
    title: Recalculate?
  confirm_reject_requests: "{itemCount, plural, one {This join request} other {The selected join requests ({itemCount})} } will be rejected. Are you sure?"
  confirm_removal_current_user_as_interface_owner: You are removed from the list of owners, and you will no longer be able to edit this interface. Are you sure?
  confirm_removal_current_user_as_object_type_owner: You are removed from the list of owners, and you will no longer be able to edit this object. Are you sure?
  confirm_remove_interface: Are you sure you want to remove <b>{name}</b> from <b>{teamName}</b>?
  confirm_remove_object_type: Are you sure you want to remove <b>{name}</b> from <b>{teamName}</b>?
  confirm_trigger_event: This record has unsaved changes. Do you wish to save the record and execute the action?
  confirm_unpublish_interface: Are you sure you want to cancel the publication of <b>{name}</b> in this team? Members will not be able to access it nor its data. Are you sure?
  confirm_unpublish_object_type: Are you sure you want to cancel the publication of <b>{name}</b> in this team? Members will not be able to access it nor its data. Are you sure?
  confirm_unsaved_changes_automation:
    text_close: This automation has unsaved changes. Are you sure you want to close without saving?
    text_save: This automation has unsaved changes. Are you sure you want to save?
  confirm_unsaved_changes_automation_action:
    text_close: This action has unsaved changes. Are you sure you want to close without saving?
    text_save: This action has unsaved changes. Are you sure you want to save?
  confirm_unsaved_changes_conditional_display: This condition has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_changes_interface: This interface has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_changes_object: This record has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_changes_record_picker: This record has unsaved changes. Are you sure you want to select a different record and discard the unsaved changes?
  confirm_unsaved_changes_role: This role has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_changes_show_route_record_picker: Unsaved changes exist for {records}. Are you sure you want to discard them?
  confirm_unsaved_changes_team: This team has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_changes_thumbnail: This thumbnail has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_form: This form has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_interface: This interface has unsaved changes. Are you sure you want to close without saving?
  confirm_unsaved_translations:
    target_text: The target translation is not saved. Are you sure you want to change the target locale without saving?
    text: These translations are not saved. Are you sure you want to close without saving?
  confirm_unshare_object_type: Are you sure you want to unshare <b>{name}</b> from <b>{teamName}</b>?
  confirmation_required: Confirmation required
display: Display
display_as: Display as
do: Do
done: Done
download: Download
dynamic: Dynamic
dynamic_options:
  current_user: Logged in user
  now: Current date and time
  today: Current date
edit: Edit
edit_filter_rules: Edit filters
edit_object: Edit object
edit_shortcut: Edit link
edit_sort_rules: Edit sort rules
edit_tab: Edit tab
edit_view: Edit view
embed: Embed
embed_modal:
  show_tabs: Display tab selector
  show_toolbar: Display toolbar
enable_scanning: Enable scanning
enter_number: Enter number
enter_something: Enter something
enter_text: Enter text
enter_url: Enter URL
error:
  empty_interface_owners_list: The interface should have at least one owner. Please add a new owner.
  empty_object_type_owners_list: The object should have at least one owner. Please add a new owner.
  generic_server_message: There was an error processing the request.
  object_type_attributes_limit: You've reached the maximum of {object_type_attributes_limit} fields per object. Please consider creating separate objects with fewer fields each, and link those objects.
  title: Error
export:
  cancelled: Export cancelled
  completed: Export successful
  export: Export
  failed: Export failed
  in_progress: Export in progress
'false': 'No'
field: Field
field_calculation: Field calculation
field_name: Field name
field_name_placeholder: Enter field name
field_to_display: Field value to display
field_to_search_by: Field to search by
filters_applied: "{amount, plural, =0 {No filters} =1 {# filter} other {# filters}} applied"
fit_element: Fit within element (Displaying only the first preview)
form: Form
form_builder:
  label_for_hidden: Hide field in this variant
  label_for_locked: Force display in variants
  object_type_relationship_options:
    cardinalities:
      multiple: One <baseObjectType> can be linked to multiple <targetObjectType>
      single: One <baseObjectType> can be linked to at most one <targetObjectType>
    forward: Forward relationship
    from: From
    inverse: Inverse relationship
    object_already_linked: Object already linked
    object_not_yet_linked: Object not yet linked
    to: To
  selectable_value_options:
    allow_variant_options: Possibility to add options in variants
formula:
  ABS:
    description: Returns the absolute value of a number, a number without its sign.
    syntax_description: "<ul><li>Number: is the real number for which to find the absolute value.</li></ul>"
  ALL:
    description: Checks whether all arguments are TRUE, and returns TRUE if all of the arguments are TRUE.
    syntax_description: "<ul><li>List: list of values.</li> <li>Parameter: parameter name that is used in the function.</li> <li>Function: applied to each value(parameter) and checks if all values meet a certain condition.</li></ul>"
  AND:
    description: Checks whether all arguments are TRUE, and returns TRUE if all arguments are TRUE.
    syntax_description: "<ul><li>Logical1, Logical2,...: any values or expressions that can be evaluated as TRUE or FALSE.</li></ul>"
  ANY:
    description: Checks whether all arguments are TRUE, and returns TRUE if any of the arguments are TRUE.
    syntax_description: "<ul><li>List: list of values.</li> <li>Parameter: parameter name that is used in the function.</li> <li>Function: applied to each value(parameter) and checks if any value meets a certain condition.</li></ul>"
  AVG:
    description: Returns the average (arithmetic mean) of its arguments.
    syntax_description: "<ul><li>Number1, Number2, ...: numeric arguments for which to calculate the average.</li></ul>"
  CONCAT:
    description: Concatenates a list of text strings.
    syntax_description: "<ul><li>Text1, Text2, ...: strings to be joined to a single text string.</li></ul>"
  CONTAINS:
    description: Checks whether a subtext is within a text and returns TRUE or FALSE.
    syntax_description: "<ul><li>Find_text: is the text to find.</li> <li>Within_text: is the text containing the text to find.</li></ul>"
  COS:
    description: Returns the cosine of an angle.
    syntax_description: "<ul><li>Number: is the angle in radians for which to calculate the cosine.</li></ul>"
  COUNT:
    description: Counts the number of values or length of a string.
    syntax_description: "<ul><li>Value1, Value2, ...: values that can be a variety of different data types.</li></ul>"
  COUNTIF:
    description: Counts all values of a list that meet a certain condition.
    syntax_description: '<ul><li>List: list of values.</li> <li>Function: applied to each value(parameter) and counts each value that meets a certain condition. The parameter name that should be used in the function is: "value".</li></ul>'
  DURATION:
    description: Returns a value to add or substract from a date.
    syntax_description: "<ul><li>Number: is the amount of units.</li><li>Unit: 'years', 'months' and 'days' are the available units.</li></ul>"
  EXP:
    description: Returns e raised to the power of a given number.
    syntax_description: "<ul><li>Number: is the exponent applied to the base e. The constant e equals 2.71828182845904, the base of the natural logarithm.</li></ul>"
  FILTER:
    description: Filters a list of values.
    syntax_description: "<ul><li>List: list of values.</li> <li>Parameter: parameter name that is used in the function.</li> <li>Function: applied to each value(parameter) and will filter out the values that do not meet a certain condition.</li></ul>"
  FIND:
    description: Returns the position of a subtext within a text.
    syntax_description: "<ul><li>Find_text: is the text to find.</li> <li>Within_text: is the text containing the text to find.</li></ul>"
  IF:
    description: Checks whether a condition has been met, and returns one value if TRUE, and another value if FALSE.
    syntax_description: "<ul><li>Logical_test: is any value or expression that can be evaluated as TRUE or FALSE.</li> <li>Value_if_true: is the value that will be returned if Logical_test is TRUE. If omitted, TRUE will be returned.</li> <li>Value_if_false: is the value that will be returned if Logical_test is FALSE. If omitted, FALSE will be returned.</li></ul>"
  LEFT:
    description: Returns the specified number of characters from the start of a text string.
    syntax_description: "<ul><li>Text: is the text string containing the characters to extract.</li> <li>Num_chars: specifies how many characters to extract.</li></ul>"
  LEN:
    description: Returns the number of characters in a text string.
    syntax_description: "<ul><li>Text: is the text for which to calculate the length. Spaces count as characters.</li></ul>"
  LOG:
    description: Returns the logarithm of a number to the base you specify.
    syntax_description: "<ul><li>Number: is the positive real number for which you want the logarithm.</li><li>Base: is the base of the logarithm; e if omitted.</li></ul>"
  LOG10:
    description: Returns the base-10 logarithm of a number.
    syntax_description: "<ul><li>Number: is the positive real number for which you want the base-10 logarithm.</li></ul>"
  MAP:
    description: Returns a list of values formed by 'mapping' each value in the list to a new value by applying a function.
    syntax_description: "<ul><li>List: list of values.</li> <li>Parameter: parameter name that is used in the function.</li> <li>Function: applied to each value(parameter) and will return the new calculated value.</li></ul>"
  MAX:
    description: Returns the largest value in a set of values.
    syntax_description: "<ul><li>Number1, Number2, ...: numbers for which to find the maximum.</li></ul>"
  MAXOCCURRENCE:
    description: Returns the nth most common value in a list.
    syntax_description: "<ul><li>List:list of values.</li> <li>n: index of the most common value to find.</li></ul>"
  MID:
    description: Returns the characters from the middle of a text string, given a starting position and length.
    syntax_description: "<ul><li>Text: is the text string from which to extract the characters.</li> <li>Start_num: is the position of the first character to extract. The first character in Text is 1.</li> <li>Num_chars: specifies how many characters to return from Text.</li></ul>"
  MIN:
    description: Returns the smallest number in a set of values.
    syntax_description: "<ul><li>Number1, Number2, ...: numbers for which to find the minimum.</li></ul>"
  NOT:
    description: Changes FALSE to TRUE, or TRUE to FALSE.
    syntax_description: "<ul><li>Logical: is a value or expression that can be evaluated to TRUE or FALSE.</li></ul>"
  OR:
    description: Checks whether any of the arguments are TRUE, and returns TRUE or FALSE. Only returns FALSE if all arguments are FALSE.
    syntax_description: "<ul><li>Logical1, Logical2, ...: conditions to test that can be either TRUE or FALSE.</li></ul>"
  RIGHT:
    description: Returns the specified number of characters from the end of a text string.
    syntax_description: "<ul><li>Text: is the text string containing the characters to extract.</li> <li>Num_chars: specifies how many characters to extract.</li></ul>"
  ROUND:
    description: Rounds a number to a specified number of digits.
    syntax_description: "<ul><li>Number: is the number to round.</li> <li>Num_digits: is the number of digits to which to round.</li></ul>"
  ROUNDDOWN:
    description: Rounds a number down, towards zero.
    syntax_description: "<ul><li>Number: is any real number to round down.</li> <li>Num_digits: is the number of digits to which to round to.</li></ul>"
  ROUNDUP:
    description: Rounds a number up, away from zero.
    syntax_description: "<ul><li>Number: is any real number to round up.</li> <li>Num_digits: is the number of digits to which to round.</li></ul>"
  SIN:
    description: Returns the sine of an angle.
    syntax_description: "<ul><li>Number: is the angle in radians for which to calculate the sine.</li></ul>"
  SQRT:
    description: Returns the square root of a number.
    syntax_description: "<ul><li>Number: is the number for which you want the square root.</ul></li>"
  STDEV:
    description: Returns standard deviation based on a sample.
    syntax_description: "<ul><li>Number1, Number2, ...: numbers for which to find the sample standard deviation.</li></ul>"
  SUBSTITUTE:
    description: Replaces existing text with new text in a text string.
    syntax_description: "<ul><li>Text: is the text in which to substitute characters.</li> <li>Old_text: is the existing text to replace.</li> <li>New_text: is the text to replace Old_text with.</li></ul>"
  SUM:
    description: Adds all of the numbers.
    syntax_description: "<ul><li>Number1, Number2, ...: the numbers to add.</li></ul>"
  SWITCH:
    description: Evaluates an expression against a list of values and returns the result corresponding to the first matching value. If there is no match, an optional default value is returned.
    syntax_description: "<ul><li>Expression: is an expression to be evaluated.</li><li>Value1, Value2, ...: value to match whith the expression.</li><li>Result1, Result2, ...: value to return if there's a match.</li><li>Default value: Value to return if there's no match.</li></ul>"
  TAN:
    description: Returns the tangent of an angle.
    syntax_description: "<ul><li>Number: is the angle in radians for which to calculate the tangent.</li></ul>"
  VAR:
    description: Returns variance based on a sample.
    syntax_description: "<ul><li>Number1, Number2, ...: numbers for which to find the sample variance.</li></ul>"
  XOR:
    description: Returns a logical ’Exclusive OR’ of all arguments.
    syntax_description: "<ul><li>Logical1, Logical2, ...: conditions to test that can be either TRUE or FALSE.</li></ul>"
  syntax: Syntax
function_assistant: Function assistant
general: General
got_it: Got it
grid: Grid
grouped_by: Grouped by
help: Help
hide_all: Hide all
how_to_use_in: How to use in
icon: Icon
include: Include
include_descendants: Include descendants
incorrect_qr_format: Incorrect QR format
input: Fields
input_type:
  attachment: Attachment
  button: Button
  checkbox: Checkbox
  checkmark: Checkmark
  date: Date
  datetime: Date and time
  description: Fixed text
  descriptive_attachment: Fixed images / media
  dropdown_list: Dropdown list
  linked_object: Linked Houston object
  lookup: Field from a linked Houston object
  number: Number
  radio: Radio
  range_select: Range select
  rich_text: Rich text
  short_text: Short text
  signature: Signature
  switch: Switch
  textarea: Text area
  type: Field type
  url: URL
  user: User
insert_function: Insert function
interface:
  attachment: Fixed images / media
  button: Button
  calendar_chart: Calendar chart
  contact_text: This element is not yet configurable in the UI. Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a>
  filter: Filter
  filter_through_element: AND filter through
  gage_chart: Gauge chart
  html: HTML
  html_placeholder: Enter HTML
  interface: Interface
  interface_not_available: This interface is not available in the selected team.
  linebar_chart: Line/Bar/Area chart
  links: Links
  nested_grid: Element group
  number: Number
  pie_chart: Pie/Donut chart
  pyramid_chart: Pyramid chart
  radar_chart: Radar chart
  read_only: Read-only
  record_in_record_picker: Record "{record}" in record picker "{record_picker}"
  record_picker: Record picker
  show_label: Show label
  source: Source
  text: Text
  unit: Unit
interface_editor:
  action_type_selector:
    data_change: Change data
    delete_record_action: Delete a record
    external_url_navigation: Open external URL
    interface_section_navigation: Go to interface tab
    navigation: Navigate
    place_holder: Select an action type
    save_record_action: Save a record
    trigger_automation_action: Trigger an automation
  actions:
    navigation_action:
      open_in_new_tab: Open in new tab
interfaces_managed_by_me: Interfaces managed by me
invitation:
  add_message: Add a message... (recommended)
  date_and_time: Invitation date & time
  delete_invite: Delete invitation
  email: Email
  invite_user_by_email: Invite user by email
  invites: Invitations
  message: Message
  send_invite: Send an invite
  toast:
    invitation_sent: Invitation sent
    title: User invite
    user_already_in_team: This user is already a member of the team
    user_found: This email address corresponds to user {name} who has now been added to the team
label_display: Label display
language: Language
languages:
  en: English
  es: Spanish
  fr: French
  nl: Dutch
last_activity: Last activity
layout: Layout
less: less
linked_fields: Linked Fields
linked_object: Linked object
linked_object_field: Linked object field
linked_objects: Linked objects
loading: Loading...
lookup_from: Lookup from
lookup_through_field: through field "{field}"
max: Max
members: Members
memberships_awaiting_approval: Memberships awaiting approval
message: Message
min: Min
minutes: Minutes
missing_video_permission: No camera is present or permission was not given.
more: more
more_info: More info
move: Move
move_form_element_modal_title: Move field to another tab
move_interface_modal_title: Move element to ...
multiple: Multiple
my_teams: My teams
navigation:
  home: Home
  interfaces: Interfaces
  not_found: Not Found
  objects: Objects
new: New
new_interface: New interface
new_object_type: New object
new_section: New Section
new_shortcut: New link
new_tab: New Tab
new_version_info: A new version is available.
new_version_refresh: Please refresh
no_access: No access
no_access_tooltip: You are not a member of this team
no_filters: No filters
no_grouped_attribute_warning: No field selected for grouping records.
no_home_page_description: No home page defined for this team.
no_items_to_display: No items to display.
no_results_found: No results found
no_team_filters: No team filters
non_editable_view: This view is not editable
none: None
not_active: Not active
not_found_description: The page you tried to access was not found.
notify_team_admins: Notify team admins
object_form_link: Link to form
object_hyperlink: Link to record
object_not_available: This object is not available in the selected team.
object_print_modal:
  all_tabs: All tabs
  current_tab: Current tab
object_type: Object
object_type_description:
  editor: Information for object editors
  empty: "(No description yet)"
  user: Information for all users
object_type_share_violation: The object has already been shared with the team.
objects_managed_by_me: Objects managed by me
ok: Ok
'on': 'On'
operations:
  after: After
  before: Before
  blank: Is blank
  contains: Contains
  equals: Equals
  greater_than: Greater than
  in: In
  lower_than: Less than
  not_contains: Does not contain
  not_equals: Does not equal
  presence: Is filled in
  within: In
operator: Operator
option: Option
options: Options
or: Or
other: Other
owned: Managed by me
owners: Owners
personal: Personal
pin_tab: Pin a tab
placeholder: Placeholder
position:
  inside: Inside
  top: On top
power_bi: Power BI
print: Print
properties: Properties
publication_settings: Publication settings
publish: Publish
publish_dialog:
  default_assignment: Automatically assign Creator and Editor roles to this team
  descendants: This team and all its descendants
  interface: Publish interface
  no_default_assignment: Let me choose the role assignments
  object: Publish object
  redirect: Bring me to the Roles page after publishing.
  role_info: In order to create, update and delete records for an object, users need to have a role having such right.
  team: This team only
publish_in: Publish in
publish_warnings:
  automation: This automation has missing information. This may impact its behaviour and/or usability.
  automations: There are one or multiple automations which have missing information. This may impact their behaviour and/or usability.
  form_element: This field has missing information. This may impact its behaviour and/or usability.
  form_elements: There are one or multiple fields which have missing information. This may impact their behaviour and/or usability.
  role: This role has missing information. This may impact its behaviour and/or usability.
  roles: There are one or multiple roles which have missing information. This may impact their behaviour and/or usability.
published: Published
qr_bar_code: QR/bar code
qr_download: Download Houston QR code
qr_modal:
  placeholder: Enter code
  title: Enter QR/bar code for record
query:
  current: Current
  current_month: Current month
  current_week: Current week
  current_year: Current year
  day: Day
  exact_date: Exact date
  filter: Filter
  filter_by_this_field: Filter by this field
  from: From
  from_team: From team
  last_month: Last month
  last_week: Last week
  last_year: Last year
  linked_with: Linked with
  month: Month
  new_filter_condition: New filter
  new_sort_option: New sort rule
  new_team_filter_condition: New team filter
  new_view: New view
  next: Next
  no_filters_applied: There are no filters applied.
  no_sorts_applied: There are no sort rules applied.
  previous: Previous
  select_option: Select an option
  sort: Sort
  sort_asc: Sort ascending
  sort_desc: Sort descending
  to: To
  today: Today
  today_minus: Today minus
  today_plus: Today plus
  week: Week
  where: Where
  year: Year
quick_access: Quick access
recently_used: Recently used
records_shared: Records shared with this team
records_subteams: Records from subteams
refresh: Refresh
remove_interface_from_team: Remove interface from this team
remove_object_from_team: Remove object from this team
revision_history:
  title: Revision history
  user:
    create: "<b>{user}</b> created this record"
    update: "<b>{user}</b> edited this record"
  you:
    create: "<b>You</b> created this record"
    update: "<b>You</b> edited this record"
role:
  access_rights: Access rights
  assigned_to: Assigned to
  author_of_a_record: Author of a record
  edit_role: Edit role
  fixed_set_of_users: Fixed set of users
  new_role: New role
  role: Role
  through_attribute: Through field
  users_from_specific_field: Users from specific field
roles: Roles
root_level: Root level
save: Save
saving: Saving
scan: Scan
search: Search
search_icons: Search icons
section: Section
section_name: Section name
select_color: Select color
select_column_header: Select a column header
select_column_header_description: Select a field to use for grouping records in this {viewType} view
select_create_which_object: Select which object you want to create
select_date: Select date
select_field: Select field
select_filter: Select filter
select_object: Select an object
select_placeholder: Select something
select_record: Select a record
select_tab: Select tab
select_team: Select a team
select_user: Select a user
selectable_values: Selectable values
send_email_to: Send email to
settings: Settings
share: Share
share_interface_with_new_teams: Share interface with teams
share_object_with_new_teams: Share object with teams
show_all: Show all
show_background_image: Show background image
show_details: Show details
show_details_in_header: Show details in header
show_field_names: Show field names
show_more: Show more
show_options: 'Show options:'
show_thumbnail_icon: Show object icon
sign_here: Sign here
sign_out: Sign out
single: Single
size_limit_warning: Size limit exceeded
someone: Someone
sort_rules_applied: "{amount, plural, =0 {No sort rules} =1 {# sort rule} other {# sort rules}} applied"
start_houston: Start using Houston
status: Status
step: Step
stop_scanning: Stop scanning
structure: Structure
subject: Subject
submit: Submit
subteams: Subteams
subtitle: Subtitle
support: Support
system: System
tab: Tab
tab_name: Tab name
team: Team
team_filterable: Filter records based on the selected team
team_filterable_user_relationship: Filter users based on the selected team
teams: Teams
teams_to_scope_data_to: Data from
theme: Display mode
theme_dark: Dark (experimental)
theme_light: Light
thumbnail:
  background_image: Background image
  card: Card
  date_time_display_mode:
    date_and_time: Date and time
    date_only: Date only
    time_only: Time only
  default_card: Used by default in Gallery.
  default_multiple_rows: Used by default in Lists and Forms.
  default_single_column: Can be used in forms.
  default_single_row: Used by default in Tables.
  edit_thumbnail: Edit thumbnail
  grid: Multiple rows
  horizontal: Single row
  prefix: Prefix
  row: Row
  selected_field: Selected thumbnail field
  show_title_row: Show title row
  subtitle: Subtitle
  suffix: Suffix
  thumbnail: Thumbnail
  title: Title
  type: Thumbnail type
  vertical: Single column
thumbnails: Thumbnails
time_unit: Time unit
timeline:
  end_date: End date
  start_date: Start date
timeline_settings: Timeline settings
title: Title
title_field: Title field
translation_management: Translation management
'true': 'Yes'
type: Type
unassigned: Unassigned
uncategorized: Uncategorized
unlink_selected: Disconnect selected records
unpublish: Cancel publication
unpublished: Awaiting publication
unshare: Unshare
uploader:
  action: Action
  one_or_more_files: Upload 1 or more files
  take_picture: Take picture
  take_video: Record video
  uploading: Uploading ...
used_in: Used in
user_account:
  activate: Activate user account
  deactivate: Deactivate user account
user_guide: User guide
user_join_requests:
  approvals: Approvals
  approve: Approve
  approve_requests: Approve selection
  awaiting_approval: Awaiting approval
  date_and_time: Request date and time
  reject: Reject
  reject_requests: Reject selection
  toast:
    body: We have asked the administrators to approve your request to join.
    title: Join request sent
user_not_found: User not found?
users: Users
validation: Validation
validation_type:
  equals: should be equal to
  greater_than: should be greater than
  less_than: should be less than
  max_length: should not be longer than
  min_length: should not be shorter than
  other_than: should be other than
  presence: should be filled in
validations: Validations
value: Value
value_set_on_creation: Value set on creation
variants:
  master: Master
view: View
visibility: Visibility
visualisation:
  multiObjectList: Multi-object list
  objectCalendar: Calendar
  objectGallery: Gallery
  objectKanban: Kanban
  objectList: List
  objectTable: Table
  objectTimeline: Timeline
warning_changes_object_type: Changes to this object will affect all records for this object. Proceed with care!
warning_optional_field: This field is optional and might not be available in variants.
welcome: Welcome to Houston. To start using Houston, please request to join a team
