---
access: Acceso
active: Activo
add_calculation: Agregar un cálculo
add_custom_qr: Agregar código QR/de barras
add_quick_access: Añadir un acceso rápido
add_subteam: Agregar un subequipo
add_to_team: Ag<PERSON>gar al equipo
add_user: Agregar un usuario
admin_panel:
  invitations_page:
    delete_selected_invitations: Eliminar invitaciones seleccionadas
  page_title: Administración
  users_page:
    cannot_delete_yourself: No puede eliminarse a sí mismo
    cannot_disable_yourself: No puede desactivarse a sí mismo
    delete_selected_users: Eliminar usuarios seleccionados
    delete_user: Eliminar usuario
    disable_selected_users: Desactivar usuarios seleccionados
administrators: Administradores
aggregation: Cálculo
aggregations:
  average: Promedio
  count: Recuento de registros
  maximum: Máximo
  minimum: Mínimo
  pluck: Lista
  sum: Suma
all: Todo
allow_creation_new_records: Permitir la creación de nuevos registros
allow_select_existing_records: Permitir seleccionar registros existentes
and: "Y"
appearance: Apariencia
apply: Aplicar
assistant:
  analysis_in_progress: <PERSON><PERSON><PERSON><PERSON>...
  ask_houston: Pregúntele a Houston
  data_limit_exceeded: Se ha excedido el límite de datos. Considere utilizar menos campos de entrada o menos registros.
  experimental: Experimental
  houston: Houston
  question: Pregunta
  question_placeholder: "¿En qué puedo ayudarte? Puedo resumir, analizar y mucho más."
  toggle_input_fields: Activar o desactivar campos de entrada
attributes:
  background: Fondo
  created_at: Creado en
  id: ID global
  label: Etiqueta
  last_updated_by_id: Última actualización por
  local_id: ID en el objeto
  name: Nombre
  text: Texto
  updated_at: Última actualización en
  user_id: Creado por
attributes_list: Campos para mostrar como lista
authentication_header_explanation:
  authentication: Este token de autenticación debe proporcionarse como el valor de un encabezado "Authorization"
  power_bi: <i>(En Inglés)</i><ol class="text-start"><li>In Power BI Desktop, click "Get data", choose "Web" and select "Advanced"</li><li>Paste the URL above in "URL parts" </li><li>Add a header called "Authorization" in "HTTP request header parameters (optional)" and paste the Authentication token above as its value</li><li>Confirm by clicking "OK"</li></ol>
automate: Automatizar
automation:
  action: Acción
  add_all: Añadir todo
  add_attribute: Agregar campo
  add_input_attribute: Agregar campo de entrada
  add_output_attribute: Agregar campo de salida
  add_recipient: Agregar recipiente
  advanced_settings: Ajustes avanzados
  automation: Automatización
  automations: Automatizaciones
  create: se crea un nuevo registro
  description_field: Describe el campo a la IA.
  destroy: se elimina un registro
  email_address_from_field: Dirección de correo electrónico ingresada en un campo
  enter_email: Ingrese correo electrónico
  event: Evento
  fill_fields_ai: Generar con IA
  frontend: se hace clic en un botón
  get_static_data: Obtener datos estáticos
  input_attributes: Campos de entrada
  integration: se activa una integración
  mentioned_users: Usuarios mencionados en el registro
  only_present_fields: Ignorar campos vacíos
  only_updated_fields: Mostrar sólo los campos modificados
  output_attributes: Campos de salida
  recipient: Recipiente
  recipient_type: Tipo de recipiente
  recipients: Destinatarios
  remove_all: Retirar todo
  run_integration: Ejecutar integración
  save_before_execute: Guardar registro antes de la ejecución de la automatización
  schedule_options:
    after_n_minutes: Después de {delay} minutos
    immediately: Inmediatamente
    with_delay: Con un retraso
  scheduling: Programación
  select_buttons: Seleccione los botones
  selected_users: Usuarios seleccionados
  send_based_on_field: Enviar en función de un valor de campo
  send_email: Enviar correo electrónico
  send_immediately: enviar inmediatamente
  send_later: Envíalo después
  show_changes: Resaltar los cambios en los valores de los campos
  static_mail_address: Dirección de correo electrónico
  system_message: Mensaje del sistema
  system_message_description: Instruye a la IA cómo comportarse
  team_of_record: Equipo del registro
  type_of_action: Tipo de acción
  type_of_event: Tipo de evento
  update: se actualiza un registro
  update_attributes: Campos a actualizar
  update_record: Actualizar registro
  user_selected_in_a_field: Usuario seleccionado en un campo
background: Fondo
bars: Verja
body: Cuerpo
calculation:
  calculated: Calculado
  create_calculation: Crear cálculo
  edit_calculation: Editar cálculo
  enter_formula: Ingresar fórmula
  formula: Fórmula
  formula_explanation: |-
    <p>Las fórmulas son herramientas poderosas para realizar cálculos y analizar datos en Houston.</p><p>Las fórmulas siguen una sintaxis similar a la de Excel:</p> <ul> <li>Una fórmula es una expresión (matemática) que consta de números/texto, operadores matemáticos, <strong>variables</strong> y <strong>funciones</strong> que se pueden anidar. Por ejemplo:</li> <ul> <li>A + 10</li> <li>A / B * 2</li> <li>SI(A > B, VERDADERO, FALSO)</li> < li>CONCAT(A + B, 'kg en total')</li></ul>
    <li>Una <strong>variable</strong> es una referencia a un campo en un objeto. La variable es un carácter alfabético (A, B, C, etc.) que se puede utilizar en la fórmula. Cuando se evalúa la fórmula, la variable se reemplaza por el valor del campo en el registro.</li>
    <li>Una <strong>función</strong> es un acceso directo integrado para realizar un cálculo u otra operación con datos. Hay muchas funciones disponibles y la lista sigue creciendo. Este asistente tiene como objetivo ayudarle a encontrar exactamente la función de Houston que se adapte a sus necesidades. Comuní<NAME_EMAIL> en caso de que vea la necesidad de una función adicional.</li></ul>
  how_to_write_formula: "¿Cómo escribir una fórmula en Houston? »"
  selectable_values_number_map: Valor para los cálculos
  variable: Variable
cancel: Cancelar
categories: Categorías
charts:
  area: Área
  axis: Eje
  bar: Barra
  bar_stack_id: ID del grupo de barras
  category: Categoría
  data_serie_options: Serie de datos
  date_field: Campo de fecha
  display_value: Mostrar valor
  legend: Leyenda
  line: Línea
  opening_angle: Ángulo de apertura
  rotation: Rotación
  segment: Segmento
  show_as_donut: "¿Mostrar como donut?"
  target: Objetivo
  targets:
    above: Arriba
    below: Abajo
    between: Entre
    lower: Mínimo
    outside: Fuera
    range: Intervalo
    single_value: Valor
    upper: Máximo
  time_frame: Periodo de tiempo
  time_unit: Unidad de tiempo
  x_axis: Eje x
  y_axis: Eje y
choose_or_drop_files: Elija archivos o suéltelos aquí
clear: Borrar
clone: Clonar
close: Cerrar
color: Color
columns: Columnas
comment:
  comment: Comentario
  deleted_user: "<b>Usuario eliminado</b> ha comentado"
  edited: editado
  loading: Cargando comentarios ...
  placeholder: Deja un comentario
  user: "<b>{user}</b> ha comentado"
  you: "<b>Usted</b> ha comentado"
condition:
  always: Siempre
  and_join: " y "
  blank_value: "(vacío)"
  condition: Condición
  conditions: Condiciones
  display_types:
    disable: desactiva
    filter_selectable: filtrar las opciones
    hide: oculta
    show: muestra
  field_with_name: campo "{name}"
  if: Si
  operators:
    blank: es blanco
    changes: Se cambia
    changes_into: Se cambia en
    contains: contiene
    equals: es igual a
    gt: es mayor que
    in: en
    lt: es menor que
    not_equals: no es igual
    presence: es llenado
  or_join: " o "
  result_selectable_explanation: muestra {values} en {subject}
  result_visibility_explanation: "{action} {subject}"
  rule: Criterio
  rule_clause_explanation: '"{field}" {operator}'
  rule_clause_explanation_with_value: '"{field}" {operator} "{value}"'
  section_with_name: sección "{name}"
  tab_with_name: Pestaña "{name}"
  then: entonces
  when: Si
conditional_colors: Formato condicional
conditional_displays: Visualizaciones condicionales
confirm: Confirmar
connected_elements: Elementos conectados
contact_support: Contactar soporte
copied: "¡Copiado!"
copy: Copiar
create: Crear
create_data_source: Crear fuente de datos
create_interface: Crear interfaz
create_lookup_field: Crear un campo de búsqueda a partir de un objeto vinculado
create_new_interface: Crear nueva interfaz
create_new_object_type: Crear nuevo objeto
create_object_type: Crear objeto
create_subteam: Crear subequipo
create_view: Crear vista
custom: Personalizado
custom_color: Color personalizado
data: Datos
data_source:
  auth_token: Token de autenticación
datafeed_field_identifier: ID para exportación de datos
date_period: Periodo de fecha
deactivated: Desactivado
default: Por defecto
default_value: Valor por defecto
delete: Eliminar
delete_interface: Eliminar interfaz
delete_invitations_from_team: Retirar invitaciones seleccionadas de este equipo
delete_object: Eliminar objeto
delete_record: Eliminar registro
delete_selected: Eliminar seleccionado
delete_selected_users_from_team: Retirar usuarios seleccionados de este equipo
delete_user_from_team: Retirar usuario de este equipo
delivery: Entrega
description: Descripción
dialog:
  are_you_sure: "¿Está seguro?"
  confirm_accept_requests: "{itemCount, plural, one {Se aceptará esta solicitud de unión} other {Se aceptarán las solicitudes de unión seleccionadas ({itemCount})} }. ¿Está seguro?"
  confirm_delete_attachment: Este archivo adjunto ({attachmentName}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_automation: Esta automatización ({automationName}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_comment: Esta comentario se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_condition: Esta condición se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_empty_form_tab: Esta pestaña ({name}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_empty_section: Esta sección ({name}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_form_element: Este elemento de formulario ({name}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_form_tab: Esta pestaña ({name}) se eliminará de forma permanente. ¿Está seguro? Escriba <b>{confirmText}</b> para eliminar de forma permanente esta pestaña.
  confirm_delete_interface: Esta interfaz ({nombre}) se eliminará de forma permanente y no se podrá recuperar. ¿Está seguro? Esto resultará en una PÉRDIDA DE DATOS irreparable. Escriba <b>{confirmText}</b> para eliminar esta interfaz de forma permanente.
  confirm_delete_interface_element: Este elemento se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_invites: "{itemCount, plural, one {Esta invitación sera eliminada} other {Las invitaciones seleccionadas ({itemCount}) serán eliminadas} }. ¿Está seguro?"
  confirm_delete_link_tab: Esta pestaña y su contenido se eliminarán permanentemente. ¿Estás seguro?
  confirm_delete_nested_grid: Este grupo de elementos y su contenido se eliminarán de forma permanente. ¿Está seguro?
  confirm_delete_object_type: Este objeto ({name}) y todos sus registros se eliminarán de forma permanente y no se podrán recuperar. ¿Está seguro? Esto resultará en una PÉRDIDA DE DATOS irreparable. Escriba <b>{confirmText}</b> para eliminar este objeto de forma permanente.
  confirm_delete_object_type_role: Este rol ({objectTypeRoleName}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_objects: "{itemCount, plural, one {Este registro se eliminará de forma permanente y no se podrá recuperar.} other {Los registros seleccionados se eliminarán de forma permanente y no se podrán recuperar ({itemCount}).} } ¿Está seguro?"
  confirm_delete_option: Esta opción se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_quick_access: Este acceso rápido ({quickAccessName}) se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_section: Esta sección ({name}) se eliminará de forma permanente. ¿Está seguro? Escriba <b>{confirmText}</b> para eliminar de forma permanente esta sección.
  confirm_delete_signature: Esta firma se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_users: "{itemCount, plural, one {Este usuario sera eliminado} other {Los usuarios seleccionados ({itemCount}) serán eliminados} } de forma permanente. ¿Está seguro?"
  confirm_delete_users_from_team: "{itemCount, plural, one {Este usuario será retirado} other {Los usuarios seleccionados ({itemCount}) serán retirados} } de forma permanente de este equipo. ¿Está seguro?"
  confirm_delete_validation: Esta validación se eliminará de forma permanente. ¿Está seguro?
  confirm_delete_view: Esta vista ({viewName}) se eliminará de forma permanente. ¿Está seguro?
  confirm_discard_custom_thumbnail: Esto descartará la miniatura personalizada actual. ¿Está seguro?
  confirm_lose_rights: Los cambios que realizó pueden afectar a los usuarios asignados a este rol. ¿Está seguro?
  confirm_overwrite_custom_qr: Ya se ha asociado un código QR/de barras con este registro. ¿Quieres sobreescribirlo?
  confirm_publish_object_type: Este objeto todavía contiene información faltante. Esto puede afectar su comportamiento y/o usabilidad. ¿Está seguro?
  confirm_recalculation: La fórmula asociada a este campo ha cambiado. ¿Desea recalcular los valores de este campo para todos los registros existentes?<br>Este recálculo no activará ninguna automatización.
  confirm_reject_requests: "{itemCount, plural, one {Estas solicitudes de unión será rechazada} other {Las solicitudes de unión seleccionadas ({itemCount}) serán rechazadas } }. ¿Está seguro?"
  confirm_removal_current_user_as_interface_owner: Se retirá de la lista de propietarios y ya no podrá editar esta interfaz. ¿Está seguro?
  confirm_removal_current_user_as_object_type_owner: Se retirá de la lista de propietarios y ya no podrá editar este objeto. ¿Está seguro?
  confirm_remove_interface: "¿Está seguro de que deseas retirar <b>{name}</b> de <b>{teamName}</b>?"
  confirm_remove_object_type: "¿Está seguro de que deseas retirar <b>{name}</b> de <b>{teamName}</b>?"
  confirm_trigger_event: Este registro tiene cambios sin guardar. ¿Desea guardar el registro y ejecutar la acción?
  confirm_unpublish_interface: "¿Está seguro de que deseas cancelar la publicación de <b>{name}</b> en este equipo? Los miembros no podrán acceder a él ni a sus datos. ¿Está seguro?"
  confirm_unpublish_object_type: "¿Está seguro de que deseas cancelar la publicación de <b>{name}</b> en este equipo? Los miembros no podrán acceder a él ni a sus datos. ¿Está seguro?"
  confirm_unsaved_changes_automation:
    text_close: Esta Automatización tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
    text_save: Esta Automatización tiene cambios sin guardar. ¿Está seguro de que desea guardar?
  confirm_unsaved_changes_automation_action:
    text_close: Esta acción tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
    text_save: Esta acción tiene cambios sin guardar. ¿Está seguro de que desea guardar?
  confirm_unsaved_changes_conditional_display: Esta condición tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_changes_interface: Esta interfaz tiene cambios no guardados. ¿Está seguro de que quieres cerrar sin guardar?
  confirm_unsaved_changes_object: Este registro tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_changes_record_picker: Este registro tiene cambios sin guardar. ¿Está seguro de que desea seleccionar un registro diferente y descartar los cambios sin guardar?
  confirm_unsaved_changes_role: Este rol tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_changes_show_route_record_picker: Existen cambios sin guardar para {registros}. ¿Está seguro de que desea descartarlos?
  confirm_unsaved_changes_team: Este equipo tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_changes_thumbnail: Esta miniatura tiene cambios no guardados. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_form: Este formulario tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_interface: Esta interfaz tiene cambios sin guardar. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unsaved_translations:
    target_text: La traducción de destino no se guarda. ¿Está seguro de que desea cambiar la configuración regional de destino sin guardar?
    text: Estas traducciones no se guardan. ¿Está seguro de que desea cerrar sin guardar?
  confirm_unshare_object_type: "¿Está seguro de que desea dejar de compartir <b>{name}</b> en <b>{teamName}</b>?"
  confirmation_required: Confirmación requerida
display: Vista
display_as: Mostrar como
do: Haz
done: Hecho
download: Descargar
dynamic: Dinámica
dynamic_options:
  current_user: Usuario registrado
  now: Fecha y hora actual
  today: Fecha actual
edit: Editar
edit_filter_rules: Editar filtros
edit_object: Editar objeto
edit_shortcut: Editar vínculo
edit_sort_rules: Editar reglas de ordenación
edit_tab: Editar pestaña
edit_view: Editar vista
embed: Integrar
embed_modal:
  show_tabs: Mostrar el selector de pestañas
  show_toolbar: Mostrar la barra de herramientas
enable_scanning: Activar escaneo
enter_number: Ingresar número
enter_something: Ingresar algo
enter_text: Ingresar texto
enter_url: Ingresar URL
error:
  empty_interface_owners_list: La interfaz debe tener al menos un propietario. Por favor agregue un nuevo propietario.
  empty_object_type_owners_list: El objeto debe tener al menos un propietario. Por favor, añade un nuevo propietario.
  generic_server_message: Hubo un error al procesar la solicitud.
  object_type_attributes_limit: Ha alcanzado el máximo de {object_type_attributes_limit} campos por objeto. Considere la posibilidad de crear objetos separados con menos campos cada uno y vincularlos.
  title: Error
export:
  cancelled: Exportación cancelada
  completed: Exportación exitosa
  export: Exportar
  failed: Exportación fallida
  in_progress: Exportación en curso
'false': 'No'
field: Campo
field_calculation: Cálculo de campo
field_name: Nombre del campo
field_name_placeholder: Ingresar el nombre del campo
field_to_display: Valor del campo a mostrar
field_to_search_by: Campo para buscar
filters_applied: "{amount, plural, =0 {Sin filtros aplicados} =1 {# filtro aplicado} other {# filtros aplicados}}"
fit_element: Ajustar dentro del elemento (mostrando solo la primera vista previa)
form: Formulario
form_builder:
  label_for_hidden: Ocultar campo en esta variante
  label_for_locked: Visualización de fuerza en variantes
  object_type_relationship_options:
    cardinalities:
      multiple: Un <baseObjectType> se puede vincular a varios <targetObjectType>
      single: Un <baseObjectType> se puede vincular como máximo a un <targetObjectType>
    forward: Relación directa
    from: Desde
    inverse: Relación inversa
    object_already_linked: Objeto ya vinculado
    object_not_yet_linked: Objeto aún no vinculado
    to: Hasta
  selectable_value_options:
    allow_variant_options: Posibilidad de añadir opciones en variantes
formula:
  ABS:
    description: Devuelve el valor absoluto de un número, un número sin su signo.
    syntax_description: "<ul><li>Number: es el número real para encontrar el valor absoluto.</li></ul>"
  ALL:
    description: Comprueba si todos los argumentos son TRUE y devuelve TRUE si todos los argumentos son TRUE.
    syntax_description: "<ul><li>List: lista de valores.</li> <li>Parameter: nombre del parámetro que se utiliza en la función.</li> <li>Function: se aplica a cada valor (parámetro) y verifica si todos los valores cumplen con una determinada condición.</li></ul>"
  AND:
    description: Comprueba si todos los argumentos son TRUE y devuelve TRUE si todos los argumentos son TRUE.
    syntax_description: "<ul><li>Logical1, Logical2,...: cualquier valor o expresión que pueda evaluarse como TRUE o FALSE.</li></ul>"
  ANY:
    description: Comprueba si todos los argumentos son TRUE y devuelve TRUE si alguno de los argumentos es TRUE.
    syntax_description: "<ul><li>List: lista de valores.</li> <li>Parameter: nombre del parámetro que se utiliza en la función.</li> <li>Function: se aplica a cada valor (parámetro) y verifica si hay alguno. El valor cumple con una determinada condición.</li></ul>"
  AVG:
    description: Devuelve el promedio (media aritmética) de sus argumentos.
    syntax_description: "<ul><li>Number1, Number2, ...: argumentos numéricos para calcular el promedio.</li></ul>"
  CONCAT:
    description: Concatena una lista de cadenas de texto.
    syntax_description: "<ul><li>Text1, Text2, ...: cadenas que se unirán en una sola cadena de texto.</li></ul>"
  CONTAINS:
    description: Comprueba si un subtexto está dentro de un texto y devuelve TRUE o FALSE.
    syntax_description: "<ul><li>Find_text: es el texto a buscar.</li> <li>Within_text: es el texto que contiene el texto a buscar.</li></ul>"
  COS:
    description: Devuelve el coseno de un ángulo.
    syntax_description: "<ul><li>Number: es el ángulo en radianes para el cual calcular el coseno.</li></ul>"
  COUNT:
    description: Cuenta el número de valores o la longitud de una cadena.
    syntax_description: "<ul><li>Value1, Value2, ...: valores que pueden ser una variedad de tipos de datos diferentes.</li></ul>"
  COUNTIF:
    description: Cuenta todos los valores de una lista que cumplen una determinada condición.
    syntax_description: '<ul><li>List: lista de valores.</li> <li>Function: se aplica a cada valor (parámetro) y cuenta cada valor que cumple con una determinada condición. El nombre del parámetro que se debe utilizar en la función es: "valor".</li></ul>'
  DURATION:
    description: Devuelve un valor para sumar o restar de una fecha.
    syntax_description: "<ul><li>Number: es la cantidad de unidades.</li><li>Unit: 'years', 'months' y 'days' son las unidades disponibles.</li></ul>"
  EXP:
    description: Devuelve e elevado a la potencia de un número dado.
    syntax_description: "<ul><li>Number: es el exponente aplicado a la base e. La constante e es igual a 2,71828182845904, la base del logaritmo natural.</li></ul>"
  FILTER:
    description: Filtra una lista de valores.
    syntax_description: "<ul><li>List: lista de valores.</li> <li>Parameter: nombre del parámetro que se utiliza en la función.</li> <li>Function: se aplica a cada valor (parámetro) y filtrará los valores que no cumplen una determinada condición.</li></ul>"
  FIND:
    description: Devuelve la posición de un subtexto dentro de un texto.
    syntax_description: "<ul><li>Find_text: es el texto a buscar.</li> <li>Within_text: es el texto que contiene el texto a buscar.</li></ul>"
  IF:
    description: Comprueba si se ha cumplido una condición y devuelve un valor si es TRUE y otro valor si es FALSE.
    syntax_description: "<ul><li>Logical_test: es cualquier valor o expresión que puede evaluarse como TRUE o FALSE.</li> <li>Valor_si_verdadero: es el valor que se devolverá si prueba_lógica es TRUE. Si se omite, se devolverá TRUE.</li> <li>Valor_si_falso: es el valor que se devolverá si Prueba_lógica es FALSE. Si se omite, se devolverá FALSE.</li></ul>"
  LEFT:
    description: Devuelve el número especificado de caracteres desde el inicio de una cadena de texto.
    syntax_description: "<ul><li>Text: es la cadena de texto que contiene los caracteres a extraer.</li> <li>Num_chars: especifica cuántos caracteres a extraer.</li></ul>"
  LEN:
    description: Devuelve el número de caracteres de una cadena de texto.
    syntax_description: "<ul><li>Text: es el texto para el cual se calculará la longitud. Los espacios cuentan como caracteres.</li></ul>"
  LOG:
    description: Devuelve el logaritmo de un número con la base que especifique.
    syntax_description: "<ul><li>Number: es el número real positivo para el cual desea el logaritmo.</li><li>Base: es la base del logaritmo; e si se omite.</li></ul>"
  LOG10:
    description: Devuelve el logaritmo en base 10 de un número.
    syntax_description: "<ul><li>Number: es el número real positivo para el cual desea el logaritmo en base 10.</li></ul>"
  MAP:
    description: Devuelve una lista de valores formada al 'asignar' cada valor de la lista a un nuevo valor aplicando una función.
    syntax_description: "<ul><li>List: lista de valores.</li> <li>Parameter: nombre del parámetro que se utiliza en la función.</li> <li>Function: se aplica a cada valor(parámetro) y devolverá el nuevo valor calculado.</li></ul>"
  MAX:
    description: Devuelve el valor más grande de un conjunto de valores.
    syntax_description: "<ul><li>Number1, Number2, ...: números para los cuales encontrar el máximo.</li></ul>"
  MAXOCCURRENCE:
    description: Devuelve el n-ésimo valor más común en una lista.
    syntax_description: "<ul><li>Lista: lista de valores.</li> <li>n: índice del valor más común a encontrar.</li></ul>"
  MID:
    description: Devuelve los caracteres del medio de una cadena de texto, dada una posición inicial y una longitud.
    syntax_description: "<ul><li>Text: es la cadena de texto de la cual extraer los caracteres.</li> <li>Start_num: es la posición del primer carácter a extraer. El primer carácter del Texto es 1.</li> <li>Num_chars: especifica cuántos caracteres devolver del Texto.</li></ul>"
  MIN:
    description: Devuelve el número más pequeño de un conjunto de valores.
    syntax_description: "<ul><li>Number1, Number2, ...: números para encontrar el mínimo.</li></ul>"
  NOT:
    description: Cambia FALSE a TRUE o TRUE a FALSE.
    syntax_description: "<ul><li>Logical: es un valor o expresión que puede evaluarse como TRUE o FALSE.</li></ul>"
  OR:
    description: Comprueba si alguno de los argumentos es TRUE y devuelve TRUE o FALSE. Sólo devuelve FALSE si todos los argumentos son FALDOS.
    syntax_description: "<ul><li>Logical1, Logical2, ...: condiciones a probar que pueden ser TRUE o FALSE.</li></ul>"
  RIGHT:
    description: Devuelve el número especificado de caracteres desde el final de una cadena de texto.
    syntax_description: "<ul><li>Text: es la cadena de texto que contiene los caracteres a extraer.</li> <li>Num_chars: especifica cuántos caracteres a extraer.</li></ul>"
  ROUND:
    description: Redondea un número a un número específico de dígitos.
    syntax_description: "<ul><li>Number: es el número a redondear.</li> <li>Num_digits: es el número de dígitos a redondear.</li></ul>"
  ROUNDDOWN:
    description: Redondea un número hacia abajo, hacia cero.
    syntax_description: "<ul><li>Number: es cualquier número real a redondear hacia abajo.</li> <li>Num_digits: es el número de dígitos al que redondear.</li></ul>"
  ROUNDUP:
    description: Redondea un número hacia arriba, lejos de cero.
    syntax_description: "<ul><li>Number: es cualquier número real a redondear.</li> <li>Num_digits: es el número de dígitos a los que redondear.</li></ul>"
  SIN:
    description: Devuelve el seno de un ángulo.
    syntax_description: "<ul><li>Number: es el ángulo en radianes para el cual calcular el seno.</li></ul>"
  SQRT:
    description: Devuelve la raíz cuadrada de un número.
    syntax_description: "<ul><li>Number: es el número del que desea obtener la raíz cuadrada.</ul></li>"
  STDEV:
    description: Devuelve la desviación estándar basada en una muestra.
    syntax_description: "<ul><li>Number1, Number2, ...: números para encontrar la desviación estándar muestral.</li></ul>"
  SUBSTITUTE:
    description: Reemplaza el texto existente con texto nuevo en una cadena de texto.
    syntax_description: "<ul><li>Text: es el texto en el que sustituir caracteres.</li> <li>Old_text: es el texto existente a reemplazar.</li> <li>New_text: es el texto a reemplazar con texto_antiguo. </li></ul>"
  SUM:
    description: Suma todos los números.
    syntax_description: "<ul><li>Number1, Number2, ...: los números a sumar.</li></ul>"
  SWITCH:
    description: Evalúa una expresión frente a una lista de valores y devuelve el resultado correspondiente al primer valor coincidente. Si no hay ninguna coincidencia, se devuelve un valor predeterminado opcional.
    syntax_description: "<ul><li>Expression: es una expresión a evaluar.</li><li>Value1, Value2, ...: valor a coincidir con la expresión.</li><li>Result1, Result2, .. .: valor a devolver si hay una coincidencia.</li><li>Default_value: valor a devolver si no hay una coincidencia.</li></ul>"
  TAN:
    description: Devuelve la tangente de un ángulo.
    syntax_description: "<ul><li>Number: es el ángulo en radianes para el cual calcular la tangente.</li></ul>"
  VAR:
    description: Devuelve la variación basada en una muestra.
    syntax_description: "<ul><li>Number1, Number2, ...: números para los cuales encontrar la varianza muestral.</li></ul>"
  XOR:
    description: Devuelve un 'O exclusivo' lógico de todos los argumentos.
    syntax_description: "<ul><li>Logical1, Logical2, ...: condiciones a probar que pueden ser TRUE o FALSE.</li></ul>"
  syntax: Sintaxis
function_assistant: Asistente de función
general: General
got_it: Entendido
grid: Red
grouped_by: Agrupado por
help: Ayuda
hide_all: Ocultar todo
how_to_use_in: Cómo utilizar en
icon: Icono
include: Incluir
include_descendants: Incluir descendientes
incorrect_qr_format: Formato QR incorrecto
input: Campos
input_type:
  attachment: Adjunto
  button: Botón
  checkbox: Caja
  checkmark: Marca de verificación
  date: Fecha
  datetime: Fecha y hora
  description: Texto fijo
  descriptive_attachment: Imágenes / medios fijos
  dropdown_list: Lista desplegable
  linked_object: Objeto Houston vinculado
  lookup: Campo de un objeto Houston vinculado
  number: Número
  radio: Radio
  range_select: Selección de rango
  rich_text: Texto rico
  short_text: Texto corto
  signature: Firma
  switch: Cambiar
  textarea: Área de texto
  type: Tipo de campo
  url: URL
  user: Usuario
insert_function: Insertar la función
interface:
  attachment: Imágenes / medios fijos
  button: Botón
  calendar_chart: Gráfico de calendario
  contact_text: Este elemento aún no se puede configurar en la interfaz de usuario. ¿Necesitas ayuda? Contáctenos en <a href="mailto:<EMAIL>"><EMAIL></a>
  filter: Filtrar
  filter_through_element: Y filtrar a través de
  gage_chart: Gráfico de indicador
  html: HTML
  html_placeholder: Ingrese HTML
  interface: Interfaz
  interface_not_available: Esta interfaz no está disponible en el equipo seleccionado.
  linebar_chart: Gráfico de línea/barra/área
  links: Vínculos
  nested_grid: Grupo de elementos
  number: Número
  pie_chart: Gráfico circular y de anillos
  pyramid_chart: Gráfico piramidal
  radar_chart: Gráfico de radar
  read_only: Sólo lectura
  record_in_record_picker: Grabar "{record}" en el selector de registros "{record_picker}"
  record_picker: Selector de registros
  show_label: Mostrar etiqueta
  source: Fuente
  text: Texto
  unit: Unidad
interface_editor:
  action_type_selector:
    data_change: Cambiar datos
    delete_record_action: Eliminar un registro
    external_url_navigation: Abrir URL externa
    interface_section_navigation: Ir a pestaña de interfaz
    navigation: Navegar
    place_holder: Seleccione un tipo de acción
    save_record_action: Guardar un registro
    trigger_automation_action: Activar una automatización
  actions:
    navigation_action:
      open_in_new_tab: Abrir en nueva pestaña
interfaces_managed_by_me: Interfaces bajo mi gestión
invitation:
  add_message: Añadir un mensaje... (recomendado)
  date_and_time: Fecha y hora de la invitación
  delete_invite: Eliminar invitación
  email: Correo electrónico
  invite_user_by_email: Invitar usuario por correo electrónico
  invites: Invitaciones
  message: Mensaje
  send_invite: enviar una invitacion
  toast:
    invitation_sent: Invitación enviada
    title: Invitación de usuario
    user_already_in_team: Este usuario ya es miembro del equipo
    user_found: Esta dirección de correo electrónico corresponde al usuario {name} que ahora se ha agregado al equipo
label_display: Visualización de etiquetas
language: Idioma
languages:
  en: Inglés
  es: Español
  fr: Francés
  nl: Neerlandés
last_activity: Última actividad
layout: Disposición
less: menos
linked_fields: Campos vinculados
linked_object: Objeto vinculado
linked_object_field: Campo de objeto vinculado
linked_objects: Objetos vinculados
loading: Cargando...
lookup_from: Búsqueda desde
lookup_through_field: a través del campo "{field}"
max: Máx
members: Miembros
memberships_awaiting_approval: Membresías en espera de aprobación
message: Mensaje
min: Mín
minutes: Minutos
missing_video_permission: No hay cámara presente o no se dio permiso.
more: más
more_info: Más información
move: Mover
move_form_element_modal_title: Mover campo a otra pestaña
move_interface_modal_title: Mover elemento a ...
multiple: Múltiple
my_teams: mis equipos
navigation:
  home: Inicio
  interfaces: Interfaces
  not_found: No encontrado
  objects: Objetos
new: Nuevo
new_interface: Nueva interfaz
new_object_type: Nuevo objeto
new_section: Nueva sección
new_shortcut: Nuevo vínculo
new_tab: Nueva pestaña
new_version_info: Una nueva version esta disponible.
new_version_refresh: Porfavor refresca
no_access: Sin acceso
no_access_tooltip: No eres miembro de este equipo
no_filters: No hay filtros
no_grouped_attribute_warning: No hay ningún campo seleccionado para agrupar registros.
no_home_page_description: No hay ninguna página de inicio definida para este equipo.
no_items_to_display: No hay elementos para mostrar.
no_results_found: No hay resultados
no_team_filters: No hay filtros de equipo
non_editable_view: Esta vista no se puede editar
none: Ninguno
not_active: No activo
not_found_description: No se encontró la página a la que intentó acceder.
notify_team_admins: Notificar a los administradores del equipo
object_form_link: Enlace al formulario
object_hyperlink: Enlace al registro
object_not_available: Este objeto no está disponible en el equipo seleccionado.
object_print_modal:
  all_tabs: Todas las pestañas
  current_tab: Pestaña actual
object_type: Objeto
object_type_description:
  editor: Información para editores de objetos
  empty: "(Aún no hay descripción)"
  user: Información para todos los usuarios
object_type_share_violation: El objeto ya ha sido compartido con el equipo.
objects_managed_by_me: Objetos bajo mi gestión
ok: OK
'on': En
operations:
  after: Después
  before: Antes
  blank: Es blanco
  contains: Contiene
  equals: Igual
  greater_than: Mayor que
  in: En
  lower_than: Menor que
  not_contains: No contiene
  not_equals: No es igual
  presence: Es llenado
  within: En
operator: Operador
option: Opción
options: Opciones
or: O
other: Otro
owned: Bajo mi gestión
owners: Propietarios
personal: Personal
pin_tab: Fijar una pestaña
placeholder: Marcador de posición
position:
  inside: Adentro
  top: Arriba
power_bi: Power BI
print: Imprimir
properties: Propiedades
publication_settings: Configuración de publicación
publish: Publicar
publish_dialog:
  default_assignment: Asigne automáticamente roles de Creador y Editor a este equipo
  descendants: Este equipo y todos sus descendientes.
  interface: Publicar interfaz
  no_default_assignment: Déjame elegir las asignaciones de roles
  object: Publicar objeto
  redirect: Llévame a la página de Roles después de la publicación
  role_info: Para crear, actualizar y eliminar registros de un objeto, los usuarios deben tener un rol que tenga ese derecho
  team: este equipo solo
publish_in: Publicar en
publish_warnings:
  automation: A esta automatización le falta información. Esto puede afectar su comportamiento y/o usabilidad.
  automations: Hay una o varias automatizaciones a las que les falta información. Esto puede afectar su comportamiento y/o usabilidad.
  form_element: A este campo le falta información. Esto puede afectar su comportamiento y/o usabilidad.
  form_elements: Hay uno o varios campos a los que les falta información. Esto puede afectar su comportamiento y/o usabilidad.
  role: A este rol le falta información. Esto puede afectar su comportamiento y/o usabilidad.
  roles: Hay uno o varios roles a los que les falta información. Esto puede afectar su comportamiento y/o usabilidad.
published: Publicado
qr_bar_code: Código QR/de barras
qr_download: Descargar código QR de Houston
qr_modal:
  placeholder: Introduzca el código
  title: Ingrese el código QR/de barras para este registro
query:
  current: En curso
  current_month: Mes actual
  current_week: Semana actual
  current_year: Año actual
  day: Día
  exact_date: Fecha exacta
  filter: Filtrar
  filter_by_this_field: Filtrar por este campo
  from: De
  from_team: Del equipo
  last_month: Mes pasado
  last_week: Semana pasada
  last_year: Año pasado
  linked_with: Vinculado con
  month: Mes
  new_filter_condition: Nuevo filtro
  new_sort_option: Nueva regla de ordenación
  new_team_filter_condition: Nuevo filtro de equipo
  new_view: Nueva vista
  next: Próximo
  no_filters_applied: No se aplican filtros.
  no_sorts_applied: No se aplican reglas de ordenación.
  previous: Anterior
  select_option: Seleccionar una opción
  sort: Ordenar
  sort_asc: Orden ascendente
  sort_desc: Orden descendiente
  to: Hasta
  today: Hoy
  today_minus: Hoy menos
  today_plus: Hoy más
  week: Semana
  where: Donde
  year: Año
quick_access: Acceso rapido
recently_used: Recientemente usado
records_shared: Registros compartidos con este equipo
records_subteams: Registros de subequipos
refresh: Actualizar
remove_interface_from_team: Retirar interfaz de este equipo
remove_object_from_team: Retirar objeto de este equipo
revision_history:
  title: Historial de revisiones
  user:
    create: "<b>{user}</b> creó este registro"
    update: "<b>{user}</b> editó este registro"
  you:
    create: "<b>Usted</b> creaste este registro"
    update: "<b>Usted</b> has editado este registro"
role:
  access_rights: Derechos de acceso
  assigned_to: Asignado a
  author_of_a_record: Autor de un registro
  edit_role: Editar rol
  fixed_set_of_users: Grupo fijo de usuarios
  new_role: Nuevo rol
  role: Rol
  through_attribute: A través del campo
  users_from_specific_field: Usuarios de un campo específico
roles: Roles
root_level: Nivel raíz
save: Guardar
saving: Guardar en progreso
scan: Escanear
search: Buscar
search_icons: Buscar iconos
section: Sección
section_name: Nombre de la sección
select_color: Seleccionar color
select_column_header: Seleccionar un encabezado de columna
select_column_header_description: Seleccione un campo para usar para agrupar registros en esta vista {viewType}
select_create_which_object: Seleccionar el objeto que desea crear
select_date: Seleccionar une fecha
select_field: Seleccionar campo
select_filter: Seleccionar filtro
select_object: Seleccionar un objeto
select_placeholder: Seleccionar algo
select_record: Seleccionar un registro
select_tab: Seleccionar pestaña
select_team: Seleccionar un equipo
select_user: Seleccionar un usuario
selectable_values: Valores seleccionables
send_email_to: Enviar el email a
settings: Ajustes
share: Compartir
share_interface_with_new_teams: Compartir interfaz con equipos
share_object_with_new_teams: Compartir objeto con equipos
show_all: Mostrar todo
show_background_image: Mostrar imagen de fondo
show_details: Mostrar detalles
show_details_in_header: Mostrar detalles en el encabezado
show_field_names: Mostrar nombres de campos
show_more: Mostrar más
show_options: 'Mostrar opciones:'
show_thumbnail_icon: Mostrar icono de objeto
sign_here: Firmar aquí
sign_out: Desconectar
single: Único
size_limit_warning: Límite de tamaño excedido
someone: Alguien
sort_rules_applied: "{amount, plural, =0 {Sin reglas de ordenación aplicadas} =1 {# regla de ordenación aplicada} other {# reglas de ordenación aplicadas}}"
start_houston: Empieza a usar Houston
status: Estado
step: Paso
stop_scanning: Dejar de escanear
structure: Estructura
subject: Sujeto
submit: Enviar
subteams: Subequipos
subtitle: Subtítulo
support: Soporte
system: Sistema
tab: Pestaña
tab_name: Nombre de la pestaña
team: Equipo
team_filterable: Filtrar registros según el equipo seleccionado
team_filterable_user_relationship: Filtrar usuarios según el equipo seleccionado
teams: Equipos
teams_to_scope_data_to: Datos de
theme: Modo de visualización
theme_dark: Oscuro (experimental)
theme_light: Claro
thumbnail:
  background_image: Imagen de fondo
  card: Tarjeta
  date_time_display_mode:
    date_and_time: Fecha y hora
    date_only: Solo fecha
    time_only: Solo hora
  default_card: Usado por defecto en la Galería.
  default_multiple_rows: Utilizado de forma predeterminada en listas y formularios.
  default_single_column: Se puede utilizar en formularios.
  default_single_row: Usado por defecto en Tablas.
  edit_thumbnail: Editar miniatura
  grid: Varias filas
  horizontal: Unica fila
  prefix: Prefijo
  row: Fila
  selected_field: Campo de miniatura seleccionado
  show_title_row: Mostrar fila de título
  subtitle: Subtitular
  suffix: Sufijo
  thumbnail: Miniatura
  title: Título
  type: Tipo de miniatura
  vertical: Una sola columna
thumbnails: Miniaturas
time_unit: Unidad de tiempo
timeline:
  end_date: Fecha de finalización
  start_date: Fecha de inicio
timeline_settings: Configuración de la línea de tiempo
title: Título
title_field: Campo de título
translation_management: Gestión de traducciones
'true': Sí
type: Tipo
unassigned: No asignado
uncategorized: Sin categoría
unlink_selected: Desvincular registros seleccionados
unpublish: Cancelar la publicación
unpublished: En espera de publicación
unshare: Dejar de compartir
uploader:
  action: Acción
  one_or_more_files: Subir 1 o más archivos
  take_picture: Tomar foto
  take_video: Grabar vídeo
  uploading: Subiendo ...
used_in: Utilizada en
user_account:
  activate: Activar cuenta de usuario
  deactivate: Desactivar cuenta de usuario
user_guide: Guía del usuario
user_join_requests:
  approvals: Aprobaciones
  approve: Aprobar
  approve_requests: Aprobar selección
  awaiting_approval: Esperando aprobacion
  date_and_time: Fecha y hora de la solicitud
  reject: Rechazar
  reject_requests: Rechazar selección
  toast:
    body: Hemos pedido a los administradores que aprueben su solicitud para unirse.
    title: Solicitud de unión enviada
user_not_found: "¿Usuario no encontrado?"
users: Usuarios
validation: Validación
validation_type:
  equals: debe ser igual a
  greater_than: debe ser mayor que
  less_than: debe ser menor que
  max_length: no debe ser más largo que
  min_length: no debe ser más corto que
  other_than: debe ser distinto de
  presence: debe ser llenado
validations: Validaciones
value: Valor
value_set_on_creation: Valor establecido en la creación
variants:
  master: Master
view: Vista
visibility: Visibilidad
visualisation:
  multiObjectList: Lista de objetos múltiples
  objectCalendar: Calendario
  objectGallery: Galería
  objectKanban: Kanban
  objectList: Lista
  objectTable: Tabla
  objectTimeline: Cronología
warning_changes_object_type: Los cambios a este objeto afectarán a todos los registros de este objeto. ¡Proceda con cuidado!
warning_optional_field: Este campo es opcional y es posible que no esté disponible en variantes.
welcome: Bienvenido a Houston. Para comenzar a usar Houston, solicite unirse a un equipo
