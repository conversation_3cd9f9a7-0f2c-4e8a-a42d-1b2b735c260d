---
access: Toegang
active: Actief
add_calculation: Voeg een berekening toe
add_custom_qr: Voeg een aangepaste QR/barcode toe
add_quick_access: Snelle toegang toevoegen
add_subteam: Voeg een subteam toe
add_to_team: Voeg toe aan team
add_user: Voeg een gebruiker toe
admin_panel:
  invitations_page:
    delete_selected_invitations: Geselecteerde uitnodigingen verwijderen
  page_title: Administratie
  users_page:
    cannot_delete_yourself: U kunt uzelf niet verwijderen
    cannot_disable_yourself: U kunt uzelf niet deactiveren
    delete_selected_users: Geselecteerde gebruikers verwijderen
    delete_user: Gebruiker verwijderen
    disable_selected_users: Geselecteerde gebruikers deactiveren
administrators: Beheerders
aggregation: Berekening
aggregations:
  average: Gemiddelde
  count: Aantal records
  maximum: Maximum
  minimum: Minimum
  pluck: Lijst
  sum: Som
all: Alle
allow_creation_new_records: Sta het maken van nieuwe records toe
allow_select_existing_records: Sta toe om bestaande records te selecteren
and: En
appearance: Lay-out
apply: To<PERSON><PERSON><PERSON>
assistant:
  analysis_in_progress: Analyseren...
  ask_houston: Vraag aan Houston
  data_limit_exceeded: Datalimiet overschreden. Overweeg om minder invoervelden of minder records te gebruiken.
  experimental: Experimenteel
  houston: Houston
  question: Vraag
  question_placeholder: Waarmee kan ik u helpen? Ik kan samenvatten, analyseren en nog veel meer.
  toggle_input_fields: Invoervelden in-/uitschakelen
attributes:
  background: Achtergrond
  created_at: Gecreëerd op
  id: Globale ID
  label: Label
  last_updated_by_id: Laatst bijgewerkt door
  local_id: ID in object
  name: Naam
  text: Tekst
  updated_at: Laatst bijgewerkt op
  user_id: Gecreëerd door
attributes_list: Velden om als lijst weer te geven
authentication_header_explanation:
  authentication: Dit authenticatietoken moet worden opgegeven als de waarde van een 'Authorization' header
  power_bi: <i>(In het Engels)</i><ol class="text-start"><li>In Power BI Desktop, click "Get data", choose "Web" and select "Advanced"</li><li>Paste the URL above in "URL parts" </li><li>Add a header called "Authorization" in "HTTP request header parameters (optional)" and paste the Authentication token above as its value</li><li>Confirm by clicking "OK"</li></ol>
automate: Automatiseren
automation:
  action: Actie
  add_all: Voeg alles toe
  add_attribute: Veld toevoegen
  add_input_attribute: Invoerveld toevoegen
  add_output_attribute: Uitvoerveld toevoegen
  add_recipient: Ontvanger toevoegen
  advanced_settings: Geavanceerde instellingen
  automation: Automatisatie
  automations: Automatiseringen
  create: een nieuw record wordt aangemaakt
  description_field: Beschrijf het veld voor de AI
  destroy: een record wordt verwijderd
  email_address_from_field: E-mailadres ingevoerd in een veld
  enter_email: Voer email in
  event: Gebeurtenis
  fill_fields_ai: Genereer met AI
  frontend: er wordt op een knop geklikt
  get_static_data: Krijg statische gegevens
  input_attributes: Invoervelden
  integration: een integratie geactiveerd wordt
  mentioned_users: Gebruikers vermeld in het record
  only_present_fields: Negeer lege velden
  only_updated_fields: Alleen gewijzigde velden weergeven
  output_attributes: Uitvoervelden
  recipient: Ontvanger
  recipient_type: Type ontvanger
  recipients: Ontvangers
  remove_all: Alles weghalen
  run_integration: Integratie uitvoeren
  save_before_execute: Record opslaan voordat de automatisatie wordt uitgevoerd
  schedule_options:
    after_n_minutes: Na {delay} minuten
    immediately: Onmiddellijk
    with_delay: Met een vertraging
  scheduling: Planning
  select_buttons: Selecteer knoppen
  selected_users: Geselecteerde gebruikers
  send_based_on_field: Verzenden op basis van een veldwaarde
  send_email: Stuur e-mail
  send_immediately: Stuur onmiddellijk
  send_later: Stuur later
  show_changes: Wijzigingen in veldwaarden markeren
  static_mail_address: E-mailadres
  system_message: Systeem bericht
  system_message_description: Vertel de AI assistent hoe zich te gedragen
  team_of_record: Team van het record
  type_of_action: Type actie
  type_of_event: Type gebeurtenis
  update: een record wordt bijgewerkt
  update_attributes: Bij te werken velden
  update_record: Record bijwerken
  user_selected_in_a_field: Gebruiker geselecteerd in een veld
background: Achtergrond
bars: Balken
body: Bericht
calculation:
  calculated: Berekend
  create_calculation: Berekening maken
  edit_calculation: Berekening bewerken
  enter_formula: Formule invoeren
  formula: Formule
  formula_explanation: |-
    <p>Formules zijn krachtige hulpmiddelen voor het uitvoeren van berekeningen en het analyseren van gegevens in Houston.</p><p>Formules volgen een Excel-achtige syntaxis:</p> <ul> <li>Een formule is een (wiskundige) uitdrukking die bestaat uit getallen/tekst, wiskundige operatoren, <strong>variabelen</strong> en <strong>functies</strong> die kunnen worden genest. Bijvoorbeeld:</li> <ul> <li>A + 10</li> <li>A / B * 2</li> <li>ALS(A > B, WAAR, ONWAAR)</li> < li>CONCAT(A + B, ' kg in totaal')</li></ul>
    <li>Een <strong>variabele</strong> is een verwijzing naar een veld in een object. De variabele is een alfabetisch teken (A, B, C, enzovoort) dat in de formule kan worden gebruikt. Wanneer de formule wordt geëvalueerd, wordt de variabele vervangen door de waarde van het veld in de record.</li>
    <li>Een <strong>functie</strong> is een ingebouwde methode voor het uitvoeren van een berekening of andere bewerking op gegevens. Er zijn veel functies beschikbaar en de lijst blijft groeien. Deze assistant heeft tot doel u te helpen precies de Houston-functie te vinden die bij uw behoeften past. Neem contact <NAME_EMAIL> als u behoefte heeft aan een extra functie.</li></ul>
  how_to_write_formula: Hoe een formule schrijven in Houston? »
  selectable_values_number_map: Waarde voor berekeningen
  variable: Variabele
cancel: Annuleren
categories: Categorieën
charts:
  area: Vlak
  axis: As
  bar: Staaf
  bar_stack_id: ID van de staafgroep
  category: Categorie
  data_serie_options: Gegevensreeks
  date_field: Datumveld
  display_value: Waarde weergeven
  legend: Legende
  line: Lijn
  opening_angle: Openingshoek
  rotation: Rotatie
  segment: Segment
  show_as_donut: Weergeven als donut?
  target: Doel
  targets:
    above: Boven
    below: Onder
    between: Tussen
    lower: Minimum
    outside: Buiten
    range: Interval
    single_value: Waarde
    upper: Maximum
  time_frame: Tijdsperiode
  time_unit: Tijdseenheid
  x_axis: X-as
  y_axis: Y-as
choose_or_drop_files: Kies bestanden of zet bestanden hier neer
clear: Wissen
clone: Klonen
close: Sluiten
color: Kleur
columns: Kolommen
comment:
  comment: Reactie
  deleted_user: "<b>Verwijderde gebruiker</b> heeft gereageerd"
  edited: bewerkt
  loading: Reacties laden ...
  placeholder: Laat een reactie achter
  user: "<b>{user}</b> heeft gereageerd"
  you: "<b>U</b> hebt gereageerd"
condition:
  always: Altijd
  and_join: " en "
  blank_value: "(leeg)"
  condition: Voorwaarde
  conditions: Voorwaarden
  display_types:
    disable: deactiveer
    filter_selectable: filter opties
    hide: verberg
    show: toon
  field_with_name: veld "{name}"
  if: Als
  operators:
    blank: is leeg
    changes: Verandert
    changes_into: Verandert in
    contains: bevat
    equals: is gelijk aan
    gt: is groter dan
    in: in
    lt: is kleiner dan
    not_equals: niet gelijk aan
    presence: is ingevuld
  or_join: " of "
  result_selectable_explanation: toon {values} in {subject}
  result_visibility_explanation: "{action} {subject}"
  rule: Regel
  rule_clause_explanation: '"{field}" {operator}'
  rule_clause_explanation_with_value: '"{field}" {operator} "{value}"'
  section_with_name: sectie "{name}"
  tab_with_name: Tab "{name}"
  then: dan
  when: Als
conditional_colors: Voorwaardelijke opmaak
conditional_displays: Voorwaardelijke weergaven
confirm: Bevestigen
connected_elements: Verbonden elementen
contact_support: Support contacteren
copied: Gekopieerd!
copy: Kopieer
create: Creëren
create_data_source: Gegevensbron maken
create_interface: Interface maken
create_lookup_field: Maak een opzoekveld vanuit een gekoppeld object
create_new_interface: Nieuwe interface maken
create_new_object_type: Nieuw object maken
create_object_type: Object maken
create_subteam: Subteam maken
create_view: Weergave maken
custom: Aangepast
custom_color: Aangepaste kleur
data: Gegevens
data_source:
  auth_token: Authenticatietoken
datafeed_field_identifier: ID voor gegevensexport
date_period: Datum periode
deactivated: Gedeactiveerd
default: Standaard
default_value: Standaardwaarde
delete: Verwijderen
delete_interface: Interface verwijderen
delete_invitations_from_team: Geselecteerde uitnodigingen uit dit team weghalen
delete_object: Object verwijderen
delete_record: Record verwijderen
delete_selected: Verwijder de selectie
delete_selected_users_from_team: Geselecteerde gebruikers uit dit team weghalen
delete_user_from_team: Gebruiker uit dit team weghalen
delivery: Levering
description: Beschrijving
dialog:
  are_you_sure: Weet u het zeker?
  confirm_accept_requests: "{itemCount, plural, one {Dit deelnameverzoek wordt geaccepteerd} other {De geselecteerde deelnameverzoeken ({itemCount}) worden geaccepteerd} }. Weet u het zeker?"
  confirm_delete_attachment: Deze bijlage ({attachmentName}) wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_automation: Deze automatisering ({automationName}) wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_comment: Deze reactie wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_condition: Deze voorwaarde wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_empty_form_tab: Dit tabblad ({name}) wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_empty_section: Deze sectie ({name}) wordt permanent verwijderd. Weet u het zeker?
  confirm_delete_form_element: Dit formulierelement ({name}) wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_form_tab: Dit tabblad ({name}) wordt definitief verwijderd. Weet u het zeker? Typ <b>{confirmText}</b> om dit tabblad definitief te verwijderen.
  confirm_delete_interface: Deze interface ({name}) wordt permanent verwijderd en kan niet worden hersteld. Weet u het zeker? Dit leidt tot onherstelbaar GEGEVENSVERLIES. Typ <b>{confirmText}</b> om deze interface definitief te verwijderen.
  confirm_delete_interface_element: Dit element wordt permanent verwijderd. Weet u het zeker?
  confirm_delete_invites: "{itemCount, plural, one {Deze uitnodiging wordt} other {De geselecteerde uitnodigingen ({itemCount}) worden} } verwijderd. Weet u het zeker?"
  confirm_delete_link_tab: Dit tabblad en de inhoud ervan worden permanent verwijderd. Weet u het zeker?
  confirm_delete_nested_grid: Deze elementengroep en de inhoud ervan zullen permanent worden verwijderd. Weet u het zeker?
  confirm_delete_object_type: Dit object ({name}) en al zijn records worden permanent verwijderd en kunnen niet worden hersteld. Weet u het zeker? Dit leidt tot onherstelbaar GEGEVENSVERLIES. Typ <b>{confirmText}</b> om dit object definitief te verwijderen.
  confirm_delete_object_type_role: Deze rol ({objectTypeRoleName}) wordt permanent verwijderd. Weet u het zeker?
  confirm_delete_objects: "{itemCount, plural, one {Dit record wordt permanent verwijderd en kan niet worden hersteld} other {De geselecteerde records ({itemCount}) worden permanent verwijderd en kunnen niet worden hersteld} }. Weet u het zeker?"
  confirm_delete_option: Deze optie wordt permanent verwijderd. Weet u het zeker?
  confirm_delete_quick_access: Deze snelle toegang ({quickAccessName}) wordt permanent verwijderd. Weet u het zeker?
  confirm_delete_section: Deze sectie ({name}) wordt permanent verwijderd. Weet u het zeker? Typ <b>{confirmText}</b> om deze sectie definitief te verwijderen.
  confirm_delete_signature: Deze handtekening wordt permanent verwijderd. Weet u het zeker?
  confirm_delete_users: "{itemCount, plural, one {Deze gebruiker wordt} other {De geselecteerde gebruikers ({itemCount}) worden} } permanent verwijderd. Weet u het zeker?"
  confirm_delete_users_from_team: "{itemCount, plural, one {Deze gebruiker wordt} other {De geselecteerde gebruikers ({itemCount}) worden} } permanent weggehaald uit dit team. Weet u het zeker?"
  confirm_delete_validation: Deze validatie wordt definitief verwijderd. Weet u het zeker?
  confirm_delete_view: Deze weergave ({viewName}) wordt definitief verwijderd. Weet u het zeker?
  confirm_discard_custom_thumbnail: Hierdoor wordt de gepersonaliseerde thumbnail verwijderd. Weet u het zeker?
  confirm_lose_rights: De wijzigingen die u hebt aangebracht, kunnen van invloed zijn op gebruikers die aan deze rol zijn toegewezen. Weet u het zeker?
  confirm_overwrite_custom_qr: Er is al een aangepaste QR/barcode aan dit record gekoppeld. Wilt u deze overschrijven?
  confirm_publish_object_type: Dit object bevat nog steeds ontbrekende informatie. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan. Weet u het zeker?
  confirm_recalculation: De formule die aan dit veld is gekoppeld, is gewijzigd. Wilt u de waarden van dit veld voor alle bestaande records opnieuw berekenen?<br>Deze herberekening activeert geen automatiseringen.
  confirm_reject_requests: "{itemCount, plural, one {Dit deelnameverzoekverzoek wordt afgewezen} other {De geselecteerde deelnameverzoeken ({itemCount}) worden afgewezen} }. Weet u het zeker?"
  confirm_removal_current_user_as_interface_owner: U wordt weggehaald uit de lijst van eigenaren en u kunt deze interface niet meer bewerken. Weet u het zeker?
  confirm_removal_current_user_as_object_type_owner: U wordt weggehaald uit de lijst van eigenaren en u kunt dit object niet meer bewerken. Weet u het zeker?
  confirm_remove_interface: Weet u zeker dat u <b>{name}</b> uit <b>{teamName}</b> wilt weghalen?
  confirm_remove_object_type: Weet u zeker dat u <b>{name}</b> uit <b>{teamName}</b> wilt weghalen?
  confirm_trigger_event: Deze record bevat niet-opgeslagen wijzigingen. Wilt u de record opslaan en de actie uitvoeren?
  confirm_unpublish_interface: Weet u zeker dat u de publicatie van <b>{name}</b> in dit team wilt ongedaan maken? Leden zullen er geen toegang meer toe hebben, noch tot de gegevens ervan. Weet u het zeker?
  confirm_unpublish_object_type: Weet u zeker dat u de publicatie van <b>{name}</b> in dit team wilt ongedaan maken? Leden zullen er geen toegang meer toe hebben, noch tot de gegevens ervan. Weet u het zeker?
  confirm_unsaved_changes_automation:
    text_close: Deze automatisatie bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
    text_save: Deze automatisatie bevat niet-opgeslagen wijzigingen. Weet u zeker dat u die wilt opslaan?
  confirm_unsaved_changes_automation_action:
    text_close: Deze actie bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
    text_save: Deze actie bevat niet-opgeslagen wijzigingen. Weet u zeker dat u die wilt opslaan?
  confirm_unsaved_changes_conditional_display: Deze voorwaarde bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_changes_interface: Deze interface bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_changes_object: Deze record bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_changes_record_picker: Deze record heeft niet-opgeslagen wijzigingen. Weet u zeker dat u een andere record wilt selecteren en de niet-opgeslagen wijzigingen wilt verwijderen?
  confirm_unsaved_changes_role: Deze rol bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_changes_show_route_record_picker: Er zijn niet-opgeslagen wijzigingen voor {records}. Weet u zeker dat u deze wilt verwijderen?
  confirm_unsaved_changes_team: Dit team bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_changes_thumbnail: Deze thumbnail bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_form: Dit formulier bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_interface: Deze interface bevat niet-opgeslagen wijzigingen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unsaved_translations:
    target_text: De doelvertaling wordt niet opgeslagen. Weet u zeker dat u de doeltaal wilt wijzigen zonder op te slaan?
    text: Deze vertalingen worden niet opgeslagen. Weet u zeker dat u wilt sluiten zonder op te slaan?
  confirm_unshare_object_type: Weet u zeker dat u het delen van <b>{name}</b> met <b>{teamName}</b> ongedaan wilt maken?
  confirmation_required: Bevestiging vereist
display: Weergave
display_as: Weergeven als
do: Doe
done: Gereed
download: Downloaden
dynamic: Dynamisch
dynamic_options:
  current_user: Ingelogde gebruiker
  now: Huidige datum en tijd
  today: Huidige datum
edit: Bewerken
edit_filter_rules: Filters bewerken
edit_object: Object bewerken
edit_shortcut: Link bewerken
edit_sort_rules: Sorteerregels bewerken
edit_tab: Tabblad bewerken
edit_view: Weergave bewerken
embed: Embed
embed_modal:
  show_tabs: Tabblad selector weergeven
  show_toolbar: Werkbalk weergeven
enable_scanning: Scannen inschakelen
enter_number: Nummer invoeren
enter_something: Voer iets in
enter_text: Tekst invoeren
enter_url: URL invoeren
error:
  empty_interface_owners_list: De interface moet minimaal één eigenaar hebben. Voeg een nieuwe eigenaar toe.
  empty_object_type_owners_list: Het object moet ten minste één eigenaar hebben. Voeg een nieuwe eigenaar toe.
  generic_server_message: Er is een fout opgetreden bij het verwerken van het verzoek.
  object_type_attributes_limit: U heeft het maximum van {object_type_attributes_limit} velden per object bereikt. Overweeg om afzonderlijke objecten te maken met elk minder velden, en koppel deze objecten.
  title: Fout
export:
  cancelled: Exporteren geannuleerd
  completed: Exporteren succesvol
  export: Exporteren
  failed: Exporteren mislukt
  in_progress: Exporteren bezig
'false': Nee
field: Veld
field_calculation: Veldberekening
field_name: Veldnaam
field_name_placeholder: Veldnaam invoeren
field_to_display: Veldwaarde om weer te geven
field_to_search_by: Veld om op te zoeken
filters_applied: "{amount, plural, =0 {Geen filters} =1 {# filter} other {# filters}} toegepast"
fit_element: Passend binnen element (Alleen de eerste preview wordt getoond)
form: Formulier
form_builder:
  label_for_hidden: Veld verbergen in deze variant
  label_for_locked: Weergave in varianten forceren
  object_type_relationship_options:
    cardinalities:
      multiple: Eén <baseObjectType> kan aan meerdere <targetObjectType> worden gekoppeld
      single: Eén <baseObjectType> kan aan maximaal één <targetObjectType> worden gekoppeld
    forward: Voorwaartse relatie
    from: Van
    inverse: Inverse relatie
    object_already_linked: Object dat al is gekoppeld
    object_not_yet_linked: Object dat nog niet is gekoppeld
    to: Naar
  selectable_value_options:
    allow_variant_options: Mogelijkheid om opties in varianten toe te voegen
formula:
  ABS:
    description: Retourneert de absolute waarde van een getal, een getal zonder teken.
    syntax_description: "<ul><li>Number: is het reële getal waarvoor de absolute waarde moet worden gevonden.</li></ul>"
  ALL:
    description: Controleert of alle argumenten TRUE zijn en retourneert TRUE als alle argumenten TRUE zijn.
    syntax_description: "<ul><li>List: lijst met waarden.</li> <li>Parameter: parameternaam die in de functie wordt gebruikt.</li> <li>Function: toegepast op elke waarde(parameter) en controleert of alle waarden waarden voldoen aan een bepaalde voorwaarde.</li></ul>"
  AND:
    description: Controleert of alle argumenten TRUE zijn en retourneert TRUE als alle argumenten TRUE zijn.
    syntax_description: "<ul><li>Logical1, Logical2,...: alle waarden of expressies die kunnen worden geëvalueerd als TRUE of FALSE.</li></ul>"
  ANY:
    description: Controleert of alle argumenten TRUE zijn en retourneert TRUE als een van de argumenten TRUE is.
    syntax_description: "<ul><li>List: lijst met waarden.</li> <li>Parameter: parameternaam die in de functie wordt gebruikt.</li> <li>Function: toegepast op elke waarde (parameter) en controleert of deze aanwezig is waarde voldoet aan een bepaalde voorwaarde.</li></ul>"
  AVG:
    description: Retourneert het gemiddelde (rekenkundig gemiddelde) van de argumenten.
    syntax_description: "<ul><li>Number1, Number2, ...: numerieke argumenten waarvoor het gemiddelde moet worden berekend.</li></ul>"
  CONCAT:
    description: Voegt een lijst met tekstreeksen samen.
    syntax_description: "<ul><li>Text1, Text2, ...: tekenreeksen die moeten worden samengevoegd tot één tekstreeks.</li></ul>"
  CONTAINS:
    description: Controleert of een subtekst binnen een tekst staat en retourneert TRUE of FALSE.
    syntax_description: "<ul><li>Find_text: is de tekst die moet worden gevonden.</li> <li>Within_text: is de tekst die de tekst bevat die moet worden gevonden.</li></ul>"
  COS:
    description: Geeft de cosinus van een hoek terug.
    syntax_description: "<ul><li>Number: is de hoek in radialen waarvoor de cosinus moet worden berekend.</li></ul>"
  COUNT:
    description: Telt het aantal waarden of de lengte van een tekenreeks.
    syntax_description: "<ul><li>Value1, Value2, ...: waarden die uit verschillende gegevenstypen kunnen bestaan.</li></ul>"
  COUNTIF:
    description: Telt alle waarden van een lijst die aan een bepaalde voorwaarde voldoen.
    syntax_description: '<ul><li>List: lijst met waarden.</li> <li>Function: toegepast op elke waarde (parameter) en telt elke waarde die aan een bepaalde voorwaarde voldoet. De parameternaam die in de functie moet worden gebruikt is: "waarde".</li></ul>'
  DURATION:
    description: Retourneert een waarde die aan een datum moet worden toegevoegd of ervan moet worden afgetrokken.
    syntax_description: "<ul><li>Number: is het aantal eenheden.</li><li>Unit: 'years', 'months' en 'days' zijn de beschikbare eenheden.</li></ul>"
  EXP:
    description: Retourneert e verheven tot de macht van een bepaald getal.
    syntax_description: "<ul><li>Number: is de exponent toegepast op het grondtal e. De constante e is gelijk aan 2,71828182845904, de basis van de natuurlijke logaritme.</li></ul>"
  FILTER:
    description: Filtert een lijst met waarden.
    syntax_description: "<ul><li>List: lijst met waarden.</li> <li>Parameter: parameternaam die in de functie wordt gebruikt.</li> <li>Function: toegepast op elke waarde (parameter) en wordt eruit gefilterd de waarden die niet aan een bepaalde voorwaarde voldoen.</li></ul>"
  FIND:
    description: Geeft de positie van een subtekst binnen een tekst terug.
    syntax_description: "<ul><li>Find_text: is de tekst die moet worden gevonden.</li> <li>Within_text: is de tekst die de tekst bevat die moet worden gevonden.</li></ul>"
  IF:
    description: Controleert of aan een voorwaarde is voldaan en retourneert één waarde als TRUE, en een andere waarde als FALSE.
    syntax_description: "<ul><li>Logical_test: is elke waarde of expressie die kan worden geëvalueerd als TRUE of FALSE.</li> <li>Waarde_if_true: is de waarde die wordt geretourneerd als Logical_test TRUE is. Als dit wordt weggelaten, wordt TRUE geretourneerd.</li> <li>Waarde_if_false: is de waarde die wordt geretourneerd als Logical_test FALSE is. Indien dit wordt weggelaten, wordt FALSE geretourneerd.</li></ul>"
  LEFT:
    description: Retourneert het opgegeven aantal tekens vanaf het begin van een teksttekenreeks.
    syntax_description: "<ul><li>Text: is de tekstreeks die de tekens bevat die moeten worden geëxtraheerd.</li> <li>Num_chars: specificeert hoeveel tekens moeten worden geëxtraheerd.</li></ul>"
  LEN:
    description: Retourneert het aantal tekens in een teksttekenreeks.
    syntax_description: "<ul><li>Text: is de tekst waarvan de lengte moet worden berekend. Spaties tellen als tekens.</li></ul>"
  LOG:
    description: Retourneert de logaritme van een getal naar het grondtal dat u opgeeft.
    syntax_description: "<ul><li>Number: is het positieve reële getal waarvan u de logaritme wilt.</li><li>Base: is het grondtal van de logaritme; e indien weggelaten.</li></ul>"
  LOG10:
    description: Retourneert de logaritme met grondtal 10 van een getal.
    syntax_description: "<ul><li>Number: is het positieve reële getal waarvoor u de logaritme met grondtal 10 wilt weten.</li></ul>"
  MAP:
    description: Retourneert een lijst met waarden die wordt gevormd door elke waarde in de lijst 'toe te wijzen' aan een nieuwe waarde door een functie toe te passen.
    syntax_description: "<ul><li>List: lijst met waarden.</li> <li>Parameter: parameternaam die in de functie wordt gebruikt.</li> <li>Function: toegepast op elke waarde(parameter) en retourneert de nieuwe berekende waarde.</li></ul>"
  MAX:
    description: Retourneert de grootste waarde in een reeks waarden.
    syntax_description: "<ul><li>Number1, Number2, ...: getallen waarvan je het maximum moet vinden.</li></ul>"
  MAXOCCURRENCE:
    description: Retourneert de n-de meest voorkomende waarde in een lijst.
    syntax_description: "<ul><li>Lijst: lijst met waarden.</li> <li>n: index van de meest voorkomende waarde die gevonden moet worden.</li></ul>"
  MID:
    description: Retourneert de tekens uit het midden van een tekstreeks, op basis van een beginpositie en lengte.
    syntax_description: "<ul><li>Text: is de tekstreeks waaruit de tekens moeten worden geëxtraheerd.</li> <li>Start_num: is de positie van het eerste teken dat moet worden geëxtraheerd. Het eerste teken in Tekst is 1.</li> <li>Num_chars: specificeert hoeveel tekens er uit Tekst moeten worden geretourneerd.</li></ul>"
  MIN:
    description: Retourneert het kleinste getal in een reeks waarden.
    syntax_description: "<ul><li>Number1, Number2, ...: getallen waarvoor je het minimum moet vinden.</li></ul>"
  NOT:
    description: Verandert FALSE in TRUE, of TRUE in FALSE.
    syntax_description: "<ul><li>Logical: is een waarde of expressie die kan worden geëvalueerd als TRUE of FALSE.</li></ul>"
  OR:
    description: Controleert of een van de argumenten TRUE is, en retourneert TRUE of FALSE. Retourneert alleen FALSE als alle argumenten FALSE zijn.
    syntax_description: "<ul><li>Logical1, Logical2, ...: voorwaarden om te testen die TRUE of FALSE kunnen zijn.</li></ul>"
  RIGHT:
    description: Retourneert het opgegeven aantal tekens vanaf het einde van een teksttekenreeks.
    syntax_description: "<ul><li>Text: is de tekstreeks die de tekens bevat die moeten worden geëxtraheerd.</li> <li>Num_chars: specificeert hoeveel tekens moeten worden geëxtraheerd.</li></ul>"
  ROUND:
    description: Rondt een getal af op een opgegeven aantal cijfers.
    syntax_description: "<ul><li>Number: is het getal dat moet worden afgerond.</li> <li>Num_digits: is het aantal cijfers waarnaar moet worden afgerond.</li></ul>"
  ROUNDDOWN:
    description: Rondt een getal naar beneden af, richting nul.
    syntax_description: "<ul><li>Number: is een reëel getal dat naar beneden moet worden afgerond.</li> <li>Num_digits: is het aantal cijfers waarnaar moet worden afgerond.</li></ul>"
  ROUNDUP:
    description: Rondt een getal naar boven af, weg van nul.
    syntax_description: "<ul><li>Number: is een reëel getal dat naar boven moet worden afgerond.</li> <li>Num_digits: is het aantal cijfers waarnaar moet worden afgerond.</li></ul>"
  SIN:
    description: Geeft de sinus van een hoek terug.
    syntax_description: "<ul><li>Number: is de hoek in radialen waarvoor de sinus moet worden berekend.</li></ul>"
  SQRT:
    description: Retourneert de vierkantswortel van een getal.
    syntax_description: "<ul><li>Number: is het getal waarvan je de vierkantswortel wilt.</ul></li>"
  STDEV:
    description: Retourneert de standaardafwijking op basis van een steekproef.
    syntax_description: "<ul><li>Number1, Number2, ...: getallen waarvoor de standaarddeviatie van de steekproef moet worden berekend.</li></ul>"
  SUBSTITUTE:
    description: Vervangt bestaande tekst door nieuwe tekst in een tekstreeks.
    syntax_description: "<ul><li>Text: is de tekst waarin tekens moeten worden vervangen.</li> <li>Old_text: is de bestaande tekst die moet worden vervangen.</li> <li>New_text: is de tekst waarmee oude_tekst moet worden vervangen. </li></ul>"
  SUM:
    description: Voegt alle getallen toe.
    syntax_description: "<ul><li>Nummer1, Number2, ...: de getallen die moeten worden toegevoegd.</li></ul>"
  SWITCH:
    description: Evalueert een expressie op basis van een lijst met waarden en retourneert het resultaat dat overeenkomt met de eerste overeenkomende waarde. Als er geen overeenkomst is, wordt een optionele standaardwaarde geretourneerd.
    syntax_description: "<ul><li>Expression: is een expressie die moet worden geëvalueerd.</li><li>Value1, Value2, ...: waarde die overeenkomt met de expressie.</li><li>Result1, Result2, .. .: waarde die wordt geretourneerd als er een overeenkomst is.</li><li>Default_value: waarde die wordt geretourneerd als er geen overeenkomst is.</li></ul>"
  TAN:
    description: Geeft de tangens van een hoek terug.
    syntax_description: "<ul><li>Number: is de hoek in radialen waarvoor de raaklijn moet worden berekend.</li></ul>"
  VAR:
    description: Retourneert de variantie op basis van een steekproef.
    syntax_description: "<ul><li>Number1, Number2, ...: getallen waarvoor de steekproefvariantie moet worden berekend.</li></ul>"
  XOR:
    description: Retourneert een logische 'Exclusieve OR' van alle argumenten.
    syntax_description: "<ul><li>Logical1, Logical2, ...: voorwaarden om te testen die TRUE of FALSE kunnen zijn.</li></ul>"
  syntax: Syntaxis
function_assistant: Functie assistent
general: Algemeen
got_it: Begrepen
grid: Rooster
grouped_by: Gegroepeerd op
help: Help
hide_all: Verberg alles
how_to_use_in: Hoe te gebruiken met
icon: Icoon
include: Toevoegen
include_descendants: Afstammende teams opnemen
incorrect_qr_format: Ongeldig QR-formaat
input: Velden
input_type:
  attachment: Bijlage
  button: Knop
  checkbox: Selectievakje
  checkmark: Vinkje
  date: Datum
  datetime: Datum en tijd
  description: Vaste tekst
  descriptive_attachment: Vaste afbeeldingen / media
  dropdown_list: Keuzelijst
  linked_object: Gekoppeld Houston object
  lookup: Veld van een gekoppeld Houston-object
  number: Nummer
  radio: Radio
  range_select: Interval
  rich_text: Rijke tekst
  short_text: Korte tekst
  signature: Handtekening
  switch: Schakelaar
  textarea: Tekstvak
  type: Veldtype
  url: URL
  user: Gebruiker
insert_function: Functie invoegen
interface:
  attachment: Vaste afbeeldingen / media
  button: Knop
  calendar_chart: Kalenderdiagram
  contact_text: Dit element is nog niet configureerbaar in de gebruikersinterface. Hulp nodig? Neem contact met ons op via <a href="mailto:<EMAIL>"><EMAIL></a>
  filter: Filter
  filter_through_element: EN filteren via
  gage_chart: Meterdiagram
  html: HTML
  html_placeholder: HTML invoeren
  interface: Interface
  interface_not_available: Deze interface is niet beschikbaar in het geselecteerde team.
  linebar_chart: Lijn-/Staaf-/Vlakdiagram
  links: Links
  nested_grid: Elementengroep
  number: Nummer
  pie_chart: Cirkel-/Ringdiagram
  pyramid_chart: Piramidediagram
  radar_chart: Radardiagram
  read_only: Alleen-lezen
  record_in_record_picker: Record "{record}" in recordkiezer "{record_picker}"
  record_picker: Recordkiezer
  show_label: Toon label
  source: Bron
  text: Tekst
  unit: Eenheid
interface_editor:
  action_type_selector:
    data_change: Gegevens wijzigen
    delete_record_action: Een record verwijderen
    external_url_navigation: Open externe URL
    interface_section_navigation: Ga naar tabblad van de interface
    navigation: Navigeren
    place_holder: Selecteer een actietype
    save_record_action: Sla een record op
    trigger_automation_action: Activeer een automatisatie
  actions:
    navigation_action:
      open_in_new_tab: Openen in nieuw tabblad
interfaces_managed_by_me: Interfaces onder mijn beheer
invitation:
  add_message: Een bericht toevoegen... (aanbevolen)
  date_and_time: Datum en tijd uitnodiging
  delete_invite: Uitnodiging verwijderen
  email: E-mail
  invite_user_by_email: Gebruiker per e-mail uitnodigen
  invites: Uitnodigingen
  message: Bericht
  send_invite: Stuur een uitnodiging
  toast:
    invitation_sent: Uitnodiging verzonden
    title: Uitnodiging gebruiker
    user_already_in_team: Deze gebruiker is al lid van het team
    user_found: Dit e-mailadres komt overeen met gebruiker {name} die nu aan het team is toegevoegd
label_display: Labelweergave
language: Taal
languages:
  en: Engels
  es: Spaans
  fr: Frans
  nl: Nederlands
last_activity: Laatste Activiteit
layout: Lay-out
less: minder
linked_fields: Gekoppelde velden
linked_object: Gekoppeld object
linked_object_field: Veld van gekoppeld object
linked_objects: Gekoppelde objecten
loading: Bezig met laden...
lookup_from: Opzoeken vanuit
lookup_through_field: via veld "{field}"
max: Max
members: Leden
memberships_awaiting_approval: Lidmaatschappen wachten op goedkeuring
message: Bericht
min: Min
minutes: Minuten
missing_video_permission: Er is geen camera aanwezig of er is geen toestemming gegeven.
more: meer
more_info: Meer info
move: Verplaatsen
move_form_element_modal_title: Verplaats veld naar een ander tabblad
move_interface_modal_title: Verplaats element naar ...
multiple: Meerdere
my_teams: Mijn teams
navigation:
  home: Home
  interfaces: Interfaces
  not_found: Niet gevonden
  objects: Objecten
new: Nieuw
new_interface: Nieuwe interface
new_object_type: Nieuw object
new_section: Nieuwe sectie
new_shortcut: Nieuwe link
new_tab: Nieuw tabblad
new_version_info: Een nieuwe versie is beschikbaar.
new_version_refresh: Pagina opnieuw inladen
no_access: Geen toegang
no_access_tooltip: Je bent geen lid van dit team
no_filters: Geen filters
no_grouped_attribute_warning: Er is geen veld geselecteerd voor het groeperen van records.
no_home_page_description: Er is geen startpagina gedefinieerd voor dit team.
no_items_to_display: Er zijn geen items om weer te geven.
no_results_found: Geen resultaten gevonden
no_team_filters: Geen teamfilters
non_editable_view: Deze weergave kan niet worden bewerkt
none: Geen
not_active: Niet actief
not_found_description: De pagina die u probeerde te openen, is niet gevonden.
notify_team_admins: Teambeheerders op de hoogte stellen
object_form_link: Link naar formulier
object_hyperlink: Link naar het record
object_not_available: Dit object is niet beschikbaar in het geselecteerde team.
object_print_modal:
  all_tabs: Alle tabbladen
  current_tab: Huidig tabblad
object_type: Object
object_type_description:
  editor: Informatie voor objectbewerkers
  empty: "(Nog geen beschrijving)"
  user: Informatie voor alle gebruikers
object_type_share_violation: Het object is al gedeeld met het team.
objects_managed_by_me: Objecten onder mijn beheer
ok: OK
'on': Op
operations:
  after: Na
  before: Voor
  blank: Is leeg
  contains: Bevat
  equals: Gelijk aan
  greater_than: Groter dan
  in: In
  lower_than: Kleiner dan
  not_contains: Bevat geen
  not_equals: Niet gelijk aan
  presence: Is ingevuld
  within: In
operator: Operator
option: Optie
options: Opties
or: Of
other: Overige
owned: Onder mijn beheer
owners: Eigenaren
personal: Persoonlijk
pin_tab: Tabblad vastpinnen
placeholder: Plaatshouder
position:
  inside: Binnenin
  top: Bovenaan
power_bi: Power BI
print: Afdrukken
properties: Eigenschappen
publication_settings: Publicatie-instellingen
publish: Publiceren
publish_dialog:
  default_assignment: Wijs automatisch de rollen Maker en Bewerker toe aan dit team
  descendants: Dit team en al zijn afstammende teams
  interface: Interface publiceren
  no_default_assignment: Laat mij de roltoewijzingen kiezen
  object: Object publiceren
  redirect: Breng me na publicatie naar de rollenpagina
  role_info: Om records voor een object te kunnen maken, bijwerken en verwijderen, moeten gebruikers een rol hebben die dit recht heeft
  team: Alleen dit team
publish_in: Publiceer binnen
publish_warnings:
  automation: Deze automatisering bevat ontbrekende informatie. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan.
  automations: Er zijn één of meerdere automatiseringen waarbij informatie ontbreekt. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan.
  form_element: Dit veld bevat ontbrekende informatie. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan.
  form_elements: Er zijn één of meerdere velden waarin informatie ontbreekt. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan.
  role: Deze rol bevat ontbrekende informatie. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan.
  roles: Er zijn één of meerdere rollen waarvoor informatie ontbreekt. Dit kan van invloed zijn op het gedrag en/of de bruikbaarheid ervan.
published: Gepubliceerd
qr_bar_code: QR/barcode
qr_download: Download Houston QR-code
qr_modal:
  placeholder: Voer code in
  title: Voer QR/barcode in voor dit record
query:
  current: Huidige
  current_month: Huidige maand
  current_week: Huidige week
  current_year: Huidig jaar
  day: Dag
  exact_date: Exacte datum
  filter: Filteren
  filter_by_this_field: Filter op dit veld
  from: Van
  from_team: Van team
  last_month: Vorige maand
  last_week: Vorige week
  last_year: Vorig jaar
  linked_with: Gekoppeld aan
  month: Maand
  new_filter_condition: Nieuwe filter
  new_sort_option: Nieuwe sorteerregel
  new_team_filter_condition: Nieuwe teamfilter
  new_view: Nieuwe weergave
  next: Volgende
  no_filters_applied: Er zijn geen filters toegepast.
  no_sorts_applied: Er zijn geen sorteerregels toegepast.
  previous: Vorige
  select_option: Kies een optie
  sort: Sorteren
  sort_asc: Sorteer oplopend
  sort_desc: Sorteer aflopend
  to: Tot
  today: Vandaag
  today_minus: Vandaag min
  today_plus: Vandaag plus
  week: Week
  where: Waar
  year: Jaar
quick_access: Snelle toegang
recently_used: Recent gebruikt
records_shared: Records gedeeld met dit team
records_subteams: Records van subteams
refresh: Vernieuwen
remove_interface_from_team: Interface uit dit team weghalen
remove_object_from_team: Object uit dit team weghalen
revision_history:
  title: Revisiegeschiedenis
  user:
    create: "<b>{user}</b> heeft dit record gemaakt"
    update: "<b>{user}</b> heeft dit record bewerkt"
  you:
    create: "<b>U</b> heeft dit record gemaakt"
    update: "<b>U</b> heeft dit record bewerkt"
role:
  access_rights: Toegangsrechten
  assigned_to: Toegewezen aan
  author_of_a_record: Auteur van een record
  edit_role: Rol bewerken
  fixed_set_of_users: Vaste groep gebruikers
  new_role: Nieuwe rol
  role: Rol
  through_attribute: Door veld
  users_from_specific_field: Gebruikers uit een specifiek veld
roles: Rollen
root_level: Hoofdniveau
save: Opslaan
saving: Bezig met opslaan
scan: Scannen
search: Zoeken
search_icons: Iconen zoeken
section: Sectie
section_name: Sectie naam
select_color: Selecteer kleur
select_column_header: Selecteer een kolomkop
select_column_header_description: Selecteer een veld dat u wilt gebruiken voor het groeperen van records in deze {viewType} weergave
select_create_which_object: Selecteer het object dat u wilt maken
select_date: Selecteer datum
select_field: Selecteer veld
select_filter: Selecteer filter
select_object: Selecteer object
select_placeholder: Selecteer iets
select_record: Selecteer record
select_tab: Selecteer tabblad
select_team: Selecteer team
select_user: Selecteer gebruiker
selectable_values: Selecteerbare waarden
send_email_to: Stuur een email naar
settings: Instellingen
share: Delen
share_interface_with_new_teams: Interface deelen met teams
share_object_with_new_teams: Object delen met teams
show_all: Toon alles
show_background_image: Achtergrondafbeelding weergeven
show_details: Details weergeven
show_details_in_header: Details in header tonen
show_field_names: Toon veldnamen
show_more: Meer weergeven
show_options: 'Opties weergeven:'
show_thumbnail_icon: Objectpictogram weergeven
sign_here: Hier handtekenen
sign_out: Afmelden
single: Enkel
size_limit_warning: Groottelimiet overschreden
someone: Iemand
sort_rules_applied: "{amount, plural, =0 {Geen sorteerregels} =1 {# sorteerregel} other {# sorteerregels}} toegepast"
start_houston: Start Houston
status: Toestand
step: Stap
stop_scanning: Stop met scannen
structure: Structuur
subject: Onderwerp
submit: Verzenden
subteams: Subteams
subtitle: Ondertitel
support: Ondersteuning
system: Systeem
tab: Tabblad
tab_name: Tab naam
team: Team
team_filterable: Records filteren op basis van het geselecteerde team
team_filterable_user_relationship: Filter gebruikers op basis van het geselecteerde team
teams: Teams
teams_to_scope_data_to: Gegevens van
theme: Weergavemodus
theme_dark: Donker (experimenteel)
theme_light: Licht
thumbnail:
  background_image: Achtergrondafbeelding
  card: Kaart
  date_time_display_mode:
    date_and_time: Datum en tijd
    date_only: Alleen datum
    time_only: Alleen tijd
  default_card: Standaard gebruikt in de galerij.
  default_multiple_rows: Standaard gebruikt in lijsten en formulieren.
  default_single_column: Kan in formulieren worden gebruikt.
  default_single_row: Standaard gebruikt in Tabellen.
  edit_thumbnail: Thumbnail bewerken
  grid: Meerdere rijen
  horizontal: Enkele rij
  prefix: Prefix
  row: Rij
  selected_field: Geselecteerd thumbnailveld
  show_title_row: Toon titelrij
  subtitle: Subtitel
  suffix: Suffix
  thumbnail: Thumbnail
  title: Titel
  type: Thumnbailtype
  vertical: Enkele kolom
thumbnails: Thumbnails
time_unit: Tijdseenheid
timeline:
  end_date: Einddatum
  start_date: Startdatum
timeline_settings: Tijdlijninstellingen
title: Titel
title_field: Titelveld
translation_management: Vertaalbeheer
'true': Ja
type: Type
unassigned: Niet toegewezen
uncategorized: Niet gecategoriseerd
unlink_selected: Geselecteerde records loskoppelen
unpublish: Publicatie ongedaan maken
unpublished: In afwachting van publicatie
unshare: Delen ongedaan maken
uploader:
  action: Actie
  one_or_more_files: Upload 1 of meer bestanden
  take_picture: Maak foto
  take_video: Neem video op
  uploading: Uploaden ...
used_in: Gebruikt in
user_account:
  activate: Activeer gebruikersaccount
  deactivate: Deactiveer gebruikersaccount
user_guide: Gebruikershandleiding
user_join_requests:
  approvals: Goedkeuringen
  approve: Goedkeuren
  approve_requests: Selectie goedkeuren
  awaiting_approval: In afwachting van goedkeuring
  date_and_time: Datum en tijd van aanvraag
  reject: Afwijzen
  reject_requests: Selectie afwijzen
  toast:
    body: We hebben de beheerders gevraagd uw verzoek om lid te worden goed te keuren.
    title: Deelnameverzoek verzonden
user_not_found: Gebruiker niet gevonden?
users: Gebruikers
validation: Validatie
validation_type:
  equals: moet gelijk zijn aan
  greater_than: moet groter zijn dan
  less_than: moet minder zijn dan
  max_length: mag niet langer zijn dan
  min_length: mag niet korter zijn dan
  other_than: moet anders zijn dan
  presence: moet ingevuld zijn
validations: Validaties
value: Waarde
value_set_on_creation: Waarde ingesteld bij creatie
variants:
  master: Master
view: Weergave
visibility: Zichtbaarheid
visualisation:
  multiObjectList: Lijst met meerdere objecten
  objectCalendar: Kalender
  objectGallery: Gallerij
  objectKanban: Kanban
  objectList: Lijst
  objectTable: Tabel
  objectTimeline: Tijdlijn
warning_changes_object_type: Wijzigingen aan dit object hebben invloed op alle records voor dit object. Ga voorzichtig te werk!
warning_optional_field: Dit veld is optioneel en is mogelijk niet beschikbaar in varianten.
welcome: Welkom in Houston. Dien een verzoek in om lid te worden van een team om van start te gaan met Houston
