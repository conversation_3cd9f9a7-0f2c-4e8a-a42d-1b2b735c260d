module Efficy
  class Product < Efficy::BaseModel
    SUPPLIERS = {
      0 => :tero_catering
    }.freeze

    def id
      get_attribute(:K_PRODUCT)
    end

    def relation_id
      get_attribute(:K_RELATION)
    end

    def kind
      get_attribute('NAME_1')
    end

    def comment?
      kind == 'Comment *'
    end

    def family
      get_attribute(:R_FAMILY)
    end

    def activity_product?
      family == 'ACTIVITES'
    end

    def restauration_product?
      family == 'RESTAURATION'
    end

    def quantity
      get_attribute(:QUANTITY)
    end

    def name
      get_attribute(:NAME)
    end

    def comment
      get_attribute(:COMMENT)
    end

    def description
      get_attribute(:F_DESCRIPTION)
    end

    def supplier
      SUPPLIERS[get_attribute(:F_SELECTED_SUPPLIER)]
    end

    def tero_catering?
      supplier == :tero_catering
    end

    def add_description(new_description)
      set_attribute(
        :F_DESCRIPTION,
        description.present? ? description.concat("\n", new_description) : new_description
      )
    end

    def start_date
      Efficy::Util.parse_datetime(get_attribute(:F_D_START))
    end

    def position
      get_attribute(:K_SORT)
    end

    ACTIVITY_NAME_REGEX = /(?>>\s*|\d+\s*-\s*)\d+\s*(?>pax|PAX|Pax)/
    # map names like:
    # 'Teambuilding 2012 10-20 pax'
    # 'Teambuilding 2012 > 20PAX
    # onto
    # 'Teambuilding 2012'
    def normalized_name
      name&.gsub(ACTIVITY_NAME_REGEX, '')
          &.strip
    end

    # Some 'products' are not real products, but a hacky way to add comments to a preceding product.
    # This method removes those fake products and combines them with their real counterpart
    NIL_PRODUCT_ID = 99999001
    # rubocop:disable Metrics/PerceivedComplexity
    def self.condense_products!(products)
      products = products.sort do |a, b|
        if a.position.nil?
          1
        elsif b.position.nil?
          -1
        else
          a.position - b.position
        end
      end
      last_product = nil
      products.filter do |product|
        if product.id != NIL_PRODUCT_ID
          last_product = product
          next true
        end
        next if last_product.nil?

        if product.comment?
          last_product.add_description(product.description) if product.description.present?
        else
          last_product = nil
        end
        false
      end
    end
    # rubocop:enable Metrics/PerceivedComplexity
  end
end
