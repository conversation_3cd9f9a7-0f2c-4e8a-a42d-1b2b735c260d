module Efficy
  module Util
    def self.parse_datetime(iso8601_timestamp)
      local_time = Time.zone.parse(iso8601_timestamp) if iso8601_timestamp.present?
      return nil if local_time.blank?

      datetime = DateTime.new
                         .in_time_zone('Europe/Brussels')
                         .change(
                           year: local_time.year,
                           month: local_time.month,
                           day: local_time.day,
                           hour: local_time.hour,
                           min: local_time.min,
                           sec: local_time.sec
                         ).utc
      return nil if datetime < Time.utc(2000)

      datetime.iso8601
    end
  end
end
