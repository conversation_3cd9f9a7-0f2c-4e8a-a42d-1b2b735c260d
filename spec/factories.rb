FactoryBot.automatically_define_enum_traits = false
FactoryBot.define do
  factory :interface_element_button, class: 'Interface::Element::Button' do
    sequence(:label) { |n| "Interface Element Button #{n}" }
    interface_element
  end

  factory :interface_section_navigation_action, class: 'Interface::InterfaceSectionNavigationAction' do
    interface_element_button factory: :interface_element_button, action_type: 'InterfaceSectionNavigationAction'

    after(:build) do |interface_section_navigation_action|
      if interface_section_navigation_action.section.blank?
        existing_section = interface_section_navigation_action.interface_element_button.interface_element.section
        new_section = create(:section, interface: existing_section.interface, position: existing_section.position + 1)

        interface_section_navigation_action.section = new_section
      end
    end
  end

  factory :external_url_navigation_action, class: 'Interface::ExternalUrlNavigationAction' do
    url { 'https://gethouston.com/' }
    interface_element_button factory: :interface_element_button, action_type: 'ExternalUrlNavigationAction'
  end

  factory :interface_team_relationship do
    interface
    team
    published { false }
  end

  factory :interface_team_data_scope do
    interface
    team
  end

  factory :automation_ai_action_attribute do
    automation_ai_action
    object_type_attribute
  end

  factory :automation_ai_action do
    automation_action { association(:automation_action, action_type: :ai) }
  end

  factory :ai_token_usage, class: 'Analytics::AiTokenUsage' do
    automation_ai_action
  end

  factory :invitation do
    user
    team
    sequence(:invited_email) { |n| "user.#{n}@houston.software" }
  end

  factory :user_team_join_request do
    user
    team
  end

  factory :access_token do
    token { SecureRandom.alphanumeric(8) }
    kind { 0 }
    user

    trait :data_source do
      kind { 1 }
    end
  end

  factory :token do
    kind { 0 }
    expires_at { 7.days.from_now }
    user
  end

  factory :automation_mail_action_attribute do
    automation_mail_action
    position { 1 }
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
  end

  factory :automation_trigger_attribute do
    automation_trigger
    object_type_attribute { column_name ? nil : association(:object_type_attribute) }
    condition_type { 'changes' }
  end

  factory :automation_mail_action_user_relationship do
    user
    automation_mail_action
  end

  factory :automation_mail_action_role_relationship do
    object_type_role factory: [:object_type_role, :custom]
    automation_mail_action
  end

  factory :data_source do
    view
    user
    access_token factory: [:access_token, :data_source]
  end

  factory :widget do
    interface_element
    html { 'MyText' }
  end

  factory :section do
    sequence(:name) { |n| "Section #{n}" }
    position { 1 }
    interface
  end

  factory :interface do
    sequence(:name) { |n| "Interface #{n}" }
    user
  end

  factory :interface_element, class: 'Interface::Element' do
    sequence(:name) { |n| "element #{n}" }
    section
    element_type { :widget }
    grid_position { { x: 0, y: 0, w: 10, h: 10 } }
    trait :widget do
      after(:create) do |element|
        create(:widget, interface_element: element)
      end
    end
    trait :attachment do
      element_type { :attachment }
      after(:create) do |element|
        create(:interface_element_attachment, interface_element: element)
      end
    end
    trait :view do
      element_type { :view }
      after(:create) do |element|
        create(:view, interface_element: element)
      end
    end
    trait :field do
      element_type { :field }
      after(:create) do |element|
        create(:interface_element_field, interface_element: element)
      end
    end
    trait :record_picker do
      element_type { :record_picker }
      after(:create) do |element|
        create(:interface_element_record_picker, interface_element: element)
      end
    end
    trait :nested_grid do
      element_type { :nested_grid }
    end

    trait :link do
      element_type { :link }
      after(:create) do |element|
        create(:interface_element_link, interface_element: element)
      end
    end
  end

  factory :interface_element_field, class: 'Interface::Element::Field' do
    interface_element
    object_type_attribute
    interface_element_record_picker
  end

  factory :interface_element_link, class: 'Interface::Element::Link' do
    interface_element
  end

  factory :interface_element_link_tab, class: 'Interface::Element::Link::Tab' do
    interface_element_link
    position { 0 }
    name { Faker::Lorem.word }
  end

  factory :interface_element_record_picker, class: 'Interface::Element::RecordPicker' do
    object_type
    interface_element
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
  end

  factory :interface_element_attachment, class: 'Interface::Element::Attachment' do
    display_type { 'grid' }
    interface_element
  end

  factory :interface_element_filter, class: 'Interface::Element::Filter' do
    object_type
    interface_element
  end

  factory :user_interface_relationship do
    user
    interface
  end

  factory :backup do
    file { nil }
  end

  factory :connection, class: 'Analytics::Connection' do
    user
  end

  factory :user do
    sequence(:name) { |n| "User #{n}" }
    sso_provider { 'developer' }
    sequence(:email) { |n| "user.#{n}@houston.software" }
  end

  factory :instance do
    sequence(:subdomain) { |n| "test#{n}" }
  end

  factory :team do
    name { Faker::Company.profession }
  end

  factory :user_team_relationship do
    user
    team
  end

  factory :object_type_team_relationship do
    object_type
    team
  end

  factory :object_type_role_team_relationship do
    object_type_role factory: [:object_type_role, :custom]
    team
  end

  factory :object_type, class: 'Custom::ObjectType' do
    sequence(:name) { |n| "Machine #{n}" }
  end

  factory :object_type_variant, class: 'Custom::ObjectTypeVariant' do
    object_type
    team
  end

  factory :object_type_attribute, class: 'Custom::ObjectTypeAttribute' do
    object_type
    sequence(:name) { |n| "Name #{n}" }
    sequence(:key) { |n| "name#{n}" }
    sequence(:field_identifier) { |n| "name#{n}" }
    data_type { 'String' }
    can_update { true }

    trait :user_relationship do
      data_type { 'Relationship' }
      relationship_kind { 'single' }
      inverse_relationship_kind { 'multiple' }
      target_class { 'User' }
      target_column_name { 'name' }
    end
    trait :object_relationship do
      data_type { 'Relationship' }
      relationship_kind { 'single' }
      inverse_relationship_kind { 'multiple' }
      target_class { 'Object' }
      target_column_name { target_object_type_attribute || target_object_type_attribute_id ? nil : 'id' }
      target_object_type factory: :object_type
    end
  end

  factory :object_type_relationship, class: 'Custom::ObjectTypeRelationship' do
    object_type_attribute factory: [:object_type_attribute, :object_relationship]
    inverse_object_type_attribute factory: [:object_type_attribute, :object_relationship]
  end

  factory :object_type_attribute_validation do
    object_type_attribute
    validation_type { 'equals' }
  end

  factory :object_type_role, class: 'Custom::ObjectTypeRole' do
    object_type
    sequence(:name) { |n| "ObjectTypeRole #{n}" }
    role_type { Custom::ObjectTypeRole::ROLE_TYPES[:CUSTOM] }

    trait :author do
      role_type { Custom::ObjectTypeRole::ROLE_TYPES[:AUTHOR] }
    end

    trait :creator do
      role_type { Custom::ObjectTypeRole::ROLE_TYPES[:CREATOR] }
    end

    trait :editor do
      role_type { Custom::ObjectTypeRole::ROLE_TYPES[:EDITOR] }
    end

    trait :custom do
      role_type { Custom::ObjectTypeRole::ROLE_TYPES[:CUSTOM] }
    end
  end

  factory :object, class: 'Custom::Object' do
    object_type
  end

  factory :object_attribute, class: 'Custom::ObjectAttribute' do
    object
    object_type_attribute
  end

  factory :object_relationship, class: 'Custom::ObjectRelationship' do
    object
    object_type_attribute
    model factory: :object
  end

  factory :object_attachment, class: 'Custom::ObjectAttachment' do
    object
    object_type_attribute
    name { Faker::File.file_name }

    trait :with_file do
      after(:build) do |object_attachment|
        object_attachment.file.attach(io: Rails.root.join('spec/fixtures/images/the_goat.jpeg').open,
                                      filename: object_attachment.name)
      end
    end

    trait :with_large_file do
      after(:build) do |object_attachment|
        object_attachment.file.attach(io: Rails.root.join('spec/fixtures/images/a_9m_pic.jpeg').open,
                                      filename: object_attachment.name)
      end
    end

    trait :with_pdf do
      after(:build) do |object_attachment|
        object_attachment.file.attach(io: Rails.root.join('spec/fixtures/documents/great_depression.pdf').open,
                                      filename: object_attachment.name)
      end
    end

    trait :with_video do
      after(:build) do |object_attachment|
        object_attachment.file.attach(io: Rails.root.join('spec/fixtures/videos/houston.mp4').open,
                                      filename: object_attachment.name)
      end
    end
  end

  factory :form_tab do
    object_type
    position { 1 }
  end

  factory :form_element do
    form_tab
    input_type { 'Description' }
    position { 1 }
  end

  factory :attachment do
    model factory: :form_element
    name { Faker::File.file_name }

    trait :with_file do
      after(:build) do |attachment|
        attachment.file.attach(io: Rails.root.join('spec/fixtures/images/the_goat.jpeg').open,
                               filename: attachment.name)
      end
    end

    trait :with_large_file do
      after(:build) do |attachment|
        attachment.file.attach(io: Rails.root.join('spec/fixtures/images/a_9m_pic.jpeg').open,
                               filename: attachment.name)
      end
    end

    trait :with_pdf do
      after(:build) do |attachment|
        attachment.file.attach(io: Rails.root.join('spec/fixtures/documents/great_depression.pdf').open,
                               filename: attachment.name)
      end
    end

    trait :with_video do
      after(:build) do |attachment|
        attachment.file.attach(io: Rails.root.join('spec/fixtures/videos/houston.mp4').open,
                               filename: attachment.name)
      end
    end
  end

  factory :visualisation do
    view_type { 'lineChart' }
    view
  end

  factory :thumbnail do
    object_type
    default { visualisation || visualisation_id || form_element || form_element_id ? false : true }

    trait :with_visualisation do
      visualisation
      default { false }
    end

    trait :with_form_element do
      form_element
      default { false }
    end

    trait :with_default do
      default { true }
    end
  end

  factory :thumbnail_attribute do
    thumbnail
    row { 0 }
    column { 0 }
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
  end

  factory :announcement do
    body { Faker::Lorem.word }
  end

  factory :seen_announcement do
    user
    announcement
  end

  factory :object_type_object_type_group_relationship do
    object_type_group
    object_type
  end

  factory :object_type_group do
    name { Faker::Lorem.word }
  end

  factory :object_type_user_relationship do
    object_type
    user
    last_visited_at { Time.current }
  end

  factory :object_query_rule do
    object_query_view
    sequence(:position)
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
    rule_type { 'sort' }
    sort_order { 'asc' }
  end

  factory :object_query_view do
    dependency factory: :view
  end

  factory :object_query_filter_rule, class: 'ObjectQueryRule' do
    object_query_view
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
    sort_order { nil }
    rule_type { 'filter' }
    operator { 'equals' }
    sequence(:position)
  end

  factory :object_query_sort_rule, class: 'ObjectQueryRule' do
    object_query_view
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
    rule_type { 'sort' }
    sort_order { 'asc' }
    sequence(:position)
  end

  factory :qr_code do
    code { nil }
    qr_type { 0 }
  end

  factory :job do
    job_type { 'Export' }
    user

    factory :export_job, class: 'Job::Export' do
      job_type { 'Export' }
    end

    factory :automation_job, class: 'Job::Automation' do
      job_type { 'Automation' }
      automation
    end

    factory :assistant_job, class: 'Job::Assistant' do
      job_type { 'Assistant' }
    end
  end

  factory :automation do
    name { Faker::Company.buzzword }
    active { true }
    schedule_type { :instant }
    object_type
  end

  factory :automation_trigger do
    automation
    object_type
    trigger_type { :create }
    condition_operator { 'or' }
  end

  factory :automation_action do
    automation
    action_type { :mail }
  end

  factory :automation_mail_action do
    automation_action { association(:automation_action, action_type: :mail) }
    mail_type { :object_creation }
    exclude_service_invoker { true }
  end

  factory :automation_static_data_action do
    automation_action { association(:automation_action, action_type: :static_data) }
    object_type_attribute
  end

  factory :automation_integration_action do
    automation_action { association(:automation_action, action_type: :integration) }
    integration_action
  end

  factory :automation_update_object_action do
    automation_action { association(:automation_action, action_type: :update_object) }
  end

  factory :automation_update_object_action_attribute do
    automation_update_object_action
    object_type_attribute
  end

  factory :automation_webhook_action do
    url { 'https://www.example.com' }
    automation_action { association(:automation_action, action_type: :webhook) }
  end

  factory :automation_webhook_action_attribute do
    automation_webhook_action
    column_name { object_type_attribute || object_type_attribute_id ? nil : 'id' }
  end

  factory :frontend_event do
    form_element
    automation
  end

  factory :conditional_display do
    display_type { 'show' }
    subject factory: :form_tab

    trait :filter_selectable do
      display_type { 'filter_selectable' }
    end
  end

  factory :condition_rule do
    conditional_display
    rule_type { 'Object' }
    operator { 'equals' }
    value { 1 }
    column_name { object_type_attribute || object_type_attribute_id || rule_type == 'User' ? nil : 'id' }
  end

  factory :conditional_display_applicability do
    applicable factory: :object_type_attribute_selectable_value
    conditional_display factory: [:conditional_display, :filter_selectable]
  end

  factory :calculation do
    object_type_attribute factory: :object_type_attribute, calculated: true

    trait :lookup do
      calculation_type { :lookup } # also default but for consistency
    end

    trait :formula do
      formula { '1 + 1' }
      calculation_type { :formula }
    end
  end

  factory :object_type_attribute_path do
    calculation
    data_attribute factory: :object_type_attribute
  end

  factory :integration do
    type { 'Solvace' }
    after(:build, &:generate_and_set_webhook_identifier)

    factory :solvace_integration, class: 'Integration::Solvace' do
      type { 'Solvace' }
    end

    factory :api_integration, class: 'Integration::Api' do
      type { 'Api' }
      options { { 'url' => 'http://www.example.com' } }
    end

    factory :efficy_integration, class: 'Integration::Efficy' do
      type { 'Efficy' }
      options { { 'url' => 'https://www.efficy.com/crm/json?ss_id=1' } }
    end

    factory :tero_integration, class: 'Integration::Tero' do
      type { 'Tero' }
    end
  end

  factory :integration_action, class: 'Integration::Action' do
    sequence(:name) { |n| "Integration Action #{n}" }
    type { 'Integration::Api::RequestAction' }
    integration

    factory :api_request_action, class: 'Integration::Api::RequestAction' do
      type { 'Integration::Api::RequestAction' }
      options { { 'relative_url' => '/posts', 'http_method' => 'get', 'request_format' => 'json', 'response_format' => 'json' } }
      integration factory: :api_integration
    end

    factory :solvace_analysis_create_action, class: 'Integration::Solvace::Analysis::CreateAction' do
      type { 'Integration::Solvace::Analysis::CreateAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_analysis_update_action, class: 'Integration::Solvace::Analysis::UpdateAction' do
      type { 'Integration::Solvace::Analysis::UpdateAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_analysis_destroy_action, class: 'Integration::Solvace::Analysis::DestroyAction' do
      type { 'Integration::Solvace::Analysis::DestroyAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_issue_create_action, class: 'Integration::Solvace::Issue::CreateAction' do
      type { 'Integration::Solvace::Issue::CreateAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_issue_update_action, class: 'Integration::Solvace::Issue::UpdateAction' do
      type { 'Integration::Solvace::Issue::UpdateAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_issue_destroy_action, class: 'Integration::Solvace::Issue::DestroyAction' do
      type { 'Integration::Solvace::Issue::DestroyAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_action_create_action, class: 'Integration::Solvace::Action::CreateAction' do
      type { 'Integration::Solvace::Action::CreateAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_action_update_action, class: 'Integration::Solvace::Action::UpdateAction' do
      type { 'Integration::Solvace::Action::UpdateAction' }
      integration factory: :solvace_integration
    end

    factory :solvace_action_destroy_action, class: 'Integration::Solvace::Action::DestroyAction' do
      type { 'Integration::Solvace::Action::DestroyAction' }
      integration factory: :solvace_integration
    end
  end

  factory :integration_action_param, class: 'Integration::ActionParam' do
    param_type { 'response_param' }
    integration_action
  end

  factory :foreign_object_mapping, class: 'Integration::ForeignObject::Mapping' do
    model_type { 'Custom::Object' }
    foreign_object_type { Faker::Lorem.word }
    enabled_incoming_events { %w[create update destroy] }
    enabled_outgoing_events { %w[create update destroy] }
    integration
    object_type
  end

  factory :foreign_object_attribute_mapping, class: 'Integration::ForeignObject::AttributeMapping' do
    after(:build) do |attribute_mapping|
      if attribute_mapping.foreign_object_mapping.blank?
        mapping = if attribute_mapping.object_type_attribute.blank?
                    create(:foreign_object_mapping)
                  else
                    create(:foreign_object_mapping, object_type: attribute_mapping.object_type_attribute.object_type)
                  end
        attribute_mapping.foreign_object_mapping = mapping
      end
      if attribute_mapping.value.blank? &&
         attribute_mapping.property.blank? &&
         attribute_mapping.object_type_attribute_id.blank?
        attribute = create(:object_type_attribute, object_type: attribute_mapping.foreign_object_mapping.object_type)
        attribute_mapping.object_type_attribute = attribute
      end
    end
  end

  factory :foreign_object_relationship, class: 'Integration::ForeignObject::Relationship' do
    sequence(:foreign_object_id)
    model_type { 'Custom::Object' }

    after(:build) do |relationship|
      if relationship.foreign_object_mapping.blank?
        mapping = if relationship.model_type.blank?
                    create(:foreign_object_mapping)
                  elsif relationship.model_type == 'Object'
                    create(:foreign_object_mapping, object_type: relationship.model.object_type)
                  else
                    create(:foreign_object_mapping, model_type: relationship.model_type)
                  end
        relationship.foreign_object_mapping = mapping
      end
      if relationship.model_id.blank?
        model = if relationship.foreign_object_mapping.custom_object_model?
                  create(:object, object_type: relationship.foreign_object_mapping.object_type)
                else
                  create(relationship.foreign_object_mapping.model_type.demodulize.underscore.to_sym)
                end
        relationship.model = model
      end
    end
  end

  factory :view do
    name { 'My view' }
    object_type
  end

  factory :view_object_type_relationship do
    view factory: :view, object_type: nil
    object_type
  end

  factory :shortcut do
    name { 'My shortcut' }
    user
    position { 0 }
    after(:build) do |shortcut|
      shortcut.dependency = create(:view) if shortcut.dependency.blank?
      if shortcut.target_info.blank?
        shortcut.target_info = { id: shortcut.dependency.object_type.id.to_s, target_type: 'objects',
                                 params: { view_id: shortcut.dependency.id.to_s } }
      end
    end
  end

  factory :object_type_attribute_selectable_value, class: 'Custom::ObjectTypeAttributeSelectableValue' do
    text { 'In progress' }
    value { 'in progress' }
    object_type_attribute
    position { 0 }
  end

  factory :snapshot do
    event { :create }
    user
    item factory: :object
  end

  factory :comment do
    user
    commentable factory: :object
    text { 'New comment' }
  end

  factory :object_type_description do
    object_type
    text { 'Hello' }
    section { 'user' }
  end

  factory :automation_mail_action_address_attribute do
    object_type_attribute { column_name ? nil : association(:object_type_attribute) }
    automation_mail_action
  end

  factory :assistant_message, class: 'Assistant::Message' do
    chat factory: :assistant_chat
    origin { :user }
  end

  factory :assistant_chat, class: 'Assistant::Chat' do
    user
  end
end
