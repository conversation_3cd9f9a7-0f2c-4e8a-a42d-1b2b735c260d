require 'rails_helper'

RSpec.describe Efficy::Opportunity do
  describe '#early_arrival' do
    it 'returns nil if date is in the wrong millenium' do
      opportunity = build_opportunity(
        {
          details: {
            F_EARLYCHECKINTIME: '1899-12-30'
          }
        }
      )
      expect(opportunity.early_arrival).to be_nil
    end

    it 'returns nil if there are no opportunity details' do
      opportunity = build_opportunity({})
      expect(opportunity.early_arrival).to be_nil
    end

    it 'returns a iso8601 datetime converted from a Europe/Brussels datetime if the date is valid' do
      opportunity = build_opportunity(
        {
          details: {
            F_EARLYCHECKINTIME: '2023-07-05T11:13:00.000Z' # This a UTC timestamp but represents a Europe/Brussels timestamp
          }
        }
      )
      expect(opportunity.early_arrival).to eq '2023-07-05T09:13:00Z'
    end
  end

  private

  def build_opportunity(attributes)
    Efficy::Opportunity.new(attributes)
  end
end
