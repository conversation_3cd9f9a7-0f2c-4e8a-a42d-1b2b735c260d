require 'rails_helper'

RSpec.describe Efficy::Product do
  describe '.condense_products!' do
    it 'filters out fake products' do
      products = [
        {
          'K_PRODUCT' => 71,
          'NAME' => 'Apéritif Prosecco',
          'NAME_1' => 'Product *',
          'KIND_1' => '0',
          K_SORT: 1
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 2,
          'F_DESCRIPTION' => 'DATE 13/07 RÉCEPTION - 17:00 - 19:00',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 119,
          'NAME' => 'Dortoirs 6 lits',
          'NAME_1' => 'Product *',
          'KIND_1' => '0',
          K_SORT: 3
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 4,
          'F_DESCRIPTION' => '- <PERSON><PERSON> Hôtelière - Pdj Inclus',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 5,
          'F_DESCRIPTION' => '- Minimum 14 Chambres Réservées',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 6,
          'F_DESCRIPTION' => '- 1 Nuit - 14 Chambres',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => 'DATES 13-14/07/24',
          'K_SORT' => 7,
          'F_DESCRIPTION' => '',
          'NAME_1' => 'Section title *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 8,
          'F_DESCRIPTION' => 'Section comment',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 1890,
          'NAME' => 'Assortiment de Tapas - Knokke Out',
          'COMMENT' => '',
          'K_SORT' => 9,
          'NAME_1' => 'Product *'
        },
        {
          'K_PRODUCT' => 1890,
          'NAME' => 'Option - Bières Spéciales',
          'COMMENT' => '',
          'K_SORT' => 10,
          'NAME_1' => 'Product *'
        }
      ].map(&method(:build_product))

      products = described_class.condense_products!(products)

      expect(products.count).to eq 4
      expect(products.map(&:name)).to contain_exactly('Apéritif Prosecco', 'Dortoirs 6 lits', 'Assortiment de Tapas - Knokke Out',
                                                      'Option - Bières Spéciales')
    end

    it 'merges comments products with the correct product' do
      products = [
        {
          'K_PRODUCT' => 71,
          'NAME' => 'Apéritif Prosecco',
          'NAME_1' => 'Product *',
          'KIND_1' => '0',
          K_SORT: 1
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 2,
          'F_DESCRIPTION' => 'DATE 13/07 RÉCEPTION - 17:00 - 19:00',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 119,
          'NAME' => 'Dortoirs 6 lits',
          'NAME_1' => 'Product *',
          'KIND_1' => '0',
          K_SORT: 3
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 4,
          'F_DESCRIPTION' => '- Formule Hôtelière - Pdj Inclus',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 5,
          'F_DESCRIPTION' => '- Minimum 14 Chambres Réservées',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 6,
          'F_DESCRIPTION' => '- 1 Nuit - 14 Chambres',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => 'DATES 13-14/07/24',
          'K_SORT' => 7,
          'F_DESCRIPTION' => '',
          'NAME_1' => 'Section title *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 8,
          'F_DESCRIPTION' => 'Section comment',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 1890,
          'NAME' => 'Assortiment de Tapas - Knokke Out',
          'COMMENT' => '',
          'K_SORT' => 9,
          'NAME_1' => 'Product *'
        },
        {
          'K_PRODUCT' => 1890,
          'NAME' => 'Option - Bières Spéciales',
          'COMMENT' => '',
          'K_SORT' => 10,
          'NAME_1' => 'Product *'
        }
      ].map(&method(:build_product))

      products = described_class.condense_products!(products)

      product = products.find { |p| p.name == 'Apéritif Prosecco' }
      expect(product.description).to eq 'DATE 13/07 RÉCEPTION - 17:00 - 19:00'

      product = products.find { |p| p.name == 'Dortoirs 6 lits' }
      expect(product.description).to eq "- Formule Hôtelière - Pdj Inclus\n- Minimum 14 Chambres Réservées\n- 1 Nuit - 14 Chambres"

      product = products.find { |p| p.name == 'Assortiment de Tapas - Knokke Out' }
      expect(product.description).to be_nil

      product = products.find { |p| p.name == 'Option - Bières Spéciales' }
      expect(product.description).to be_nil
    end

    it 'merges comments correctly even when the products are in the incorrect order' do
      products = [
        {
          'K_PRODUCT' => 1890,
          'NAME' => 'Assortiment de Tapas - Knokke Out',
          'COMMENT' => '',
          'K_SORT' => 9,
          'NAME_1' => 'Product *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 2,
          'F_DESCRIPTION' => 'DATE 13/07 RÉCEPTION - 17:00 - 19:00',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 4,
          'F_DESCRIPTION' => '- Formule Hôtelière - Pdj Inclus',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 6,
          'F_DESCRIPTION' => '- 1 Nuit - 14 Chambres',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 119,
          'NAME' => 'Dortoirs 6 lits',
          'NAME_1' => 'Product *',
          'KIND_1' => '0',
          K_SORT: 3
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 5,
          'F_DESCRIPTION' => '- Minimum 14 Chambres Réservées',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => 'DATES 13-14/07/24',
          'K_SORT' => 7,
          'F_DESCRIPTION' => '',
          'NAME_1' => 'Section title *'
        },
        {
          'K_PRODUCT' => 99999001,
          'COMMENT' => '',
          'K_SORT' => 8,
          'F_DESCRIPTION' => 'Section comment',
          'NAME_1' => 'Comment *'
        },
        {
          'K_PRODUCT' => 1890,
          'NAME' => 'Option - Bières Spéciales',
          'COMMENT' => '',
          'K_SORT' => 10,
          'NAME_1' => 'Product *'
        },
        {
          'K_PRODUCT' => 71,
          'NAME' => 'Apéritif Prosecco',
          'NAME_1' => 'Product *',
          'KIND_1' => '0',
          K_SORT: 1
        }
      ].map(&method(:build_product))

      products = described_class.condense_products!(products)

      product = products.find { |p| p.name == 'Apéritif Prosecco' }
      expect(product.description).to eq 'DATE 13/07 RÉCEPTION - 17:00 - 19:00'

      product = products.find { |p| p.name == 'Dortoirs 6 lits' }
      expect(product.description).to eq "- Formule Hôtelière - Pdj Inclus\n- Minimum 14 Chambres Réservées\n- 1 Nuit - 14 Chambres"

      product = products.find { |p| p.name == 'Assortiment de Tapas - Knokke Out' }
      expect(product.description).to be_nil

      product = products.find { |p| p.name == 'Option - Bières Spéciales' }
      expect(product.description).to be_nil
    end
  end

  describe '#start_date' do
    it 'returns nil if date is in the wrong millenium' do
      product = build_product(
        {
          'K_PRODUCT' => 71,
          'NAME' => 'Apéritif Prosecco',
          'F_D_START' => '1899-12-30'
        }
      )
      expect(product.start_date).to be_nil
    end

    it 'returns a iso8601 datetime converted from a Europe/Brussels datetime if the date is valid' do
      product = build_product(
        {
          'K_PRODUCT' => 71,
          'NAME' => 'Apéritif Prosecco',
          'F_D_START' => '2023-07-05T11:13:00.000Z' # This a UTC timestamp but represents a Europe/Brussels timestamp
        }
      )
      expect(product.start_date).to eq '2023-07-05T09:13:00Z'
    end
  end

  private

  def build_product(attributes)
    Efficy::Product.new(attributes)
  end
end
