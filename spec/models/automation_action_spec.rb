# == Schema Information
#
# Table name: automation_actions
#
#  id                     :bigint           not null, primary key
#  action_type            :integer          not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  automation_id          :bigint
#  object_type_variant_id :bigint
#  team_id                :bigint
#
require 'rails_helper'

RSpec.describe AutomationAction do
  subject { build(:automation_action) }

  it { is_expected.to belong_to(:automation).required }
  it { is_expected.to belong_to(:team).optional }

  it { is_expected.to have_one(:automation_mail_action).dependent(:destroy) }
  it { is_expected.to have_one(:automation_static_data_action).dependent(:destroy) }
  it { is_expected.to have_one(:automation_integration_action).dependent(:destroy) }
  it { is_expected.to have_one(:automation_ai_action).dependent(:destroy) }
  it { is_expected.to have_one(:automation_update_object_action).dependent(:destroy) }
  it { is_expected.to have_one(:automation_webhook_action).dependent(:destroy) }
  it { is_expected.to belong_to(:object_type_variant).optional }

  describe '#mail_action?' do
    it 'returns false when action_type is not mail' do
      automation_action = build(:automation_action, action_type: nil)
      expect(automation_action.mail_action?).to be false
    end

    it 'returns false when action_type is mail but without mail action details' do
      automation_action = build(:automation_action, action_type: :mail)
      expect(automation_action.mail_action?).to be false
    end

    it 'returns true when action_type is mail and has mail action details' do
      automation_action = build(:automation_action, action_type: :mail)
      build(:automation_mail_action, automation_action: automation_action)
      expect(automation_action.mail_action?).to be true
    end
  end

  describe '#static_data_action?' do
    it 'returns false when action_type is not static_data' do
      automation_action = build(:automation_action, action_type: nil)
      expect(automation_action.static_data_action?).to be false
    end

    it 'returns false when action_type is static_data but without static_data action details' do
      automation_action = build(:automation_action, action_type: :static_data)
      expect(automation_action.static_data_action?).to be false
    end

    it 'returns true when action_type is static_data and has static_data action details' do
      automation_action = build(:automation_action, action_type: :static_data)
      build(:automation_static_data_action, automation_action: automation_action)
      expect(automation_action.static_data_action?).to be true
    end
  end

  describe '#integration_action?' do
    it 'returns false when action_type is not integration' do
      automation_action = build(:automation_action, action_type: nil)
      expect(automation_action.integration_action?).to be false
    end

    it 'returns false when action_type is integration but without integration_action action details' do
      automation_action = build(:automation_action, action_type: :integration)
      expect(automation_action.integration_action?).to be false
    end

    it 'returns true when action_type is integration and has integration_action action details' do
      automation_action = build(:automation_action, action_type: :integration)
      build(:automation_integration_action, automation_action: automation_action)
      expect(automation_action.integration_action?).to be true
    end
  end

  describe '#automation_ai_action?' do
    it 'returns false when action_type is not ai' do
      automation_action = build(:automation_action, action_type: nil)
      expect(automation_action.ai_action?).to be false
    end

    it 'returns false when action_type is ai but without ai_action action details' do
      automation_action = build(:automation_action, action_type: :ai)
      expect(automation_action.ai_action?).to be false
    end

    it 'returns true when action_type is ai and has ai_action action details' do
      automation_action = build(:automation_action, action_type: :ai)
      build(:automation_ai_action, automation_action: automation_action)
      expect(automation_action.ai_action?).to be true
    end
  end

  describe '#automation_update_object_action?' do
    it 'returns false when action_type is not update_object' do
      automation_action = build(:automation_action, action_type: nil)
      expect(automation_action.update_object_action?).to be false
    end

    it 'returns false when action_type is update_object but without update_object action details' do
      automation_action = build(:automation_action, action_type: :update_object)
      expect(automation_action.update_object_action?).to be false
    end

    it 'returns true when action_type is update_object and has update_object action details' do
      automation_action = build(:automation_action, action_type: :update_object)
      build(:automation_update_object_action, automation_action: automation_action)
      expect(automation_action.update_object_action?).to be true
    end
  end

  describe '#automation_webhook_action?' do
    it 'returns false when action_type is not webhook' do
      automation_action = build(:automation_action, action_type: nil)
      expect(automation_action.webhook_action?).to be false
    end

    it 'returns false when action_type is webhook but without webhook action details' do
      automation_action = build(:automation_action, action_type: :webhook)
      expect(automation_action.webhook_action?).to be false
    end

    it 'returns true when action_type is webhook and has webhook action details' do
      automation_action = build(:automation_action, action_type: :webhook)
      build(:automation_webhook_action, automation_action: automation_action)
      expect(automation_action.webhook_action?).to be true
    end
  end
end
