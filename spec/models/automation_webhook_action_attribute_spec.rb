# == Schema Information
#
# Table name: automation_webhook_action_attributes
#
#  id                           :bigint           not null, primary key
#  column_name                  :string(255)
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  automation_webhook_action_id :bigint
#  object_type_attribute_id     :bigint
#
require 'rails_helper'

RSpec.describe AutomationWebhookActionAttribute do
  it { is_expected.to belong_to(:automation_webhook_action).required }
  it { is_expected.to belong_to(:object_type_attribute).optional }

  describe '#valid?' do
    describe 'column_xor_attribute' do
      it 'is valid when only an object_type_attribute is specified' do
        attribute = build(:automation_webhook_action_attribute, object_type_attribute: create(:object_type_attribute),
                                                                column_name: nil)
        expect(attribute.valid?).to be true
      end

      it 'is valid when only a column_name is specified' do
        attribute = build(:automation_webhook_action_attribute, object_type_attribute: nil, column_name: 'id')
        expect(attribute.valid?).to be true
      end

      it 'adds an error if no column or object type attribute is specified' do
        attribute = build(:automation_webhook_action_attribute, object_type_attribute: nil, column_name: nil)
        attribute.valid?

        expect(attribute.errors[:base]).not_to be_blank
      end

      it 'adds an error if both column and object type attribute are specified' do
        attribute = build(:automation_webhook_action_attribute, object_type_attribute: create(:object_type_attribute),
                                                                column_name: 'id')
        attribute.valid?

        expect(attribute.errors[:base]).not_to be_blank
      end
    end
  end
end
