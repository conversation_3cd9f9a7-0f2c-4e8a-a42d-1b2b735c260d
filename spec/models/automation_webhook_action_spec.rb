# == Schema Information
#
# Table name: automation_webhook_actions
#
#  id                   :bigint           not null, primary key
#  headers              :json
#  url                  :string(255)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  automation_action_id :bigint
#
require 'rails_helper'

RSpec.describe AutomationWebhookAction do
  it { is_expected.to belong_to(:automation_action).required }
  it { is_expected.to have_many(:automation_webhook_action_attributes).dependent(:destroy) }

  it { is_expected.to allow_value('https://example.com').for(:url) }
  it { is_expected.not_to allow_value('http://example.com').for(:url) }
  it { is_expected.not_to allow_value('not a even a link').for(:url) }
end
