# == Schema Information
#
# Table name: object_type_attributes
#
#  id                              :bigint           not null, primary key
#  allow_variant_options           :boolean          default(FALSE), not null
#  allowed_values                  :json
#  archived                        :boolean          default(FALSE), not null
#  calculated                      :boolean          default(FALSE), not null
#  can_update                      :boolean          default(TRUE), not null
#  data_type                       :string(255)
#  default_value                   :json
#  field_identifier                :string(255)
#  hidden                          :json
#  inverse                         :boolean          default(FALSE), not null
#  inverse_relationship_kind       :string(255)
#  key                             :string(255)
#  locked                          :boolean          default(TRUE), not null
#  relationship_kind               :string(255)
#  searchable_for_recommendations  :boolean          default(FALSE), not null
#  show_colored_options            :boolean
#  target_class                    :string(255)
#  target_column_name              :string(255)
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  object_type_id                  :bigint
#  object_type_variant_id          :bigint
#  target_object_type_attribute_id :bigint
#  target_object_type_id           :bigint
#  target_team_id                  :bigint
#
require 'rails_helper'
require 'models/concerns/shared_examples_variantable_concern'

RSpec.describe Custom::ObjectTypeAttribute do
  subject(:object_type_attribute) { build(:object_type_attribute) }

  it { is_expected.to belong_to(:object_type).required }
  it { is_expected.to belong_to(:object_type_variant).optional }
  it { is_expected.to belong_to(:target_object_type).class_name('Custom::ObjectType').optional }
  it { is_expected.to belong_to(:target_team).class_name('Team').optional }

  it { is_expected.to have_many(:object_type_roles).dependent(:nullify) }
  it { is_expected.to have_many(:object_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:object_relationships).dependent(:destroy) }
  it { is_expected.to have_many(:object_attachments).dependent(:destroy) }
  it { is_expected.to have_many(:form_elements).dependent(:destroy) }
  it { is_expected.to have_many(:object_query_rules).dependent(:destroy) }
  it { is_expected.to have_many(:object_query_rules_depth1).dependent(:destroy) }
  it { is_expected.to have_many(:object_query_rules_depth2).dependent(:destroy) }
  it { is_expected.to have_many(:condition_rules).dependent(:destroy) }
  it { is_expected.to have_many(:automation_mail_action_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:automation_mail_action_address_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:automation_mail_actions).dependent(:destroy) }

  it {
    expect(object_type_attribute).to have_many(:automation_mail_action_relationship_address_attributes)
      .class_name('AutomationMailActionAddressAttribute').dependent(:destroy)
  }

  it { is_expected.to have_many(:automation_static_data_actions).dependent(:nullify) }
  it { is_expected.to have_many(:automation_trigger_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:automation_ai_action_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:automation_update_object_action_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:automation_webhook_action_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:visualisation_thumbnails).class_name('Thumbnail').dependent(:destroy) }
  it { is_expected.to have_many(:object_type_relationships).dependent(:destroy) }
  it { is_expected.to have_many(:inverse_object_type_attributes) }
  it { is_expected.to have_many(:inverse_object_type_relationships).dependent(:destroy) }
  it { is_expected.to have_many(:linked_object_type_attributes) }
  it { is_expected.to have_many(:object_type_attribute_validations).dependent(:destroy) }
  it { is_expected.to have_many(:other_object_type_attribute_validations).dependent(:destroy) }
  it { is_expected.to have_many(:thumbnail_attributes).dependent(:destroy) }
  it { is_expected.to have_many(:background_thumbnails).dependent(:nullify) }
  it { is_expected.to have_many(:interface_element_record_pickers).class_name('Interface::Element::RecordPicker').dependent(:nullify) }
  it { is_expected.to have_many(:interface_element_fields).class_name('Interface::Element::Field').dependent(:nullify) }

  it { is_expected.to have_one(:calculation).dependent(:destroy) }
  it { is_expected.to have_many(:data_object_type_attribute_paths).dependent(:destroy) }
  it { is_expected.to have_many(:filter_attribute_views).class_name('View').dependent(:nullify) }

  it {
    expect(object_type_attribute).to have_many(:automation_relationship_ai_action_attributes)
      .class_name('AutomationAiActionAttribute')
      .dependent(:destroy)
  }

  it {
    expect(object_type_attribute).to have_many(:foreign_object_attribute_mappings)
      .class_name('Integration::ForeignObject::AttributeMapping')
      .dependent(:destroy)
  }

  it { is_expected.to have_one(:object_type_owner) }
  it { is_expected.to have_one(:inverse_title_object_type_attribute) }

  it { is_expected.to validate_length_of(:name).is_at_most(255) }
  it { is_expected.to validate_presence_of(:key) }
  it { is_expected.to validate_length_of(:key).is_at_most(255) }
  it { is_expected.to validate_uniqueness_of(:key).case_insensitive.scoped_to(:object_type_id) }
  it { is_expected.to validate_presence_of(:data_type) }

  it 'validates inclusion of data_type' do
    allowed_data_types = %w[String Boolean Date DateTime Number HTML Relationship Attachment]
    expect(object_type_attribute).to validate_inclusion_of(:data_type).in_array(allowed_data_types)
  end

  it_behaves_like 'Variantable'

  describe '#valid?' do
    it_behaves_like 'hideable in variant'

    context 'when data_type is relationship' do
      subject(:object_type_attribute) { build(:object_type_attribute, data_type: 'Relationship', target_class: target_class) }

      let(:target_class) { 'User' }

      it { is_expected.to validate_presence_of(:relationship_kind) }
      it { is_expected.to validate_inclusion_of(:relationship_kind).in_array(%w[single multiple]) }
      it { is_expected.to validate_presence_of(:inverse_relationship_kind) }
      it { is_expected.to validate_inclusion_of(:inverse_relationship_kind).in_array(%w[single multiple]) }

      context 'when target_class is not Object' do
        it { is_expected.to validate_absence_of(:target_object_type_attribute) }
      end

      context 'when target_class is Object' do
        let(:target_class) { 'Object' }
        let(:target_object_type_attribute) { create(:object_type_attribute) }

        it { is_expected.to allow_value(target_object_type_attribute).for(:target_object_type_attribute) }
      end

      describe 'target_column_name_xor_target_object_type_attribute' do
        let(:target_object_type_attribute) { create(:object_type_attribute) }

        it 'adds an error when both target_column_name and target_object_type_attribute are present' do
          attribute = build(:object_type_attribute,
                            data_type: 'Relationship',
                            target_class: 'Object',
                            target_column_name: 'id',
                            target_object_type_attribute: target_object_type_attribute)
          expect(attribute.valid?).to be false
          expect(attribute.errors[:base]).not_to be_blank
        end

        it 'adds an error when both target_column_name and target_object_type_attribute are blank' do
          attribute = build(:object_type_attribute,
                            data_type: 'Relationship',
                            target_class: 'Object')
          expect(attribute.valid?).to be false
          expect(attribute.errors[:base]).not_to be_blank
        end
      end
    end

    context 'when data_type is not relationship' do
      subject(:object_type_attribute) { build(:object_type_attribute, data_type: 'String') }

      it { is_expected.to validate_absence_of(:relationship_kind) }

      describe 'target_column_name_xor_target_object_type_attribute' do
        it 'adds no error when both target_column_name and target_object_type_attribute are blank' do
          attribute = build(:object_type_attribute,
                            data_type: 'Boolean')
          expect(attribute.valid?).to be true
          expect(attribute.errors[:base]).to be_blank
        end
      end
    end

    describe '#field_identifier_uniqueness' do
      it 'cannot create an object_type_attribute without a field_identifier' do
        ota = build(:object_type_attribute, field_identifier: nil)
        expect(ota.valid?).to be false
      end

      it 'can create an object_type_attribute with a unique field_identifier' do
        ot = create(:object_type)
        create(:object_type_attribute, field_identifier: 'unique1', object_type: ot)
        create(:object_type_attribute, field_identifier: 'unique2', object_type: ot)
        create(:object_type_attribute, field_identifier: 'unique3', object_type: ot)

        different_ot = create(:object_type)
        create(:object_type_attribute, field_identifier: 'unique1', object_type: different_ot, archived: true)
        ota = build(:object_type_attribute, field_identifier: 'unique1', object_type: different_ot)
        expect(ota.valid?).to be true
      end

      it 'cannot create an object_type_attribute with a non-unique field_identifier' do
        ot = create(:object_type)
        create(:object_type_attribute, field_identifier: 'unique1', object_type: ot)
        create(:object_type_attribute, field_identifier: 'unique2', object_type: ot)

        ota = build(:object_type_attribute, field_identifier: 'unique1', object_type: ot)
        expect(ota.valid?).to be false
      end

      it 'can create an object_type_attribute with the same field_identifier if the attribute is archived or belongs to another ot' do
        ot = create(:object_type)
        create(:object_type_attribute, field_identifier: 'unique1', object_type: ot, archived: true)
        create(:object_type_attribute, field_identifier: 'unique1')

        ota = build(:object_type_attribute, field_identifier: 'unique1', object_type: ot)
        expect(ota.valid?).to be true
      end
    end
  end

  describe '#destroy' do
    it 'removes referenced object_type_attribute_paths' do
      attribute = create(:object_type_attribute)
      path = create(:object_type_attribute_path, relationship_references: [{ attribute_id: attribute.id, inverse: false }])
      data_path = create(:object_type_attribute_path, data_attribute: attribute)
      unrelated_path = create(:object_type_attribute_path)

      attribute.destroy
      expect { path.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect { data_path.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect { unrelated_path.reload }.not_to raise_error
    end
  end

  describe '.map_relationship_objects' do
    context 'when the object_type_attribute has a target_object_type_attribute' do
      it 'returns the target_object_type_attribute values from the object_relationship model' do
        target_object = create(:object)
        target_attribute = create(:object_type_attribute, object_type: target_object.object_type, data_type: 'String', key: 'name')
        create(:object_attribute, object: target_object, object_type_attribute: target_attribute, value: 'The terminator')
        object = create(:object)
        attribute = create(:object_type_attribute, object_type: object.object_type,
                                                   data_type: 'Relationship',
                                                   relationship_kind: 'single',
                                                   inverse_relationship_kind: 'multiple',
                                                   target_class: 'Object',
                                                   target_object_type_attribute: target_attribute,
                                                   target_object_type_id: target_object.object_type.id)

        create(:object_relationship, object: object, model: target_object, object_type_attribute: attribute)

        relationship_objects = attribute.relationship_objects_for(object)
        expect(attribute.map_relationship_objects(relationship_objects)).to contain_exactly(
          { object_type_id: target_object.object_type_id,
            object_id: target_object.id,
            value: 'The terminator' }
        )
      end
    end

    context 'when the object_type_attribute has a target_column_name' do
      it 'returns the target_column_name from the User model' do
        user = create(:user, name: 'The terminator')
        object = create(:object)
        attribute = create(:object_type_attribute, object_type: object.object_type,
                                                   data_type: 'Relationship',
                                                   relationship_kind: 'single',
                                                   inverse_relationship_kind: 'multiple',
                                                   target_class: 'User',
                                                   target_column_name: 'name')

        create(:object_relationship, object: object, model: user, object_type_attribute: attribute)

        relationship_objects = attribute.relationship_objects_for(object)
        expect(attribute.map_relationship_objects(relationship_objects)).to contain_exactly({ object_type_id: nil,
                                                                                              object_id: user.id,
                                                                                              value: 'The terminator' })
      end

      it 'returns the target_column_name from the Object model' do
        target_object = create(:object)
        object = create(:object)
        attribute = create(:object_type_attribute, object_type: object.object_type,
                                                   data_type: 'Relationship',
                                                   relationship_kind: 'single',
                                                   inverse_relationship_kind: 'multiple',
                                                   target_class: 'Object',
                                                   target_column_name: 'id')

        create(:object_relationship, object: object, model: target_object, object_type_attribute: attribute)

        relationship_objects = attribute.relationship_objects_for(object)
        expect(attribute.map_relationship_objects(relationship_objects)).to contain_exactly(
          {
            object_type_id: target_object.object_type_id,
            object_id: target_object.id,
            value: target_object.id
          }
        )
      end
    end
  end

  describe 'relationship_objects_for' do
    context 'when the object_type_attribute is an inverse relational attribute' do
      it 'returns a list of related objects without duplicates' do
        forward_otas = create_list(:object_type_attribute, 2, :object_relationship, relationship_kind: 'multiple')
        inverse_ota = create(:object_type_attribute, :object_relationship, inverse: true)
        forward_otas.each do |ota|
          create(:object_type_relationship, object_type_attribute: ota, inverse_object_type_attribute: inverse_ota)
        end
        object = create(:object)
        related_object1 = create(:object_relationship, object_type_attribute: forward_otas.first, model: object).object
        related_object2 = create(:object_relationship, object_type_attribute: forward_otas.second, model: object).object
        related_object3 = create(:object_relationship, object_type_attribute: forward_otas.first, model: object).object
        create(:object_relationship, object_type_attribute: forward_otas.first, model: object, object: related_object3)

        object = Custom::Object.includes(model_object_relationships: :object).find(object.id)
        expect(inverse_ota.relationship_objects_for(object)).to contain_exactly(related_object1, related_object2, related_object3)
      end
    end
  end

  describe '#validate_value_type!' do
    it 'delegates to type validator' do
      ota = create(:object_type_attribute, data_type: 'String')
      stubbed_validator = ObjectAttributeTypeValidator.new(ota)
      allow(ObjectAttributeTypeValidator).to receive(:new).and_return(stubbed_validator)
      allow(stubbed_validator).to receive(:validate)

      ota.validate_value_type!('value')

      expect(ObjectAttributeTypeValidator).to have_received(:new).with(ota)
      expect(stubbed_validator).to have_received(:validate).with('value')
    end
  end

  describe '#hidden_for?' do
    it 'returns false if an attribute is locked' do
      variant = create(:object_type_variant)
      ota = create(:object_type_attribute, locked: true, data_type: 'String', object_type: variant.object_type,
                                           hidden: { variant.id => true })

      expect(ota.hidden_for?(variant)).to be false
    end

    it 'returns false when there is no variant' do
      variant = create(:object_type_variant)
      ota = create(:object_type_attribute, locked: false, data_type: 'String', object_type: variant.object_type,
                                           hidden: { variant.id => true })

      expect(ota.hidden_for?(nil)).to be false
    end

    it 'returns false if an unlocked attribute is not hidden for a variant' do
      variant = create(:object_type_variant)
      ota = create(:object_type_attribute, locked: false, data_type: 'String', object_type: variant.object_type,
                                           hidden: { variant.id => false })

      expect(ota.hidden_for?(variant)).to be false
    end

    it 'returns true if an unlocked attribute is hidden for a variant' do
      variant = create(:object_type_variant)
      ota = create(:object_type_attribute, locked: false, data_type: 'String', object_type: variant.object_type,
                                           hidden: { variant.id => true })

      expect(ota.hidden_for?(variant)).to be true
    end
  end

  describe '#active_calculation?' do
    it 'returns true when the attribute is calculated and not archived' do
      ota = build(:object_type_attribute, calculated: true, archived: false)

      expect(ota.active_calculation?).to be true
    end

    it 'returns false when the attribute is calculated and archived' do
      ota = build(:object_type_attribute, calculated: true, archived: true)

      expect(ota.active_calculation?).to be false
    end

    it 'returns false when the attribute is not calculated' do
      ota = build(:object_type_attribute, calculated: false)

      expect(ota.active_calculation?).to be false
    end
  end

  describe '#with_validations_for?' do
    it 'returns true if an attribute has validations, belongs to the variant and is not hidden' do
      variant = create(:object_type_variant)
      ota = create(:object_type_attribute, locked: true, data_type: 'String', object_type: variant.object_type,
                                           hidden: { variant.id => false })
      create(:object_type_attribute_validation, object_type_attribute: ota)

      expect(ota.with_validations_for?(variant)).to be true
    end

    it 'returns false if an attribute has no validations' do
      ota = create(:object_type_attribute, locked: true, data_type: 'String')
      expect(ota.with_validations_for?(nil)).to be false
    end

    it 'returns false if an attribute is hidden in the variant' do
      variant = create(:object_type_variant)
      ota = create(:object_type_attribute, locked: false, data_type: 'String', object_type: variant.object_type,
                                           hidden: { variant.id => true })
      create(:object_type_attribute_validation, object_type_attribute: ota)

      expect(ota.with_validations_for?(variant)).to be false
    end

    it 'returns false if an attribute is no part of the variant' do
      object_type = create(:object_type)
      variant = create(:object_type_variant, object_type: object_type)
      variant2 = create(:object_type_variant, object_type: object_type)
      ota = create(:object_type_attribute, locked: true, data_type: 'String', object_type: object_type, object_type_variant: variant2)
      create(:object_type_attribute_validation, object_type_attribute: ota)

      expect(ota.with_validations_for?(variant)).to be false
    end
  end

  describe '#default_value_for_variant' do
    it 'clones the default value from the target variant' do
      variant1 = create(:object_type_variant)
      variant2 = create(:object_type_variant)
      object_type_attribute = create(
        :object_type_attribute,
        default_value: [
          { value: 'A', dynamic: false },
          { value: 'B', dynamic: false, object_type_variant_id: variant1.id }
        ]
      )

      expect(object_type_attribute.default_value_for_variant['value']).to eq 'A'
      expect(object_type_attribute.default_value_for_variant(variant1)['value']).to eq 'B'
      expect(object_type_attribute.default_value_for_variant(variant2)['value']).to eq 'A'
    end
  end

  describe '#update_filter_rules_for_relationship_kind_change' do
    let(:object_type) { create(:object_type) }
    let(:object_type_attribute) { create(:object_type_attribute, :user_relationship, relationship_kind: 'single') }

    before do
      create(:object_query_rule,
             object_type_attribute: object_type_attribute,
             rule_type: 'filter',
             operator: 'equals')
      create(:object_query_rule,
             object_type_attribute: object_type_attribute,
             rule_type: 'filter',
             operator: 'not_equals')
    end

    it 'updates filter rules when relationship_kind changes to multiple' do
      object_type_attribute.update!(relationship_kind: 'multiple')

      # Check that operators were updated
      expect(object_type_attribute.object_query_rules.where(operator: 'equals')).to be_empty
      expect(object_type_attribute.object_query_rules.where(operator: 'not_equals')).to be_empty
      expect(object_type_attribute.object_query_rules.where(operator: 'contains').count).to eq(1)
      expect(object_type_attribute.object_query_rules.where(operator: 'not_contains').count).to eq(1)
    end

    it 'does not update filter rules when relationship_kind does not change' do
      object_type_attribute.update!(name: 'New Name')

      # Check that operators were not updated
      expect(object_type_attribute.object_query_rules.where(operator: 'equals').count).to eq(1)
      expect(object_type_attribute.object_query_rules.where(operator: 'not_equals').count).to eq(1)
      expect(object_type_attribute.object_query_rules.where(operator: 'contains')).to be_empty
      expect(object_type_attribute.object_query_rules.where(operator: 'not_contains')).to be_empty
    end
  end
end
