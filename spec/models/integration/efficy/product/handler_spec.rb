require 'rails_helper'

RSpec.describe 'Integration::Efficy::Product::Handler' do
  let(:event_type) { create(:object_type) }

  context 'when a product update event is received' do
    it 'queues a Integration::Efficy::OpportunitySyncWorker for the owning opportunity' do
      team = create(:team, id: 17)
      integration = setup_integration(team: team)
      mapping = integration.mappings_for_efficy_type(:opportunity).first

      efficy_event = build_efficy_event(operation: 'Update', opportunity_id: 11274)

      allow(Integration::Efficy::OpportunitySyncWorker).to receive(:perform_async)
      integration.handle_external_event(efficy_event)

      expected_event = {
        'table' => 22000,
        'key' => '11274',
        'operation' => 'Update',
        'user' => 'HOUSTTEST',
        'subject' => '',
        'fields' => {},
        'date' => '2024-08-26T09:13:07.065Z'
      }
      expect(Integration::Efficy::OpportunitySyncWorker).to have_received(:perform_async).with(
        {
          'opportunity_id' => '11274',
          'event' => expected_event.to_json,
          'mapping_id' => mapping.id
        }
      )
    end

    it 'does nothing if product key has an unexpected format' do
      team = create(:team, id: 17)
      integration = setup_integration(team: team)

      efficy_event = build_efficy_event(operation: 'Update', key: 'abc')

      allow(Integration::Efficy::OpportunitySyncWorker).to receive(:perform_async)
      integration.handle_external_event(efficy_event)

      expect(Integration::Efficy::OpportunitySyncWorker).not_to have_received(:perform_async)
    end

    it 'does nothing if opportunity id has an unexpected format' do
      team = create(:team, id: 17)
      integration = setup_integration(team: team)

      efficy_event = build_efficy_event(operation: 'Update', opportunity_id: 'abc')

      allow(Integration::Efficy::OpportunitySyncWorker).to receive(:perform_async)
      integration.handle_external_event(efficy_event)

      expect(Integration::Efficy::OpportunitySyncWorker).not_to have_received(:perform_async)
    end
  end

  context 'when a opportunity delete event is received' do
    it 'queues a Integration::Efficy::OpportunitySyncWorker for the owning opportunity' do
      team = create(:team, id: 17)
      integration = setup_integration(team: team)
      mapping = integration.mappings_for_efficy_type(:opportunity).first

      efficy_event = build_efficy_event(operation: 'Delete', opportunity_id: 11274)

      allow(Integration::Efficy::OpportunitySyncWorker).to receive(:perform_async)
      integration.handle_external_event(efficy_event)

      expected_event = {
        'table' => 22000,
        'key' => '11274',
        'operation' => 'Update',
        'user' => 'HOUSTTEST',
        'subject' => '',
        'fields' => {},
        'date' => '2024-08-26T09:13:07.065Z'
      }
      expect(Integration::Efficy::OpportunitySyncWorker).to have_received(:perform_async).with(
        {
          'opportunity_id' => '11274',
          'event' => expected_event.to_json,
          'mapping_id' => mapping.id
        }
      )
    end
  end

  private

  def build_efficy_event(operation: 'Update', opportunity_id: '11274', key: nil)
    {
      database: 'kopf',
      version: '12.0.105',
      records: [
        {
          table: 30022,
          key: key || "99999001,#{opportunity_id},510862604",
          operation: operation,
          user: 'HOUSTTEST',
          subject: 'Nil product',
          fields: {
            F_D_START: '29/04/2025 11:04:00'
          },
          date: '2024-08-26T09:13:07.065Z'
        }
      ]
    }
  end

  def setup_integration(team: nil)
    integration = create(
      :efficy_integration,
      options: {
        url: 'https://www.efficy.com/crm/json?ss_id=1',
        team_id: team&.id
      }
    )

    create_mapping_with_efficy_opportunity(integration)

    integration
  end

  def create_mapping_with_efficy_opportunity(integration)
    Integration::ForeignObject::Mapping::CreateService.execute(
      integration: integration,
      foreign_object_type: 'opportunity',
      model_type: 'Custom::Object',
      object_type: event_type,
      enabled_incoming_events: %w[create update delete],
      enabled_outgoing_events: [],
      attribute_mapping: {}
    )
  end
end
