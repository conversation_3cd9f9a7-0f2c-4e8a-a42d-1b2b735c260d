require 'rails_helper'

RSpec.describe 'AuthorizationConcern' do
  let(:url) { current_user_url(host: host, format: :json) }

  context 'when a disabled user tries to access the system' do
    it 'responds with unauthorized and destroys the session' do
      user = create(:user)
      sign_in(user)

      get url
      expect(response).to have_http_status :ok
      expect(body['data']['id']).to eq user.id.to_s

      user.update!(disabled: true)

      get url
      expect(response).to have_http_status :unauthorized
      expect(user.sessions.count).to eq 0
    end
  end
end
