require 'rails_helper'

RSpec.describe 'PagesController' do
  describe 'when not logged in' do
    it 'returns 200 on pages#signin' do
      get signin_page_url(host: host)
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'when logged in' do
    let(:user) { create(:user) }

    before do
      sign_in user
    end

    it 'returns 302 on pages#signin' do
      get signin_page_url(host: host)
      expect(response).to have_http_status(:found)
    end

    it 'returns 200 on pages#signin when user is disabled and destroys the session' do
      user.update(disabled: true)
      get signin_page_url(host: host)
      expect(response).to have_http_status(:ok)
      expect(user.sessions.count).to eq 0
    end
  end

  describe 'when logged in with invalid session token' do
    before do
      user = create(:user)
      sign_in user
      user.sessions.destroy_all
    end

    it 'returns 200 on pages#signin' do
      get signin_page_url(host: host)
      expect(response).to have_http_status(:ok)
    end
  end
end
