require 'rails_helper'

RSpec.describe AssistantService do
  describe '#execute' do
    it 'executes the action' do
      question = 'What is the capital of France?'
      data = 'Paris'
      chat_prompt = ChatPrompt.new(question, data)
      llm = instance_double(Langchain::LLM::OpenAI)
      response1_body = { 'id' => 'chatcmpl-9JgzYR8RWzKlRFONNYhh0bPXtw9Mz',
                         'object' => 'chat.completion',
                         'created' => 1714480520,
                         'model' => 'gpt-4o-mini',
                         'choices' => [{ 'index' => 0,
                                         'message' => { 'role' => 'assistant', 'content' => nil,
                                                        'tool_calls' => [
                                                          { 'id' => 'call_W6GfhhzcdLuLhq0ixxjNZPFq', 'type' => 'function',
                                                            'function' => { 'name' => 'get_record', 'arguments' => '{}' } }
                                                        ] }, 'logprobs' => nil, 'finish_reason' => 'stop' }],
                         'usage' => {
                           'prompt_tokens' => 105, 'completion_tokens' => 1, 'total_tokens' => 106
                         },
                         'system_fingerprint' => 'fp_3b956da36b' }
      response1 = Langchain::LLM::OpenAIResponse.new(response1_body)
      response2_body = { 'id' => 'chatcmpl-9JgzZloTLpE5jTDV2BZKSJ3UwksaG',
                         'object' => 'chat.completion',
                         'created' => 1714480521,
                         'model' => 'gpt-4o-mini',
                         'usage' => { 'prompt_tokens' => 278, 'completion_tokens' => 45,
                                      'total_tokens' => 323 },
                         'system_fingerprint' => 'fp_3b956da36b',
                         'choices' => [{ 'index' => 0, 'message' => { 'role' => 'assistant', 'content' => 'Paris' } }] }
      response2 = Langchain::LLM::OpenAIResponse.new(response2_body)
      allow(llm).to receive(:chat).with(hash_including(tool_choice: { type: 'function',
                                                                      function: { name: 'get_record' } })).and_return(response1)
      allow(llm).to receive(:chat).with(
        hash_including(messages: array_including(hash_including(role: 'user', content: question)))
      ).and_return(response2)
      allow(Langchain::LLM::OpenAI).to receive(:new).with(
        hash_including(
          api_key: anything,
          default_options: { chat_model: 'gpt-4o-mini', temperature: 0.0 }
        )
      ).and_return(llm)
      data = described_class.execute(chat_prompt, 'abc123')

      expect(data).to eq('Paris')

      expect(llm).to have_received(:chat).with(
        hash_including(tool_choice: { type: 'function', function: { name: 'get_record' } })
      ).ordered
      expect(llm).to have_received(:chat).with(
        hash_including(messages: array_including(hash_including(role: 'user', content: question)))
      ).ordered.twice # We don't know why it's called twice

      expect(Langchain::LLM::OpenAI).to have_received(:new).with(
        hash_including(
          api_key: anything,
          default_options: { chat_model: 'gpt-4o-mini', temperature: 0.0 }
        )
      ).once
    end
  end
end
