require 'rails_helper'

RSpec.describe Automation::HandlerService do
  describe '#execute' do
    let(:automation) { create(:automation) }

    it 'only executes automation actions with a blank or matching object team id' do
      object = create(:object, team: create(:team))
      good_automation_action = create(:automation_action,
                                      automation: automation,
                                      team: object.team,
                                      action_type: :mail,
                                      automation_mail_action: create(:automation_mail_action))
      neutral_automation_action = create(:automation_action,
                                         automation: automation,
                                         action_type: :mail,
                                         automation_mail_action: create(:automation_mail_action))
      evil_automation_action = create(:automation_action,
                                      automation: automation,
                                      team: create(:team),
                                      action_type: :mail,
                                      automation_mail_action: create(:automation_mail_action))

      allow(AutomationMailActionWorker).to receive(:schedule_for)

      field_set = FieldSet::Object.new(object)
      described_class.execute(automation, field_set: field_set)
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        good_automation_action.automation_mail_action,
        object,
        anything,
        nil
      )
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        neutral_automation_action.automation_mail_action,
        object,
        anything,
        nil
      )
      expect(AutomationMailActionWorker).not_to have_received(:schedule_for).with(
        evil_automation_action.automation_mail_action,
        object,
        anything,
        nil
      )
    end

    it 'only executes automation actions with a blank or matching payload team id' do
      team = create(:team)
      good_automation_action = create(:automation_action,
                                      automation: automation,
                                      team: team,
                                      action_type: :static_data,
                                      automation_static_data_action: create(:automation_static_data_action))
      neutral_automation_action = create(:automation_action,
                                         automation: automation,
                                         action_type: :static_data,
                                         automation_static_data_action: create(:automation_static_data_action))
      evil_automation_action = create(:automation_action,
                                      automation: automation,
                                      team: create(:team),
                                      action_type: :static_data,
                                      automation_static_data_action: create(:automation_static_data_action))

      payload = { data: { relationships: { team: { data: { id: team.id } } } } }
      field_set = FieldSet::JsonPayload.new(payload, object_type: create(:object_type))

      allow(AutomationStaticDataAction::HandlerService).to receive(:execute)

      described_class.execute(automation, field_set: field_set)
      expect(AutomationStaticDataAction::HandlerService).to have_received(:execute).with(
        good_automation_action.automation_static_data_action,
        field_set: field_set
      )
      expect(AutomationStaticDataAction::HandlerService).to have_received(:execute).with(
        neutral_automation_action.automation_static_data_action,
        field_set: field_set
      )
      expect(AutomationStaticDataAction::HandlerService).not_to have_received(:execute).with(
        evil_automation_action.automation_static_data_action,
        field_set: field_set
      )
    end

    it 'only executes automation actions part of the object type variant' do
      good_variant = create(:object_type_variant)
      bad_variant = create(:object_type_variant, object_type: good_variant.object_type)
      object = create(:object, team: good_variant.team, object_type: good_variant.object_type)

      good_automation_action = create(:automation_action,
                                      automation: automation,
                                      action_type: :mail,
                                      object_type_variant: good_variant,
                                      automation_mail_action: create(:automation_mail_action))
      neutral_automation_action = create(:automation_action,
                                         automation: automation,
                                         action_type: :mail,
                                         automation_mail_action: create(:automation_mail_action))
      evil_automation_action = create(:automation_action,
                                      automation: automation,
                                      action_type: :mail,
                                      object_type_variant: bad_variant,
                                      automation_mail_action: create(:automation_mail_action))

      allow(AutomationMailActionWorker).to receive(:schedule_for)

      field_set = FieldSet::Object.new(object)
      described_class.execute(automation, field_set: field_set)
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        good_automation_action.automation_mail_action,
        object,
        anything,
        nil
      )
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        neutral_automation_action.automation_mail_action,
        object,
        anything,
        nil
      )
      expect(AutomationMailActionWorker).not_to have_received(:schedule_for).with(
        evil_automation_action.automation_mail_action,
        object,
        anything,
        nil
      )
    end

    it 'executes automation actions in order of creation' do
      object = create(:object, team: create(:team))
      first_automation_action = create(:automation_action,
                                       automation: automation,
                                       team: object.team,
                                       action_type: :mail,
                                       automation_mail_action: create(:automation_mail_action),
                                       created_at: 3.days.ago)
      third_automation_action = create(:automation_action,
                                       automation: automation,
                                       team: object.team,
                                       action_type: :mail,
                                       automation_mail_action: create(:automation_mail_action),
                                       created_at: 1.day.ago)
      second_automation_action = create(:automation_action,
                                        automation: automation,
                                        team: object.team,
                                        action_type: :mail,
                                        automation_mail_action: create(:automation_mail_action),
                                        created_at: 2.days.ago)

      allow(AutomationMailActionWorker).to receive(:schedule_for)

      field_set = FieldSet::Object.new(object)
      described_class.execute(automation, field_set: field_set)
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        first_automation_action.automation_mail_action,
        object,
        anything,
        nil
      ).ordered
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        second_automation_action.automation_mail_action,
        object,
        anything,
        nil
      ).ordered
      expect(AutomationMailActionWorker).to have_received(:schedule_for).with(
        third_automation_action.automation_mail_action,
        object,
        anything,
        nil
      ).ordered
    end

    it 'broadcasts an update event if the object changed' do
      automation_action = create(:automation_action, automation: automation, action_type: :ai)
      automation_ai_action = create(:automation_ai_action, automation_action: automation_action)

      object = create(:object)
      field_set = FieldSet::Object.new(object)

      allow(AutomationAiAction::HandlerService).to receive(:execute).and_return(
        AutomationActionResult.new(
          change_set: Custom::Object::ChangeSet.new
        )
      )
      allow(ObjectsChannel).to receive(:broadcast_for_object)

      described_class.execute(automation, field_set: field_set)
      expect(AutomationAiAction::HandlerService).to have_received(:execute).with(
        automation_ai_action, field_set, nil, nil
      )
      expect(ObjectsChannel).to have_received(:broadcast_for_object) do |instance, broadcast_object, event, message_id|
        expect(instance).to eq(Instance.current)
        expect(broadcast_object.id).to eq(object.id)
        expect(event).to eq('update')
        expect(message_id).to be_nil
      end
    end

    it 'merges the field set results of the executed automation actions' do
      book = create(:object_type)
      title = create(:object_type_attribute, object_type: book, key: 'title', data_type: 'String')
      genre = create(:object_type_attribute, object_type: book, key: 'genre', data_type: 'String')
      description = create(:object_type_attribute, object_type: book, key: 'description', data_type: 'String')
      author = create(:object_type_attribute, object_type: book, key: 'author', data_type: 'String')

      automation.update(object_type: book)

      action1 = create(:automation_action, automation: automation, action_type: :static_data)
      create(
        :automation_static_data_action,
        object_type_attribute: title,
        automation_action: action1,
        data: {
          'Catch 22' => {
            title.key => 'static title',
            genre.key => 'static genre'
          }
        }
      )

      action2 = create(:automation_action, automation: automation, action_type: :ai)
      create(:automation_ai_action, automation_action: action2)
      ai_result = FieldSet::KeyHash.new(
        { attributes: {
          genre.key => 'AI-generated genre',
          description.key => 'AI-generated description',
          author.key => 'AI-generated author'
        } },
        object_type: book
      )
      allow(AiService).to receive(:execute).and_return(ai_result)

      action3 = create(:automation_action, automation: automation, action_type: :integration)
      integration_action = create(:api_request_action)
      create(:automation_integration_action, automation_action: action3,
                                             integration_action: integration_action,
                                             foreign_object_mapping: create(:foreign_object_mapping))
      allow(Integration::Api::RequestActionHandlerService).to receive(:execute).and_return(
        {
          field_set: FieldSet::KeyHash.new(
            { attributes: {
              author.key => 'Integration-generated author'
            } },
            object_type: book
          )
        }
      )

      field_set = FieldSet::JsonPayload.new(
        {
          data: {
            id: 1,
            attributes: {
              values: {
                title.key => 'Catch 22'
              }
            }
          }
        },
        object_type: book
      )
      result = described_class.execute(automation, field_set: field_set)

      expect(result).to be_a(FieldSet::KeyHash)
      expect(result.attribute(title.id)).to eq 'static title'
      expect(result.attribute(genre.id)).to eq 'AI-generated genre'
      expect(result.attribute(description.id)).to eq 'AI-generated description'
      expect(result.attribute(author.id)).to eq 'Integration-generated author'
    end

    context 'when automation action is mail' do
      context 'when field_set is backed by an Object' do
        it 'invokes AutomationMailActionWorker with the Object' do
          automation_action = create(:automation_action, automation: automation, action_type: :mail)
          automation_mail_action = create(:automation_mail_action, automation_action: automation_action, mail_type: :object_creation)
          object = create(:object)
          user = create(:user)

          allow(AutomationMailActionWorker).to receive(:perform_in)

          field_set = FieldSet::Object.new(object)
          change_set = Custom::Object::ChangeSet.new(
            {
              17 => { from: 'old', to: 'new' }
            }
          )
          described_class.execute(automation, field_set: field_set, user: user, change_set: change_set)
          expect(AutomationMailActionWorker).to have_received(:perform_in).with(
            15.minutes,
            {
              'automation_mail_action_id' => automation_mail_action.id,
              'object_id' => object.id,
              'change_set' => change_set.serializable_hash,
              'user_id' => user.id
            }
          )
        end
      end

      context 'when field_set is backed by a JsonPayload' do
        it 'invokes AutomationMailActionWorker with the Object referenced in the JsonPayload' do
          automation_action = create(:automation_action, automation: automation, action_type: :mail)
          automation_mail_action = create(:automation_mail_action, automation_action: automation_action, mail_type: :object_creation)
          object = create(:object)
          user = create(:user)

          allow(AutomationMailActionWorker).to receive(:perform_in)

          field_set = FieldSet::JsonPayload.new({ data: { id: object.id } }, object_type: object.object_type)
          described_class.execute(automation, field_set: field_set, user: user)
          expect(AutomationMailActionWorker).to have_received(:perform_in).with(
            15.minutes,
            {
              'automation_mail_action_id' => automation_mail_action.id,
              'object_id' => object.id,
              'change_set' => nil,
              'user_id' => user.id
            }
          )
        end
      end
    end

    context 'when automation action is static_data' do
      let(:automation_action) { create(:automation_action, automation: automation, action_type: :static_data) }
      let(:automation_static_data_action) do
        create(:automation_static_data_action, automation_action: automation_action)
      end

      it 'invokes AutomationStaticDataAction::HandlerService' do
        field_set = FieldSet::JsonPayload.new({ data: { id: 1 } }, object_type: create(:object_type))
        automation_static_data_action = create(:automation_static_data_action, automation_action: automation_action)

        allow(AutomationStaticDataAction::HandlerService).to receive(:execute).and_return(
          AutomationActionResult.new(
            field_set: FieldSet::KeyHash.new(
              { attributes: { 'key' => 'value' } },
              object_type: field_set.object_type
            )
          )
        )

        result = described_class.execute(automation, field_set: field_set)
        expect(AutomationStaticDataAction::HandlerService).to have_received(:execute).with(
          automation_static_data_action, field_set: field_set
        )
        expect(result.resource).to eq({ 'attributes' => { 'key' => 'value' } })
      end
    end

    context 'when automation action is integration' do
      let(:automation_action) { create(:automation_action, automation: automation, action_type: :integration) }

      it 'invokes AutomationIntegrationAction::HandlerService' do
        field_set = FieldSet::JsonPayload.new({ data: { id: 1 } }, object_type: create(:object_type))
        change_set = Custom::Object::ChangeSet.new
        user = create(:user)
        automation_integration_action = create(:automation_integration_action, automation_action: automation_action)

        allow(AutomationIntegrationAction::HandlerService).to receive(:execute).and_return(
          AutomationActionResult.new(
            field_set: FieldSet::KeyHash.new(
              { attributes: { 'result' => 'result' } },
              object_type: field_set.object_type
            )
          )
        )

        result = described_class.execute(automation, field_set: field_set, change_set: change_set, user: user)
        expect(AutomationIntegrationAction::HandlerService).to have_received(:execute).with(
          automation_integration_action, field_set: field_set, change_set: change_set, filter: nil, job_id: nil, user: user
        )
        expect(result.resource).to eq({ 'attributes' => { 'result' => 'result' } })
      end
    end

    context 'when automation action is ai' do
      let(:automation_action) { create(:automation_action, automation: automation, action_type: :ai) }

      it 'invokes AutomationAiAction::HandlerService' do
        field_set = FieldSet::JsonPayload.new({ data: { id: 1 } }, object_type: create(:object_type))
        automation_ai_action = create(:automation_ai_action, automation_action: automation_action)
        allow(AutomationAiAction::HandlerService).to receive(:execute).and_return(
          AutomationActionResult.new(
            field_set: FieldSet::KeyHash.new(
              { attributes: { 'key' => 'value' } },
              object_type: field_set.object_type
            )
          )
        )

        result = described_class.execute(automation, field_set: field_set)
        expect(AutomationAiAction::HandlerService).to have_received(:execute).with(
          automation_ai_action, field_set, nil, nil
        )
        expect(result.resource).to eq({ 'attributes' => { 'key' => 'value' } })
      end
    end

    context 'when automation action is update_object' do
      let(:automation_action) { create(:automation_action, automation: automation, action_type: :update_object) }

      it 'invokes AutomationUpdateObjectAction::HandlerService' do
        object = create(:object)
        user = create(:user)
        field_set = FieldSet::Object.new(object)
        update_object_action = create(:automation_update_object_action, automation_action: automation_action)

        result = AutomationActionResult.new(
          field_set: FieldSet::KeyHash.new(
            { attributes: { 'key' => 'value' } },
            object_type: object.object_type
          )
        )
        allow(AutomationUpdateObjectAction::HandlerService).to receive(:execute).and_return(result)

        result = described_class.execute(automation, field_set: field_set, user: user)
        expect(AutomationUpdateObjectAction::HandlerService).to have_received(:execute).with(
          update_object_action, object: object, user: user
        )
        expect(result.resource).to eq({ 'attributes' => { 'key' => 'value' } })
      end
    end

    context 'when automation action is webhook' do
      let(:automation_action) { create(:automation_action, automation: automation, action_type: :webhook) }

      it 'schedules the webhook automation action' do
        object = create(:object)
        user = create(:user)
        trigger = create(:automation_trigger, automation: automation_action.automation, trigger_type: :update)
        field_set = FieldSet::Object.new(object)
        webhook_action = create(:automation_webhook_action, automation_action: automation_action)

        allow(AutomationWebhookActionWorker).to receive(:schedule_for).and_return(nil)

        described_class.execute(automation, field_set: field_set, user: user)
        expect(AutomationWebhookActionWorker).to have_received(:schedule_for).with(
          webhook_action, object, trigger.trigger_type, user, object.updated_at
        )
      end
    end
  end
end
