require 'rails_helper'

RSpec.describe AutomationAction::CreateService do
  describe '#execute' do
    it 'creates an automation action with a mail action' do
      automation = create(:automation)
      automation_mail_action_user = create(:user)
      object_type_role = create(:object_type_role, :author, name: 'Bossman')
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              action_type: 'mail'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id,
                  type: 'automation'
                }
              },
              automation_mail_action: {
                data: {
                  __guid__: 'dcba',
                  type: 'automation_mail_action'
                }
              }
            }
          },
          included: [
            {
              __guid__: 'dcba',
              type: 'automation_mail_action',
              attributes: {
                mail_type: 'object_creation'
              },
              relationships: {
                selected_users: {
                  data: [
                    { type: 'user', id: automation_mail_action_user.id.to_s }
                  ]
                },
                selected_roles: {
                  data: [
                    { type: 'object_type_role', id: object_type_role.id.to_s }
                  ]
                },
                automation_mail_action_attributes: {
                  data: [
                    { type: 'automation_mail_action_attribute', __guid__: 'dca' }
                  ]
                }
              }
            },
            {
              type: 'automation_mail_action_attribute',
              __guid__: 'dca',
              attributes: {
                column_name: 'created_at',
                position: '0'
              }
            }
          ]
        }
      )

      automation_action = described_class.execute(params)
      expect(automation_action.action_type).to eq 'mail'
      expect(automation_action.automation_mail_action).to be_present
      expect(automation_action.automation_mail_action.automation_mail_action_user_relationships).not_to be_empty
      expect(automation_action.automation_mail_action.automation_mail_action_role_relationships).not_to be_empty
      expect(automation_action.automation_mail_action.automation_mail_action_attributes).not_to be_empty
    end

    it 'creates an automation action with a static action' do
      automation = create(:automation)
      object_type_attribute = create(:object_type_attribute)
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              action_type: 'static_data'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id,
                  type: 'automation'
                }
              },
              automation_static_data_action: {
                data: {
                  __guid__: 'dcba',
                  type: 'automation_static_data_action'
                }
              }
            }
          },
          included: [
            {
              __guid__: 'dcba',
              type: 'automation_static_data_action',
              relationships: {
                object_type_attribute: {
                  data: {
                    id: object_type_attribute.id,
                    type: 'object_type_attribute'
                  }
                }
              }
            }
          ]
        }
      )

      automation_action = described_class.execute(params)
      expect(automation_action.action_type).to eq 'static_data'
      expect(automation_action.automation_static_data_action).to be_present
    end

    it 'creates an automation action with an update object action' do
      automation = create(:automation)
      attr1 = create(:object_type_attribute, object_type: automation.object_type, data_type: 'String')
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              action_type: 'update_object'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id,
                  type: 'automation'
                }
              },
              automation_update_object_action: {
                data: {
                  __guid__: 'dcba',
                  type: 'automation_update_object_action'
                }
              }
            }
          },
          included: [
            {
              __guid__: 'dcba',
              type: 'automation_update_object_action',
              relationships: {
                automation_update_object_action_attributes: {
                  data: [
                    { type: 'automation_update_object_action_attribute', __guid__: 'attr1' }
                  ]
                }
              }
            },
            {
              type: 'automation_update_object_action_attribute',
              __guid__: 'attr1',
              attributes: {
                value: 'abc'
              },
              relationships: {
                object_type_attribute: {
                  data: { type: 'object_type_attribute', id: attr1.id.to_s }
                }
              }
            }
          ]
        }
      )

      automation_action = described_class.execute(params)
      expect(automation_action.action_type).to eq 'update_object'
      expect(automation_action.automation_update_object_action).to be_present
      expect(automation_action.automation_update_object_action.automation_update_object_action_attributes).not_to be_empty
    end

    it 'creates an automation action with an webhook action' do
      automation = create(:automation)
      attr1 = create(:object_type_attribute, object_type: automation.object_type, data_type: 'String')
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              action_type: 'webhook'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id.to_s,
                  type: 'automation'
                }
              },
              automation_webhook_action: {
                data: {
                  __guid__: 'dcba',
                  type: 'automation_webhook_action'
                }
              }
            }
          },
          included: [
            {
              __guid__: 'dcba',
              type: 'automation_webhook_action',
              attributes: {
                url: 'https://www.example.local'
              },
              relationships: {
                automation_webhook_action_attributes: {
                  data: [
                    { type: 'automation_webhook_action_attribute', __guid__: 'attr1' },
                    { type: 'automation_webhook_action_attribute', __guid__: 'prop1' }
                  ]
                }
              }
            },
            {
              type: 'automation_webhook_action_attribute',
              __guid__: 'attr1',
              relationships: {
                object_type_attribute: {
                  data: { type: 'object_type_attribute', id: attr1.id.to_s }
                }
              }
            },
            {
              type: 'automation_webhook_action_attribute',
              __guid__: 'attr1',
              attributes: {
                column_name: 'id'
              }
            }
          ]
        }
      )

      automation_action = described_class.execute(params)
      expect(automation_action.action_type).to eq 'webhook'
      expect(automation_action.automation_webhook_action).to be_present
      expect(automation_action.automation_webhook_action.automation_webhook_action_attributes).not_to be_empty
    end
  end
end
