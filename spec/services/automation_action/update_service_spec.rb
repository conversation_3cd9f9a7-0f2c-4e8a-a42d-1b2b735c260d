require 'rails_helper'

RSpec.describe AutomationAction::UpdateService do
  describe '#execute' do
    it 'updates an automation action with a mail action' do
      automation_action = create(:automation_action, action_type: :mail)
      automation_mail_action = create(:automation_mail_action, automation_action: automation_action, mail_type: 0)
      automation = automation_action.automation
      automation_mail_action_user = create(:user)
      object_type_role = create(:object_type_role, :author, name: '<PERSON><PERSON>')

      params = ActionController::Parameters.new(
        {
          data: {
            id: automation_action.id,
            attributes: {
              action_type: 'mail'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id,
                  type: 'automation'
                }
              },
              automation_mail_action: {
                data: {
                  id: automation_mail_action.id.to_s,
                  type: 'automation_mail_action'
                }
              }
            }
          },
          included: [
            {
              id: automation_mail_action.id.to_s,
              type: 'automation_mail_action',
              attributes: {
                to_listed_users: true,
                mail_type: 'object_update',
                settings: {
                  value: 30
                }
              },
              relationships: {
                selected_users: {
                  data: [
                    { id: automation_mail_action_user.id.to_s, type: 'user' }
                  ]
                },
                selected_roles: {
                  data: [
                    { id: object_type_role.id.to_s, type: 'object_type_role' }
                  ]
                },
                automation_mail_action_attributes: {
                  data: [
                    { __guid__: 'dca', type: 'automation_mail_action_attribute' }
                  ]
                }
              }
            },
            {
              type: 'automation_mail_action_attribute',
              __guid__: 'dca',
              attributes: {
                column_name: 'created_at',
                position: '0'
              }
            }
          ]
        }
      )

      described_class.execute(automation_action, payload: params)

      automation_action.reload
      expect(automation_mail_action.mail_type).to eq 'object_update'
      expect(automation_mail_action.to_listed_users).to be true
      expect(automation_mail_action.automation_mail_action_user_relationships).not_to be_empty
      expect(automation_mail_action.automation_mail_action_role_relationships).not_to be_empty
      expect(automation_mail_action.automation_mail_action_attributes).not_to be_empty
    end

    it 'updates an automation action with an update object action' do
      automation = create(:automation)
      automation_action = create(:automation_action, automation: automation, action_type: :update_object)
      update_object_action = create(:automation_update_object_action, automation_action: automation_action)
      attribute = create(:object_type_attribute, object_type: automation.object_type, data_type: 'Number')
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              action_type: 'update_object'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id.to_s,
                  type: 'automation'
                }
              },
              automation_update_object_action: {
                data: {
                  id: update_object_action.id.to_s,
                  type: 'automation_update_object_action'
                }
              }
            }
          },
          included: [
            {
              id: update_object_action.id.to_s,
              type: 'automation_update_object_action',
              relationships: {
                automation_update_object_action_attributes: {
                  data: [
                    { type: 'automation_update_object_action_attribute', __guid__: 'attr1' }
                  ]
                }
              }
            },
            {
              type: 'automation_update_object_action_attribute',
              __guid__: 'attr1',
              attributes: {
                value: 223
              },
              relationships: {
                object_type_attribute: {
                  data: { type: 'object_type_attribute', id: attribute.id.to_s }
                }
              }
            }
          ]
        }
      )

      described_class.execute(automation_action, payload: params)

      automation_action.reload
      expect(automation_action.automation_update_object_action).to be_present
      expect(automation_action.automation_update_object_action.automation_update_object_action_attributes).not_to be_empty
      expect(automation_action.automation_update_object_action.automation_update_object_action_attributes.first).to have_attributes(
        object_type_attribute_id: attribute.id, value: 223
      )
    end

    it 'updates an automation action with an webhook action' do
      automation = create(:automation)
      automation_action = create(:automation_action, automation: automation, action_type: :webhook)
      webhook_action = create(:automation_webhook_action, automation_action: automation_action)
      attribute = create(:object_type_attribute, object_type: automation.object_type, data_type: 'Number')
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              action_type: 'webhook'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id.to_s,
                  type: 'automation'
                }
              },
              automation_webhook_action: {
                data: {
                  id: webhook_action.id.to_s,
                  type: 'automation_webhook_action'
                }
              }
            }
          },
          included: [
            {
              id: webhook_action.id.to_s,
              type: 'automation_webhook_action',
              attributes: {
                url: 'https://my.link'
              },
              relationships: {
                automation_webhook_action_attributes: {
                  data: [
                    { type: 'automation_webhook_action_attribute', __guid__: 'attr1' },
                    { type: 'automation_webhook_action_attribute', __guid__: 'attr2' }
                  ]
                }
              }
            },
            {
              type: 'automation_webhook_action_attribute',
              __guid__: 'attr1',
              relationships: {
                object_type_attribute: {
                  data: { type: 'object_type_attribute', id: attribute.id.to_s }
                }
              }
            },
            {
              type: 'automation_webhook_action_attribute',
              __guid__: 'attr2',
              attributes: {
                column_name: 'team.name'
              },
              relationships: {
                object_type_attribute: {
                  data: nil
                }
              }
            }
          ]
        }
      )

      described_class.execute(automation_action, payload: params)

      automation_action.reload
      expect(automation_action.automation_webhook_action).to be_present
      expect(automation_action.automation_webhook_action.automation_webhook_action_attributes.count).to eq 2
      expect(automation_action.automation_webhook_action.automation_webhook_action_attributes.first).to have_attributes(
        object_type_attribute_id: attribute.id
      )
      expect(automation_action.automation_webhook_action.automation_webhook_action_attributes.second).to have_attributes(
        column_name: 'team.name'
      )
    end

    it 'updates an automation action with a static action' do
      automation_action = create(:automation_action, action_type: :static_data)
      automation_static_data_action = create(:automation_static_data_action, automation_action: automation_action,
                                                                             data: { double: 'D' })
      automation = automation_action.automation
      params = ActionController::Parameters.new(
        {
          data: {
            id: automation_action.id,
            attributes: {
              action_type: 'static_data'
            },
            relationships: {
              automation: {
                data: {
                  id: automation.id,
                  type: 'automation'
                }
              },
              automation_static_data_action: {
                data: {
                  id: automation_static_data_action.id.to_s,
                  type: 'automation_static_data_action'
                }
              }
            }
          },
          included: [
            {
              id: automation_static_data_action.id.to_s,
              type: 'automation_static_data_action',
              attributes: {
                data: {
                  double: 'G'
                }
              }
            }
          ]
        }
      )

      automation_action = described_class.execute(automation_action, payload: params)

      expect(automation_action.automation_static_data_action.data['double']).to eq 'G'
    end
  end
end
