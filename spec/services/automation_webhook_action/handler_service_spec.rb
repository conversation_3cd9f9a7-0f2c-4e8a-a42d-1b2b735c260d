require 'rails_helper'

RSpec.describe AutomationWebhookAction::HandlerService do
  let(:object) { create(:object) }
  let(:webhook_action) { create(:automation_webhook_action) }

  describe '#execute' do
    it 'does nothing when no object is passed into it' do
      stubbed_request = stub_request(:post, webhook_action.url)
      described_class.execute(webhook_action, object: nil, event: nil, user: nil, timestamp: Time.zone.now.iso8601)

      assert_not_requested(stubbed_request)
    end

    it 'sends a request with attributes as part of the payload' do
      timestamp = Time.zone.now.iso8601
      ota1 = create(:object_type_attribute, field_identifier: 'name', object_type: object.object_type, data_type: 'String')
      ota2 = create(:object_type_attribute, field_identifier: 'open', object_type: object.object_type, data_type: 'Boolean')
      ota3 = create(:object_type_attribute, field_identifier: 'blank', object_type: object.object_type, data_type: 'String')
      create(:object_attribute, object_type_attribute: ota1, object: object, value: 'Alfred')
      create(:object_attribute, object_type_attribute: ota2, object: object, value: false)

      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota1)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota2)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota3)
      user = create(:user)

      stubbed_request = stub_request(:post, webhook_action.url).with(
        body: {
          type: 'record.create',
          timestamp: timestamp,
          data: {
            id: object.id.to_s,
            object_type_id: object.object_type_id.to_s,
            values: {
              name: 'Alfred',
              open: false,
              blank: nil
            },
            user: { id: user.id, name: user.name }
          }
        }
      )

      described_class.execute(webhook_action, object: object, event: :create, timestamp: timestamp, user: user)

      assert_requested(stubbed_request)
    end

    it 'sends a request with single user relationships as part of the payload' do
      timestamp = Time.zone.now.iso8601
      ota1 = create(:object_type_attribute, :user_relationship, field_identifier: 'owner', object_type: object.object_type,
                                                                relationship_kind: 'single')
      ota2 = create(:object_type_attribute, :user_relationship, field_identifier: 'user', object_type: object.object_type,
                                                                relationship_kind: 'single')
      owner = create(:user)
      create(:object_relationship, object_type_attribute: ota1, object: object, model: owner)

      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota1)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota2)

      trigger_user = create(:user)
      stubbed_request = stub_request(:post, webhook_action.url).with(
        body: {
          type: nil,
          timestamp: timestamp,
          data: {
            id: object.id.to_s,
            object_type_id: object.object_type_id.to_s,
            values: {
              owner: { id: owner.id, name: owner.name },
              user: nil
            },
            user: { id: trigger_user.id, name: trigger_user.name }
          }
        }
      )

      described_class.execute(webhook_action, object: object, event: nil, timestamp: timestamp, user: trigger_user)

      assert_requested(stubbed_request)
    end

    it 'sends a request with multiple user relationships as part of the payload' do
      timestamp = Time.zone.now.iso8601
      ota1 = create(:object_type_attribute, :user_relationship, field_identifier: 'owners', object_type: object.object_type,
                                                                relationship_kind: 'multiple')
      ota2 = create(:object_type_attribute, :user_relationship, field_identifier: 'users', object_type: object.object_type,
                                                                relationship_kind: 'multiple')
      ota3 = create(:object_type_attribute, :user_relationship, field_identifier: 'editors', object_type: object.object_type,
                                                                relationship_kind: 'multiple')
      owners = create_list(:user, 2)
      editors = create_list(:user, 1)
      create(:object_relationship, object_type_attribute: ota1, object: object, model: owners[0])
      create(:object_relationship, object_type_attribute: ota1, object: object, model: owners[1])
      create(:object_relationship, object_type_attribute: ota3, object: object, model: editors[0])

      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota1)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota2)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota3)

      stubbed_request = stub_request(:post, webhook_action.url).with(
        body: {
          type: nil,
          timestamp: timestamp,
          data: {
            id: object.id.to_s,
            object_type_id: object.object_type_id.to_s,
            values: {
              owners: owners.map { |owner| { id: owner.id, name: owner.name } },
              users: [],
              editors: editors.map { |editor| { id: editor.id, name: editor.name } }
            },
            user: nil
          }
        }
      )

      described_class.execute(webhook_action, object: object, event: nil, timestamp: timestamp, user: nil)

      assert_requested(stubbed_request)
    end

    it 'sends a request with properties as part of the payload' do
      timestamp = Time.zone.now.iso8601
      object = create(:object, last_updated_by: create(:user), team: create(:team), updated_at: 5.minutes.ago)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'team.name')
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'last_updated_by_id')
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'updated_at')

      stubbed_request = stub_request(:post, webhook_action.url).with(
        body: {
          type: 'record.update',
          timestamp: timestamp,
          data: {
            id: object.id.to_s,
            object_type_id: object.object_type_id.to_s,
            team: object.team.name,
            last_updated_by: { id: object.last_updated_by.id, name: object.last_updated_by.name },
            updated_at: object.updated_at.iso8601,
            values: {},
            user: nil
          }
        }
      )

      described_class.execute(webhook_action, object: object, event: :update, timestamp: timestamp, user: nil)

      assert_requested(stubbed_request)
    end

    it 'sends a request with attributes and properties with the same name as part of the payload' do
      timestamp = Time.zone.now.iso8601
      object.user = create(:user)
      ota1 = create(:object_type_attribute, field_identifier: 'id', object_type: object.object_type, data_type: 'Boolean')
      ota2 = create(:object_type_attribute, field_identifier: 'user_id', object_type: object.object_type, data_type: 'Number')

      create(:object_attribute, object_type_attribute: ota1, object: object, value: true)
      create(:object_attribute, object_type_attribute: ota2, object: object, value: 214)

      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota1)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota2)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'id')
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'user_id')

      stubbed_request = stub_request(:post, webhook_action.url).with(
        body: {
          type: 'record.create',
          timestamp: timestamp,
          data: {
            id: object.id.to_s,
            object_type_id: object.object_type_id.to_s,
            created_by: { id: object.user.id, name: object.user.name },
            values: {
              id: true,
              user_id: 214
            },
            user: nil
          }
        }
      )

      described_class.execute(webhook_action, object: object, event: :create, timestamp: timestamp, user: nil)

      assert_requested(stubbed_request)
    end
  end
end
