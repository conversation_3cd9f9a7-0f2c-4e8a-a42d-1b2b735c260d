require 'rails_helper'

RSpec.describe AutomationWebhookAction::UpdateService do
  describe '#execute' do
    it 'is able to update an webhook automation action and attributes' do
      webhook_action = create(:automation_webhook_action)
      object_type = webhook_action.automation_action.automation.object_type
      ota1, ota2, ota3 = create_list(:object_type_attribute, 3, object_type: object_type, data_type: 'String')
      attribute1 = create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota1)
      attribute2 = create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, object_type_attribute: ota2)

      params = ActionController::Parameters.new(
        {
          id: webhook_action.id.to_s,
          type: 'automation_webhook_action',
          attributes: {
            url: 'https://an.updated.url'
          },
          relationships: {
            automation_webhook_action_attributes: {
              data: [
                { id: attribute1.id.to_s, type: 'automation_webhook_action_attribute' },
                { __guid__: 'attr3', type: 'automation_webhook_action_attribute' }
              ]
            }
          },
          included: [
            {
              id: attribute1.id.to_s,
              type: 'automation_webhook_action_attribute',
              relationships: {
                object_type_attribute: {
                  data: {
                    id: ota3.id.to_s, type: 'object_type_attribute'
                  }
                }
              }
            },
            {
              type: 'automation_webhook_action_attribute',
              __guid__: 'attr3',
              relationships: {
                object_type_attribute: {
                  data: {
                    id: ota1.id.to_s,
                    type: 'object_type_attribute'
                  }
                }
              }
            }
          ]
        }
      )
      webhook_action = described_class.execute(webhook_action, payload: params)

      expect(webhook_action.url).to eq 'https://an.updated.url'
      expect(webhook_action.automation_webhook_action_attributes.length).to eq 2
      expect(webhook_action.automation_webhook_action_attributes.pluck(:object_type_attribute_id)).to(
        contain_exactly(ota3.id, ota1.id)
      )
      expect { attribute2.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'is able to update an webhook automation action attribute to a property' do
      webhook_action = create(:automation_webhook_action)
      object_type = webhook_action.automation_action.automation.object_type
      ota = create(:object_type_attribute, object_type: object_type, data_type: 'String')
      webhook_attribute = create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action,
                                                                       object_type_attribute: ota)

      params = ActionController::Parameters.new(
        {
          id: webhook_action.id.to_s,
          type: 'automation_webhook_action',
          relationships: {
            automation_webhook_action_attributes: {
              data: [
                { id: webhook_attribute.id.to_s, type: 'automation_webhook_action_attribute' }
              ]
            }
          },
          included: [
            {
              id: webhook_attribute.id.to_s,
              type: 'automation_webhook_action_attribute',
              attributes: {
                column_name: 'updated_at'
              },
              relationships: {
                object_type_attribute: {
                  data: nil
                }
              }
            }
          ]
        }
      )
      webhook_action = described_class.execute(webhook_action, payload: params)

      expect(webhook_action.automation_webhook_action_attributes).to contain_exactly webhook_attribute
      webhook_attribute.reload
      expect(webhook_attribute.object_type_attribute).to be_nil
      expect(webhook_attribute.column_name).to eq 'updated_at'
    end

    it 'is able to update an webhook automation action without clearing its attributes' do
      webhook_action = create(:automation_webhook_action)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action)

      params = ActionController::Parameters.new(
        {
          id: webhook_action.id.to_s,
          type: 'automation_webhook_action',
          attributes: {
            url: 'https://my-special-link.me'
          }
        }
      )
      webhook_action = described_class.execute(webhook_action, payload: params)

      expect(webhook_action.automation_webhook_action_attributes.count).to eq 1
      expect(webhook_action.url).to eq 'https://my-special-link.me'
    end

    it 'is able to clear attributes of an webhook automation action' do
      webhook_action = create(:automation_webhook_action)
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'id')
      create(:automation_webhook_action_attribute, automation_webhook_action: webhook_action, column_name: 'created_at')

      params = ActionController::Parameters.new(
        {
          id: webhook_action.id.to_s,
          type: 'automation_webhook_action',
          relationships: {
            automation_webhook_action_attributes: {
              data: nil
            }
          }
        }
      )
      webhook_action = described_class.execute(webhook_action, payload: params)

      expect(webhook_action.automation_webhook_action_attributes.count).to eq 0
    end
  end
end
