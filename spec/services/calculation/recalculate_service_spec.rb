require 'rails_helper'

RSpec.describe Calculation::RecalculateService do
  it 'recalculates values for all objects of the object type' do
    object_type = create(:object_type)
    attribute_a = create(:object_type_attribute, object_type: object_type, data_type: 'Number')
    attribute_b = create(:object_type_attribute, object_type: object_type, data_type: 'Number', calculated: true)

    object1 = create(:object, object_type: object_type)
    object2 = create(:object, object_type: object_type)

    calculation = create(:calculation, :formula, formula: 'A * 2', object_type_attribute: attribute_b)

    create(:object_attribute, object_type_attribute: attribute_a, object: object1, value: 5)
    create(:object_attribute, object_type_attribute: attribute_a, object: object2, value: 10)
    create(:object_type_attribute_path, calculation: calculation, data_attribute: attribute_a, key: 'A', relationship_references: [])

    expect(object1.value_for(attribute_b.id)).to be_nil
    expect(object2.value_for(attribute_b.id)).to be_nil

    described_class.execute(calculation)

    expect(object1.reload.value_for(attribute_b.id)).to eq(10)
    expect(object2.reload.value_for(attribute_b.id)).to eq(20)
  end

  it 'does not recalculate values for type lookup calculations' do
    object_type = create(:object_type)
    attribute_a = create(:object_type_attribute, object_type: object_type, data_type: 'Number', calculated: true)
    calculation = create(:calculation, :lookup, object_type_attribute: attribute_a)

    allow(Calculation::FormulaEvaluateService).to receive(:execute)

    described_class.execute(calculation)

    expect(Calculation::FormulaEvaluateService).not_to have_received(:execute)
  end

  it 'continues processing other objects if one fails with an expected error' do
    object_type = create(:object_type)
    attribute_a = create(:object_type_attribute, object_type: object_type, data_type: 'Number')
    attribute_b = create(:object_type_attribute, object_type: object_type, data_type: 'Number', calculated: true)

    object1 = create(:object, object_type: object_type)
    object2 = create(:object, object_type: object_type)

    calculation = create(:calculation, :formula, formula: 'A * 2', object_type_attribute: attribute_b)

    create(:object_attribute, object_type_attribute: attribute_a, object: object1)
    create(:object_attribute, object_type_attribute: attribute_a, object: object2, value: 10)
    create(:object_type_attribute_path, calculation: calculation, data_attribute: attribute_a, key: 'A', relationship_references: [])

    expect(object1.value_for(attribute_b.id)).to be_nil
    expect(object2.value_for(attribute_b.id)).to be_nil

    described_class.execute(calculation)

    expect(object1.reload.value_for(attribute_b.id)).to be_nil
    expect(object2.reload.value_for(attribute_b.id)).to eq(20)
  end

  it 'recalculates the chain of calculations' do
    book = create(:object_type, name: 'Book')

    # vat = 21% * fee
    # price = fee + vat
    fee = create(:object_type_attribute, name: 'fee', object_type: book, data_type: 'Number')
    vat = create(:object_type_attribute, name: 'vat', object_type: book, data_type: 'Number', calculated: true)
    price = create(:object_type_attribute, name: 'price', object_type: book, data_type: 'Number', calculated: true)

    calculation_vat = create(:calculation, :formula, formula: '21 * A', object_type_attribute: vat)
    calculation_price = create(:calculation, :formula, formula: 'A + B', object_type_attribute: price)

    create(:object_type_attribute_path, calculation: calculation_vat, key: 'A', data_attribute: fee)
    create(:object_type_attribute_path, calculation: calculation_price, key: 'A', data_attribute: fee)
    create(:object_type_attribute_path, calculation: calculation_price, key: 'B', data_attribute: vat)

    publisher = create(:object_type, name: 'Publisher')

    # Each book has a publisher
    book_publisher = create(:object_type_attribute, :object_relationship, object_type: book,
                                                                          target_object_type: publisher,
                                                                          relationship_kind: 'single',
                                                                          inverse_relationship_kind: 'multiple',
                                                                          name: 'publisher')

    # Each publisher has multiple books
    publisher_books = create(:object_type_attribute, :object_relationship, object_type: publisher,
                                                                           target_object_type: book,
                                                                           relationship_kind: 'multiple',
                                                                           inverse_relationship_kind: 'single',
                                                                           name: 'books',
                                                                           inverse: true)

    create(:object_type_relationship, object_type_attribute: book_publisher, inverse_object_type_attribute: publisher_books)

    gross_income = create(:object_type_attribute, name: 'gross_income', object_type: publisher, data_type: 'Number', calculated: true)
    calculation_gross_income = create(:calculation, :formula, formula: 'A', object_type_attribute: gross_income)
    create(:object_type_attribute_path, calculation: calculation_gross_income,
                                        key: 'A',
                                        mapping: {},
                                        data_attribute: price,
                                        relationship_references: [{ attribute_id: book_publisher.id, inverse: true }],
                                        aggregation: 'sum')

    book1 = create(:object, object_type: book)
    create(:object_attribute, object_type_attribute: fee, object: book1, value: 100)

    book2 = create(:object, object_type: book)
    create(:object_attribute, object_type_attribute: fee, object: book2, value: 200)

    publisher1 = create(:object, object_type: publisher)
    create(:object_relationship, object_type_attribute: book_publisher, object: book1, model: publisher1)
    create(:object_relationship, object_type_attribute: book_publisher, object: book2, model: publisher1)

    calculation_vat.update(formula: '0.21 * A')
    described_class.execute(calculation_vat)

    expect(book1.value_for(vat.id)).to eq(21)
    expect(book2.value_for(vat.id)).to eq(42)

    expect(book1.value_for(price.id)).to eq(121)
    expect(book2.value_for(price.id)).to eq(242)

    # The income of the publisher should have been calculated automatically
    publisher1.reload
    expect(publisher1.value_for(gross_income.id)).to eq(363)

    # Now we add a new attribute to the book and update the vat calculation to utilize it
    attr = create(:object_type_attribute, name: 'attr', object_type: book, data_type: 'Number')
    calculation_vat.object_type_attribute_paths.clear
    create(:object_type_attribute_path, calculation: calculation_vat, key: 'A', data_attribute: attr)

    # Since none of the records has a value for that attribute we expect the recalculation to clear all previously calculated values,
    # only for one level
    described_class.execute(calculation_vat)

    book1.reload
    book2.reload
    publisher1.reload

    # The first level is updated
    expect(book1.value_for(vat.id)).to be_nil
    expect(book2.value_for(vat.id)).to be_nil

    # The second level is not updated
    expect(book1.value_for(price.id)).to eq(121)
    expect(book2.value_for(price.id)).to eq(242)

    # The thrid level is not updated, too
    expect(publisher1.value_for(gross_income.id)).to eq(363)
  end
end
