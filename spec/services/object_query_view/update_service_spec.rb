require 'rails_helper'

RSpec.describe ObjectQueryView::UpdateService do
  it 'updates the object query rules' do
    object_query_view = create(:object_query_view, dependency: create(:view))
    ota = create(:object_type_attribute, object_type: object_query_view.dependency.object_type)
    existing_query_rule = create(:object_query_rule, object_query_view: object_query_view, rule_type: 'filter', column_name: 'id',
                                                     operator: 'equals', position: 1, value: '1')
    params = ActionController::Parameters.new(
      {
        data: {
          type: 'object_query_view',
          id: object_query_view.id.to_s,
          relationships: {
            object_query_rules: {
              data: [
                {
                  type: 'object_query_rule',
                  id: existing_query_rule.id.to_s
                },
                {
                  type: 'object_query_rule',
                  __guid__: 'abc'
                },
                {
                  type: 'object_query_rule',
                  __guid__: 'cba'
                }
              ]
            }
          }
        },
        included: [
          {
            type: 'object_query_rule',
            id: existing_query_rule.id.to_s,
            attributes: {
              rule_type: 'filter',
              column_name: 'team_id',
              contains: 'equals',
              value: '3',
              position: 1
            }
          },
          {
            type: 'object_query_rule',
            __guid__: 'abc',
            attributes: {
              rule_type: 'filter',
              value: 'value',
              operator: 'contains',
              object_type_attribute_id: ota.id.to_s,
              position: 0
            }
          },
          {
            type: 'object_query_rule',
            __guid__: 'cba',
            attributes: {
              rule_type: 'sort',
              column_name: 'created_at',
              sort_order: 'asc',
              position: 0
            }
          }
        ]
      }
    )
    object_query_view = described_class.execute(object_query_view, params: params)
    expect(object_query_view.object_query_rules.length).to be 3
    existing_query_rule.reload
    expect(existing_query_rule).to have_attributes(rule_type: 'filter', value: '3', column_name: 'team_id', operator: 'equals')

    filter_rule = object_query_view.object_query_rules.second
    expect(filter_rule).to have_attributes(rule_type: 'filter', value: 'value', operator: 'contains')
    expect(filter_rule.object_type_attribute).to eq ota

    sort_rule = object_query_view.object_query_rules.third
    expect(sort_rule).to have_attributes(rule_type: 'sort', sort_order: 'asc', column_name: 'created_at')
  end

  it 'removes an object query rule from a query view' do
    object_query_view = create(:object_query_view, dependency: create(:view))
    ota = create(:object_type_attribute, object_type: object_query_view.dependency.object_type)
    create(:object_query_rule, object_query_view: object_query_view, rule_type: 'filter', column_name: 'id', operator: 'equals',
                               position: 0, value: '1')

    rule = create(:object_query_rule, object_query_view: object_query_view, rule_type: 'filter', value: 'value', operator: 'contains',
                                      object_type_attribute_id: ota.id, position: 1)
    params = ActionController::Parameters.new(
      {
        data: {
          type: 'object_query_view',
          id: object_query_view.id.to_s,
          relationships: {
            object_query_rules: {
              data: [
                {
                  type: 'object_query_rule',
                  id: rule.id.to_s
                }
              ]
            }
          }
        },
        included: [
          {
            type: 'object_query_rule',
            id: rule.id.to_s,
            attributes: {
              rule_type: 'filter',
              value: 'other_operator',
              operator: 'equals',
              object_type_attribute_id: ota.id,
              position: 1
            }
          }
        ]
      }
    )
    object_query_view = described_class.execute(object_query_view, params: params)
    expect(object_query_view.object_query_rules.length).to be 1
    existing_rule = object_query_view.object_query_rules.first
    expect(existing_rule.id).to eq rule.id
    expect(existing_rule).to have_attributes(rule_type: 'filter', value: 'other_operator', operator: 'equals')
  end

  it 'is able to add a new object query rule to a view' do
    object_query_view = create(:object_query_view, dependency: create(:view))
    ota = create(:object_type_attribute, object_type: object_query_view.dependency.object_type)

    params = ActionController::Parameters.new(
      {
        data: {
          type: 'object_query_view',
          id: object_query_view.id.to_s,
          relationships: {
            object_query_rules: {
              data: [
                {
                  type: 'object_query_rule',
                  __guid__: 'abc'
                }
              ]
            }
          }
        },
        included: [
          {
            type: 'object_query_rule',
            __guid__: 'abc',
            attributes: {
              rule_type: 'filter',
              value: 'value',
              operator: 'contains',
              object_type_attribute_id: ota.id,
              position: 0
            }
          }
        ]
      }
    )
    object_query_view = described_class.execute(object_query_view, params: params)
    expect(object_query_view.object_query_rules.length).to be 1
    filter_rule = object_query_view.object_query_rules.first
    expect(filter_rule).to have_attributes(rule_type: 'filter', value: 'value', operator: 'contains')
    expect(filter_rule.object_type_attribute).to eq ota
  end

  it 'removes all object query rules for a query view' do
    object_query_view = create(:object_query_view)
    deleted_rule = create(:object_query_rule, object_query_view: object_query_view, rule_type: 'filter', column_name: 'id',
                                              operator: 'equals', position: 0, value: '1')
    params = ActionController::Parameters.new(
      {
        data: {
          type: 'object_query_view',
          id: object_query_view.id.to_s,
          relationships: {
            object_query_rules: {
              data: []
            }
          }
        },
        included: []
      }
    )

    object_query_view = described_class.execute(object_query_view, params: params)
    expect(object_query_view.object_query_rules).to be_empty
    expect { deleted_rule.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  it 'does not remove object query rules when the relationship is missing' do
    object_query_view = create(:object_query_view)
    rule = create(:object_query_rule, object_query_view: object_query_view, rule_type: 'filter', column_name: 'id',
                                      operator: 'equals', position: 0, value: '1')
    params = ActionController::Parameters.new(
      {
        data: {
          type: 'object_query_view',
          id: object_query_view.id.to_s,
          relationships: {}
        },
        included: []
      }
    )

    object_query_view = described_class.execute(object_query_view, params: params)
    expect(object_query_view.object_query_rules).to contain_exactly(rule)
    expect { rule.reload }.not_to raise_error
  end
end
