require 'rails_helper'

RSpec.describe AutomationWebhookActionWorker, type: :worker do
  let(:object_type) { create(:object_type) }
  let(:object) { create(:object, object_type: object_type) }
  let(:automation_webhook_action) { create(:automation_webhook_action) }
  let(:now_iso) { Time.zone.now.iso8601 }

  describe '#perform' do
    it 'invokes AutomationWebhookAction::HandlerService' do
      user = create(:user)
      allow(AutomationWebhookAction::HandlerService).to receive(:execute)
      described_class.new.perform(
        {
          'automation_webhook_action_id' => automation_webhook_action.id,
          'object_id' => object.id,
          'user_id' => user.id,
          'timestamp' => now_iso,
          'event' => 'create'
        }
      )

      expect(AutomationWebhookAction::HandlerService).to have_received(:execute).with(
        automation_webhook_action,
        object: object,
        event: :create,
        user: user,
        timestamp: now_iso
      )
    end

    it 'does nothing when AutomationWebhookAction is not found' do
      allow(AutomationWebhookAction::HandlerService).to receive(:execute)
      described_class.new.perform(
        {
          'automation_webhook_action_id' => 998,
          'object_id' => nil,
          'user_id' => nil,
          'timestamp' => nil,
          'event' => 'create'
        }
      )

      expect(AutomationWebhookAction::HandlerService).not_to have_received(:execute)
    end

    it 'does nothing when object is not found' do
      allow(AutomationWebhookAction::HandlerService).to receive(:execute)
      described_class.new.perform(
        {
          'automation_webhook_action_id' => automation_webhook_action.id,
          'object_id' => 998,
          'user_id' => nil,
          'timestamp' => nil,
          'event' => 'create'
        }
      )

      expect(AutomationWebhookAction::HandlerService).not_to have_received(:execute)
    end

    it 'invokes AutomationWebhookAction::HandlerService without user if no user is found' do
      allow(AutomationWebhookAction::HandlerService).to receive(:execute)
      described_class.new.perform(
        {
          'automation_webhook_action_id' => automation_webhook_action.id,
          'object_id' => object.id,
          'user_id' => '998',
          'timestamp' => now_iso,
          'event' => nil
        }
      )

      expect(AutomationWebhookAction::HandlerService).to have_received(:execute).with(
        automation_webhook_action,
        object: object,
        event: nil,
        user: nil,
        timestamp: now_iso
      )
    end
  end

  describe '.schedule_for' do
    it 'schedules a new worker with an explicit timestamp' do
      allow(described_class).to receive(:perform_async)
      time = 5.minutes.ago
      described_class.schedule_for(automation_webhook_action, object, :update, nil, time)

      expect(described_class).to have_received(:perform_async).with(
        {
          'automation_webhook_action_id' => automation_webhook_action.id,
          'object_id' => object.id,
          'event' => :update,
          'user_id' => nil,
          'timestamp' => time.iso8601
        }
      )
    end

    it 'schedules a new worker with an implicit timestamp' do
      allow(described_class).to receive(:perform_async)
      Timecop.freeze do
        described_class.schedule_for(automation_webhook_action, object, 'update', nil)

        expect(described_class).to have_received(:perform_async).with(
          {
            'automation_webhook_action_id' => automation_webhook_action.id,
            'object_id' => object.id,
            'event' => 'update',
            'user_id' => nil,
            'timestamp' => Time.zone.now.iso8601
          }
        )
      end
    end
  end
end
